import request from '@/axios';

export const getList = ( params) => {
  return request({
    url: '/balde-ad/groupcategory/list',
    method: 'get',
    params: {
      ...params
    }
  })
}
export const getPage = (current, size, params) => {
  return request({
    url: '/balde-ad/groupcategory/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/balde-ad/groupcategory/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/balde-ad/groupcategory/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/balde-ad/groupcategory/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/balde-ad/groupcategory/submit',
    method: 'post',
    data: row
  })
}

