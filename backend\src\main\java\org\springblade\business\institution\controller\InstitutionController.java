/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.business.institution.dto.InstitutionAuditLogDTO;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.institution.entity.Institution;
import org.springblade.business.institution.vo.InstitutionVO;
import org.springblade.business.institution.wrapper.InstitutionWrapper;
import org.springblade.business.institution.service.IInstitutionService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 机构主表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/institution")
@io.swagger.v3.oas.annotations.tags.Tag(name = "机构接口", description = "机构主表接口")
public class InstitutionController extends BladeController {


	private IInstitutionService institutionService;

	/**
	 * 详情机构基本信息
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入institution")
	public R<InstitutionVO> detail(Institution institution) {
		Institution detail = institutionService.getOne(Condition.getQueryWrapper(institution));
		return R.data(InstitutionWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 机构主表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入institution")
	public R<IPage<InstitutionVO>> list(Institution institution, Query query) {
		IPage<InstitutionVO> pages = institutionService.InstitutionPage(institution,query);
		return R.data(pages);
	}


	/**
	 * 自定义分页 机构主表  并且返回 关联的帖子数量
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入institution")
	public R<IPage<InstitutionVO>> page(InstitutionVO institutionVo, Query query) {
		IPage<InstitutionVO> pages = institutionService.selectInstitutionPage(Condition.getPage(query), institutionVo);
		return R.data(pages);
	}

	/**
	 * 新增 机构主表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入institution")
	public R save(@Valid @RequestBody Institution institution) {
		return R.status(institutionService.save(institution));
	}

	/**
	 * 修改 机构主表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入institution")
	public R update(@Valid @RequestBody Institution institution) {
		return R.status(institutionService.updateById(institution));
	}

	/**
	 * 新增或修改 机构主表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入institution")
	public R submit(@Valid @RequestBody Institution institution) {
		return R.status(institutionService.saveOrUpdate(institution));
	}


	/**
	 * 删除 机构主表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(institutionService.deleteLogic(Func.toLongList(ids)));
	}

	/**
	 * 审核机构并且记录审核记录
	 */
	@PostMapping("/audit")
	@ApiOperationSupport(order = 8)
	@Operation(summary = "审核", description = "传入ids")
	public R audit(@RequestBody InstitutionAuditLogDTO institutionAuditLogDTO) {
		boolean result = institutionService.auditInstitution(institutionAuditLogDTO);
		return R.status(result);
	}


	/**
	 * 启动和禁用 机构主表
	 */
	@PostMapping("/openOrClose")
	@ApiOperationSupport(order = 9)
	@Operation(summary = "启动会或者禁用", description = "传入ids")
	public R openOrClose(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		boolean result = institutionService.openOrClose(ids);
		return R.status(result);
	}



}
