<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.config.mapper.NoticeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="noticeResultMap" type="org.springblade.business.config.entity.Notice">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="tag" property="tag"/>
    </resultMap>


    <select id="selectNoticePage" resultMap="noticeResultMap">
        select * from urb_notice where is_deleted = 0
    </select>

</mapper>
