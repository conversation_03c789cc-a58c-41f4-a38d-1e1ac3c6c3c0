<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.points.mapper.SigninRecordMapper">
    <insert id="insertWinRecord">
        insert into urb_lottery_winning_record (user_id,prize_rate,participants) values(#{winRecord.user_id},#{winRecord.prize_rate},#{winRecord.participants})
    </insert>

    <select id="selectByDate" resultType="org.springblade.business.points.entity.SigninRecord">
        select * from urb_signin_record where user_id = #{userId} and signin_date = #{signinDate}
    </select>
    <select id="selectLastSignin" resultType="org.springblade.business.points.entity.SigninRecord">
        select * from urb_signin_record where user_id = #{userId} order by signin_time desc limit 1
    </select>
    <select id="selectByMonth" resultType="org.springblade.business.points.entity.SigninRecord">
        select * from urb_signin_record where user_id = #{userId} and year = #{year} and month = #{month}
        order by signin_time asc
    </select>
    <select id="selectSigninRecordPage" resultType="org.springblade.business.points.vo.SigninRecordVO">
        select * from urb_signin_record where user_id = #{signinRecord.userId}
        <if test="signinRecord.signinDate != null">
            and signin_date = #{signinRecord.signinDate}
        </if>
        <if test="signinRecord.signinType != null">
            and signin_type = #{signinRecord.signinType}
        </if>
        <if test="signinRecord.status != null">
            and status = #{signinRecord.status}
        </if>
        order by signin_time asc
    </select>
    <select id="getContinuousReward" resultType="java.lang.Integer">
        select reward_points from urb_signin_reward_config where days = #{days}
    </select>
    <select id="countMonthSigninDays" resultType="java.lang.Integer">
        select count(*) from urb_signin_record where user_id = #{userId} and year = #{year} and month = #{month}
    </select>
    <select id="getAllContinuousReward" resultType="java.util.Map">
        select days, reward_points from urb_signin_reward_config
        order by sort_order asc
    </select>
    <select id="selectByDateRange" resultType="java.util.Map">
        select user_id,count(1) as signin_count from urb_signin_record where signin_date &gt;=  #{startDate} and signin_date &lt;= #{endDate}
        group by user_id
    </select>

    <!-- 分页查询用户签到记录 -->
    <select id="selectUserRecords" resultType="org.springblade.business.points.entity.SigninRecord">
        select * from urb_signin_record
        where user_id = #{userId}
        <if test="startDate != null and startDate != ''">
            and signin_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and signin_date &lt;= #{endDate}
        </if>
        order by signin_date desc, signin_time desc
    </select>

    <!-- 统计用户签到记录总数 -->
    <select id="countUserRecords" resultType="java.lang.Integer">
        select count(*) from urb_signin_record
        where user_id = #{userId}
        <if test="startDate != null and startDate != ''">
            and signin_date &gt;= #{startDate}
        </if>
        <if test="endDate != null and endDate != ''">
            and signin_date &lt;= #{endDate}
        </if>
    </select>

    <!-- 获取用户签到统计汇总 -->
    <select id="selectUserSummary" resultType="java.util.Map">
        select
            count(*) as totalDays,
            sum(points) as totalBasePoints,
            sum(continuous_reward) as totalContinuousReward,
            sum(points + ifnull(continuous_reward, 0)) as totalPoints,
            max(continuous_days) as maxContinuousDays,
            min(signin_date) as firstSigninDate,
            max(signin_date) as lastSigninDate
        from urb_signin_record
        where user_id = #{userId}
    </select>

    <!-- 统计用户总签到天数 -->
    <select id="countSigninDays" resultType="java.lang.Integer">
        select count(*) from urb_signin_record where user_id = #{userId}
    </select>
</mapper>
