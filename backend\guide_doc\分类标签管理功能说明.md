# 分类标签管理功能说明

## 功能概述

本功能为帖子分类管理提供了完整的标签管理能力，支持分类图标上传、标签创建、标签关联、标签排序等功能。

## 数据库设计

### 1. 分类表增强 (urb_category)

新增字段：
- `icon` - 分类图标URL
- `description` - 分类描述
- `sort` - 排序字段
- `enabled` - 是否启用
- `enable_audit` - 是否启用审核
- `tip` - 提示信息

### 2. 分类标签关联表 (urb_category_tag)

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| category_id | bigint | 分类ID |
| tag_id | bigint | 标签ID |
| sort_order | int | 排序 |
| create_time | datetime | 创建时间 |
| update_time | datetime | 更新时间 |

### 3. 标签表增强 (urb_tag)

新增字段：
- `category_id` - 所属分类ID
- `color` - 标签颜色
- `sort` - 排序
- `enabled` - 是否启用
- `use_count` - 使用次数
- `is_system` - 是否系统标签

## 后端功能

### 1. 分类管理

#### 实体类
- `Category.java` - 分类实体类
- `CategoryTag.java` - 分类标签关联实体类

#### 控制器
- `CategoryController.java` - 分类管理控制器

主要接口：
- `GET /admin/category/list` - 分页查询分类
- `GET /admin/category/enabled` - 获取启用分类
- `GET /admin/category/tree` - 获取分类树
- `GET /admin/category/detail/{id}` - 获取分类详情
- `POST /admin/category/submit` - 新增/更新分类
- `POST /admin/category/remove` - 删除分类
- `POST /admin/category/enable` - 启用/禁用分类
- `POST /admin/category/enable-audit` - 启用/禁用审核

#### 标签管理接口
- `GET /admin/category/{categoryId}/tags` - 获取分类下的标签
- `POST /admin/category/{categoryId}/tags` - 添加标签到分类
- `DELETE /admin/category/{categoryId}/tags/{tagId}` - 从分类移除标签
- `POST /admin/category/{categoryId}/tags/batch` - 批量添加标签
- `PUT /admin/category/{categoryId}/tags/sort` - 更新标签排序
- `GET /admin/category/tags/available` - 获取可用标签
- `GET /admin/category/tags/search` - 搜索标签
- `POST /admin/category/{categoryId}/tags/create` - 创建标签

### 2. 服务层

#### 分类服务
- `ICategoryService.java` - 分类服务接口
- `CategoryServiceImpl.java` - 分类服务实现

主要方法：
- `getEnabledCategories()` - 获取启用分类
- `getCategoryTree()` - 获取分类树
- `getCategoryDetail(Long id)` - 获取分类详情
- `enableCategory(Long id, Boolean enabled)` - 启用/禁用分类
- `enableCategoryAudit(Long id, Boolean enableAudit)` - 启用/禁用审核

#### 标签管理方法
- `addTagToCategory(Long categoryId, Long tagId)` - 添加标签到分类
- `removeTagFromCategory(Long categoryId, Long tagId)` - 从分类移除标签
- `batchAddTagsToCategory(Long categoryId, List<Long> tagIds)` - 批量添加标签
- `updateCategoryTagsSort(Long categoryId, List<TagSortDTO> tagSorts)` - 更新标签排序
- `getCategoryTagCount(Long categoryId)` - 获取分类标签数量
- `isTagBelongToCategory(Long categoryId, Long tagId)` - 检查标签是否属于分类

### 3. 数据访问层

#### Mapper接口
- `CategoryMapper.java` - 分类数据访问接口
- `CategoryTagMapper.java` - 分类标签关联数据访问接口

#### XML映射文件
- `CategoryMapper.xml` - 分类SQL映射
- `CategoryTagMapper.xml` - 分类标签关联SQL映射

## 前端功能

### 1. 分类管理页面

#### 文件位置
- `admin/src/views/ad/post/category.vue` - 分类管理页面
- `admin/src/api/ad/category.js` - 分类API接口

#### 主要功能
- 分类列表展示（支持分页、搜索）
- 分类新增/编辑（支持图标上传）
- 分类删除（支持批量删除）
- 分类启用/禁用
- 分类审核开关
- 标签管理弹窗

#### 标签管理功能
- 查看当前分类标签
- 添加已有标签到分类
- 从分类移除标签
- 创建新标签（支持颜色选择）
- 标签排序管理

### 2. 界面特性

#### 分类表格
- 分类名称、上级分类、图标展示
- 启用状态、审核状态显示
- 标签列表展示
- 操作按钮（编辑、删除、标签管理）

#### 标签管理弹窗
- 当前分类标签展示（支持删除）
- 标签创建表单（名称、颜色）
- 可用标签选择（点击添加）
- 响应式布局设计

## 使用说明

### 1. 分类创建流程

1. 点击"新增"按钮
2. 填写分类基本信息：
   - 分类名称（必填）
   - 上级分类（必选）
   - 分类图标（可选，支持图片上传）
   - 分类描述（可选）
   - 提示信息（可选）
   - 最大图片数（必填）
   - 排序序号（必填）
   - 是否启用（默认启用）
   - 是否启用审核（默认启用）
3. 保存分类

### 2. 标签管理流程

1. 选择要管理的分类
2. 点击"管理标签"按钮
3. 在弹窗中进行标签操作：
   - 查看当前分类的标签
   - 点击已有标签添加到分类
   - 创建新标签（输入名称、选择颜色）
   - 删除分类中的标签

### 3. 分类配置

#### 启用/禁用分类
- 在分类列表中点击状态列进行切换
- 禁用的分类不会在小程序中显示

#### 审核开关
- 控制该分类下的帖子是否需要审核
- 启用审核的帖子需要管理员审核后才能发布

## 技术特性

### 1. 数据库设计
- 使用关联表设计，支持多对多关系
- 添加排序字段，支持自定义排序
- 支持软删除和状态管理

### 2. 后端架构
- 使用MyBatis Plus进行数据访问
- 支持事务管理
- 提供完整的RESTful API

### 3. 前端实现
- 使用Vue.js + Element UI
- 支持文件上传（图标）
- 响应式设计
- 组件化开发

### 4. 性能优化
- 分页查询
- 索引优化
- 缓存策略

## 扩展功能

### 1. 标签统计
- 标签使用次数统计
- 热门标签推荐
- 标签使用趋势分析

### 2. 分类权限
- 分类访问权限控制
- 用户角色权限管理
- 分类内容审核流程

### 3. 标签推荐
- 基于用户行为的标签推荐
- 智能标签匹配
- 标签相似度计算

## 注意事项

1. 删除分类时需要考虑关联的标签和帖子
2. 图标上传需要配置存储服务
3. 标签颜色需要符合前端展示规范
4. 分类层级不宜过深，建议不超过3级
5. 标签数量需要控制，避免过多影响性能

## 部署说明

1. 执行数据库脚本 `category_enhancement.sql`
2. 配置文件上传服务（阿里云OSS或本地存储）
3. 更新后端配置文件
4. 重启后端服务
5. 更新前端代码并部署 