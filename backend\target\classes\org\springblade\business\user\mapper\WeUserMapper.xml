<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.WeUserMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userResultMap" type="org.springblade.business.user.entity.WeUser">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="nickname" property="nickname"/>
        <result column="mobile" property="mobile"/>
        <result column="gender" property="gender"/>
        <result column="signature" property="signature"/>
        <result column="company_name" property="companyName"/>
    </resultMap>


    <select id="selectUserPage" resultType="org.springblade.business.user.vo.WeUserVO">
        SELECT
            u.*,
            COALESCE(p.post_count, 0) as postCount,
            DATE_FORMAT(COALESCE(r.last_request_time, u.update_time), '%Y-%m-%d %H:%i:%s') as lastActiveTime,
            COALESCE(s.total_signin_days, 0) as signInDays,
            COALESCE(u.balance, 0) as points
        FROM urb_user u
        LEFT JOIN (
            SELECT create_user, COUNT(*) as post_count
            FROM urb_post
            WHERE is_deleted = 0
            GROUP BY create_user
        ) p ON u.id = p.create_user
        LEFT JOIN (
            SELECT user_id, COUNT(*) as total_signin_days
            FROM urb_signin_record
            WHERE is_deleted = 0
            GROUP BY user_id
        ) s ON CAST(u.id AS CHAR) = s.user_id
        LEFT JOIN (
            SELECT user_id, last_request_time
            FROM urb_user_request_stats
        ) r ON u.id = r.user_id
        WHERE u.is_deleted = 0
        <if test="user.nickname != null and user.nickname != ''">
            AND u.nickname LIKE CONCAT('%', #{user.nickname}, '%')
        </if>
        <if test="user.mobile != null and user.mobile != ''">
            AND u.mobile LIKE CONCAT('%', #{user.mobile}, '%')
        </if>
        <if test="user.gender != null and user.gender != ''">
            AND u.gender = #{user.gender}
        </if>
        <if test="user.id!=null">
            AND u.id = #{user.id}
        </if>
        ORDER BY u.create_time DESC
    </select>

    <!-- 获取用户统计数据 -->
    <select id="getUserStats" resultType="java.util.Map">
        SELECT
            (SELECT COUNT(*) FROM urb_post WHERE create_user = #{userId} ) and is_deleted = 0  as postCount,
            (SELECT COUNT(*) FROM urb_like WHERE user_id = #{userId} ) and is_deleted = 0  as likeCount,
            (SELECT COUNT(*) FROM urb_favorite WHERE user_id = #{userId} ) and is_deleted = 0 as favoriteCount
    </select>

    <select id="getUserActiveCount" resultType="java.util.Map">
        select DATE_FORMAT(sync_time, '&#37;Y-&#37;m-&#37;d') as date, COUNT(*) as count
        from urb_user_request_stats
        where DATE_FORMAT(sync_time, '&#37;Y-&#37;m-&#37;d') &gt;= #{startDate}
        and DATE_FORMAT(sync_time, '&#37;Y-&#37;m-&#37;d') &lt;= #{endDate}
        group by DATE_FORMAT(sync_time, '&#37;Y-&#37;m-&#37;d')
        ORDER BY date ASC
    </select>
    <select id="getUserRequestStatsTop20"
            resultType="org.springblade.business.statistics.entity.UserRequestStats">
        select * from urb_user_request_stats
        where DATE_FORMAT(sync_time, '&#37;Y-&#37;m-&#37;d') &gt;= #{date}
        and DATE_FORMAT(sync_time, '&#37;Y-&#37;m-&#37;d') &lt;= #{date}
        order by today_request_count desc
        limit 20
    </select>

</mapper>
