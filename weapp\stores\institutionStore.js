const { request } = require('../utils/request.js');
const { institution: API } = require('../config/api.js');

/**
 * 获取机构列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码，默认1
 * @param {number} params.size 每页大小，默认10
 * @param {string} params.name 机构名称（模糊搜索）
 * @param {string} params.description 机构描述（模糊搜索）
 * @param {number} params.typeId 机构分类ID
 * @param {string} params.sortType 排序类型：latest-最新，nearby-附近
 * @returns {Promise<Object>} 机构列表数据
 */
const getInstitutionList = async (params = {}) => {
  try {
    console.log('institutionStore: 开始请求机构列表');
    console.log('institutionStore: API.page =', API.page);
    console.log('institutionStore: 请求参数 =', params);

    const requestData = {
      current: params.current || 1,
      size: params.size || 10,
      ...(params.name && { name: params.name }),
      ...(params.description && { description: params.description }),
      ...(params.typeId && { typeId: params.typeId }),
      // 可以根据需要添加更多查询条件
      ...(params.contactPerson && { contactPerson: params.contactPerson }),
      ...(params.phone && { phone: params.phone }),
      ...(params.address && { address: params.address })
    };

    console.log('institutionStore: 最终请求数据 =', requestData);

    // 构建URL查询参数
    const queryParams = new URLSearchParams();
    Object.keys(requestData).forEach(key => {
      if (requestData[key] !== undefined && requestData[key] !== null) {
        queryParams.append(key, requestData[key]);
      }
    });

    const response = await request({
      url: `${API.page}?${queryParams.toString()}`,
      method: 'GET'
    });

    console.log('institutionStore: 接口响应 =', response);

    if (response.code === 200) {
      const data = response.data;
      return {
        records: processInstitutions(data.records || []),
        total: data.total || 0,
        size: data.size || 10,
        current: data.current || 1,
        pages: data.pages || 0
      };
    } else {
      console.error('获取机构列表失败:', response.msg);
      throw new Error(response.msg || '获取机构列表失败');
    }
  } catch (error) {
    console.error('请求机构列表接口失败:', error);
    throw error;
  }
};

/**
 * 获取机构详情
 * @param {number|string} id 机构ID
 * @returns {Promise<Object|null>} 机构详情数据
 */
const getInstitutionDetail = async (params) => {
  try {
    const response = await request({
      url: API.detail,
      method: 'GET',
      data: params
    });

    if (response.code === 200 && response.success) {
      const institution = response.data;
      return processInstitutionDetail(institution);
    } else {
      console.error('获取机构详情失败:', response.msg);
      return null;
    }
  } catch (error) {
    console.error('请求机构详情接口失败:', error);
    return null;
  }
};

/**
 * 获取我加入的机构列表
 * @param {Object} params 查询参数
 * @param {number} params.current 当前页码，默认1
 * @param {number} params.size 每页大小，默认10
 * @returns {Promise<Object>} 我的机构列表数据
 */
const getMyInstitutions = async (params = {}) => {
  try {
    const response = await request({
      url: API.myInstitutions,
      method: 'GET',
      data: {
        current: params.current || 1,
        size: params.size || 10
      }
    });

    if (response.code === 200 && response.success) {
      const data = response.data;
      return {
        records: processInstitutions(data.records || []),
        total: data.total || 0,
        size: data.size || 10,
        current: data.current || 1,
        pages: data.pages || 0
      };
    } else {
      console.error('获取我的机构列表失败:', response.msg);
      throw new Error(response.msg || '获取我的机构列表失败');
    }
  } catch (error) {
    console.error('请求我的机构列表接口失败:', error);
    throw error;
  }
};

/**
 * 处理机构列表数据
 * @param {Array} institutions 原始机构数据
 * @returns {Array} 处理后的机构数据
 */
const processInstitutions = (institutions) => {
  return institutions.map(institution => processInstitution(institution));
};

/**
 * 处理单个机构数据
 * @param {Object} institution 原始机构数据
 * @returns {Object} 处理后的机构数据
 */
const processInstitution = (institution) => {
  return {
    id: institution.id,
    name: institution.name || '未知机构',
    logo: institution.logo || '/assets/images/default-institution.png',
    description: institution.description || '暂无描述',
    address: institution.address || '地址未知',
    phone: institution.phone || '',
    contactPerson: institution.contactPerson || '',
    latitude: institution.latitude || null,
    longitude: institution.longitude || null,
    typeId: institution.typeId || null,
    typeName: institution.typeName || '未分类',
    businessHours: institution.businessHours || null,
    images: institution.images ? parseImages(institution.images) : [],
    isStore: institution.isStore || false,
    hasDelivery: institution.hasDelivery || false,
    serviceRadius: institution.serviceRadius || 0,
    paymentMethods: institution.paymentMethods ? institution.paymentMethods.split(',') : [],
    specialServices: institution.specialServices || '',
    auditStatus: institution.auditStatus || 0,
    status: institution.status || 1,
    postCount: institution.postCount || 0,
    createTime: institution.createTime || '',
    updateTime: institution.updateTime || ''
  };
};

/**
 * 处理机构详情数据（包含帖子列表）
 * @param {Object} institution 原始机构详情数据
 * @returns {Object} 处理后的机构详情数据
 */
const processInstitutionDetail = (institution) => {
  const processedInstitution = processInstitution(institution);
  
  // 处理关联的帖子列表
  if (institution.posts && Array.isArray(institution.posts)) {
    processedInstitution.posts = institution.posts.map(post => ({
      id: post.id,
      title: post.title || '无标题',
      content: post.content || '',
      images: post.images ? parseImages(post.images) : [],
      address: post.address || '',
      publishTime: post.publishTime || post.createTime || '',
      viewCount: post.viewCount || 0,
      likeCount: post.likeCount || 0,
      commentCount: post.commentCount || 0
    }));
  } else {
    processedInstitution.posts = [];
  }
  
  return processedInstitution;
};

/**
 * 解析图片字符串
 * @param {string} imagesStr 图片JSON字符串
 * @returns {Array} 图片URL数组
 */
const parseImages = (imagesStr) => {
  try {
    if (typeof imagesStr === 'string') {
      const images = JSON.parse(imagesStr);
      if (Array.isArray(images)) {
        return images.map(img => {
          if (typeof img === 'string') {
            return img;
          } else if (img && img.url) {
            return img.url;
          }
          return '';
        }).filter(url => url);
      }
    } else if (Array.isArray(imagesStr)) {
      return imagesStr;
    }
  } catch (error) {
    console.error('解析图片数据失败:', error);
  }
  return [];
};

/**
 * 解析营业时间
 * @param {string} businessHoursStr 营业时间JSON字符串
 * @returns {Object} 营业时间对象
 */
const parseBusinessHours = (businessHoursStr) => {
  try {
    if (typeof businessHoursStr === 'string') {
      return JSON.parse(businessHoursStr);
    } else if (typeof businessHoursStr === 'object') {
      return businessHoursStr;
    }
  } catch (error) {
    console.error('解析营业时间失败:', error);
  }
  return {};
};

module.exports = {
  getInstitutionList,
  getInstitutionDetail,
  getMyInstitutions,
  processInstitutions,
  processInstitution,
  processInstitutionDetail,
  parseImages,
  parseBusinessHours
};
