# 城市菜单图片上传功能说明

## 功能概述

城市菜单管理模块已集成图片上传功能，支持为菜单项上传自定义图标。该功能基于 `FileUploadComponent` 组件实现，提供了完整的图片上传、预览和管理功能。

## 功能特性

### 1. 图标上传
- **支持格式**: JPG、PNG、GIF等图片格式
- **文件大小**: 最大2MB
- **建议尺寸**: 32x32px（最佳显示效果）
- **上传来源**: 管理后台（admin）
- **业务类型**: urbmenu-icon

### 2. 上传方式
- **工具栏上传**: 通过"上传图标"按钮批量上传
- **表单上传**: 在新增/编辑菜单时直接上传
- **拖拽上传**: 支持拖拽文件到上传区域

### 3. 预览功能
- **缩略图预览**: 在列表中显示32x32px缩略图
- **大图预览**: 点击图标可查看原图
- **错误处理**: 图标加载失败时显示占位符
- **批量预览**: 批量上传时显示所有选中项的预览效果

## 使用方法

### 1. 工具栏批量上传图标
1. 在菜单列表中选择一个或多个菜单项
2. 点击工具栏中的"上传图标"按钮
3. 在弹出的对话框中选择图标文件
4. 预览上传效果
5. 点击"确认上传"完成批量更新

### 2. 表单中上传图标
1. 点击"新增"或"编辑"按钮
2. 在"菜单图标"字段中点击"选择图标"
3. 选择图标文件并上传
4. 可以预览已上传的图标
5. 点击"删除图标"可移除已上传的图标

### 3. 图标显示
- **列表显示**: 32x32px缩略图，支持点击预览
- **颜色预览**: 显示背景颜色预览块
- **类型标签**: 显示菜单类型（菜单/滚动图片）

## 技术实现

### 1. 组件依赖
```javascript
import FileUploadComponent from '@/components/FileUpload/index.vue'
import { Picture } from '@element-plus/icons-vue'
```

### 2. API接口
```javascript
// 文件上传接口
POST /blade-system/file-upload/upload
Content-Type: multipart/form-data

// 参数
{
  file: File,                    // 上传的文件
  uploadSource: 'admin',         // 上传来源
  businessType: 'urbmenu-icon',  // 业务类型
  businessId: 'menu_id'          // 业务ID（可选）
}

// 菜单更新接口
POST /blade-ad/urbmenu/submit
{
  id: 'menu_id',
  name: '菜单名称',
  image: 'icon_url',
  sortWeight: 1,
  color: '#409EFF',
  url: 'link_url',
  category: 0
}
```

## 配置说明

### 1. 上传配置
```javascript
upload: {
  action: '/blade-system/file-upload/upload',
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('blade-auth')}`
  },
  data: {
    uploadSource: 'admin',
    businessType: 'urbmenu-icon'
  },
  accept: 'image/*',
  limit: 1,
  tip: '支持jpg/png/gif格式，建议尺寸32x32px，文件大小不超过2MB'
}
```

### 2. 表格列配置
```javascript
{
  label: "菜单图标",
  prop: "image",
  type: 'upload',
  slot: true,
  rules: [{
    required: true,
    message: "请上传菜单图标",
    trigger: "change"
  }]
}
```

## 界面组件

### 1. 工具栏按钮
```vue
<el-button type="primary"
           
           icon="el-icon-upload"
           plain
           v-if="permission.urbmenu_edit && selectionList.length > 0"
           @click="handleUploadImage">上传图标
</el-button>
```

### 2. 图片显示组件
```vue
<template #image="{ row }">
  <div class="image-cell">
    <el-image
      v-if="row.image"
      :src="row.image"
      :preview-src-list="[row.image]"
      fit="cover"
      class="menu-image"
      @error="handleImageError">
      <template #error>
        <div class="image-error">
          <el-icon><Picture /></el-icon>
        </div>
      </template>
    </el-image>
    <div v-else class="image-placeholder">
      <el-icon><Picture /></el-icon>
      <span>暂无图片</span>
    </div>
  </div>
</template>
```

### 3. 上传对话框
```vue
<el-dialog
  v-model="uploadDialogVisible"
  title="上传菜单图标"
  width="600px"
  :close-on-click-modal="false">
  <!-- 上传组件和预览 -->
</el-dialog>
```

## 样式设计

### 1. 图片单元格样式
```scss
.image-cell {
  display: flex;
  align-items: center;
  
  .menu-image {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }
  
  .image-error {
    width: 32px;
    height: 32px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }
  
  .image-placeholder {
    width: 32px;
    height: 32px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 10px;
  }
}
```

### 2. 颜色预览样式
```scss
.color-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .color-preview {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    border: 1px solid #e4e7ed;
  }
}
```

## 最佳实践

### 1. 图标设计
- 使用PNG格式，支持透明背景
- 建议尺寸32x32px，确保清晰度
- 保持简洁的设计风格
- 考虑在不同背景色下的显示效果

### 2. 性能优化
- 图标文件大小控制在100KB以内
- 使用CDN加速图标加载
- 实现图片懒加载

### 3. 用户体验
- 提供清晰的上传说明
- 支持批量操作提高效率
- 实时预览上传效果
- 友好的错误提示

## 注意事项

1. **文件安全**: 上传的文件会进行安全检查
2. **存储管理**: 定期清理未使用的图标文件
3. **权限控制**: 确保只有授权用户才能上传图标
4. **数据一致性**: 批量更新时确保数据一致性

## 扩展功能

### 1. 图标管理
- 图标库管理
- 图标分类管理
- 图标使用统计

### 2. 高级功能
- 图标裁剪和编辑
- 图标模板管理
- 批量图标处理
- 图标版本管理

## 故障排除

### 1. 上传失败
- 检查网络连接
- 验证文件格式和大小
- 确认用户权限
- 查看服务器日志

### 2. 显示异常
- 检查图标URL是否有效
- 验证CDN配置
- 确认浏览器兼容性

### 3. 性能问题
- 优化图标文件大小
- 检查CDN配置
- 监控服务器负载

## 更新日志

### v1.0.0 (2024-01-XX)
- 初始版本发布
- 支持单个和批量图标上传
- 集成FileUploadComponent组件
- 添加图片预览和错误处理
- 实现工具栏批量上传功能 