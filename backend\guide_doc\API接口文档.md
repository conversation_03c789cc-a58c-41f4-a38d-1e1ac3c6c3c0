# API接口文档

## 接口概述

### 基础信息
- **基础URL**: `http://localhost:80`
- **API版本**: v1.0
- **认证方式**: JWT Token
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

### 状态码说明
| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 认证接口

### 1. 用户登录
```
POST /blade-auth/oauth/token
```

**请求参数:**
```json
{
  "username": "admin",
  "password": "123456",
  "grant_type": "password",
  "scope": "all"
}
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "token_type": "bearer",
    "expires_in": 86400
  }
}
```

### 2. 刷新Token
```
POST /blade-auth/oauth/token
```

**请求参数:**
```json
{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "grant_type": "refresh_token"
}
```

## 帖子管理接口

### 1. 获取帖子列表
```
GET /blade-ad/post/list
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| title | String | 否 | 标题模糊查询 |
| content | String | 否 | 内容模糊查询 |
| audit_status | String | 否 | 审核状态 |
| status | Integer | 否 | 状态 |
| create_time_start | String | 否 | 创建时间开始 |
| create_time_end | String | 否 | 创建时间结束 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "title": "帖子标题",
        "content": "帖子内容",
        "images": ["http://example.com/image1.jpg"],
        "address": "发布地址",
        "publish_time": "2024-01-01 12:00:00",
        "audit_status": "PENDING",
        "geo_location": {
          "latitude": 39.9042,
          "longitude": 116.4074
        },
        "tags": ["标签1", "标签2"],
        "contact_name": "联系人",
        "contact_phone": "13800138000",
        "top": "0",
        "completed": 0,
        "create_time": "2024-01-01 12:00:00",
        "update_time": "2024-01-01 12:00:00"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 2. 获取帖子详情
```
GET /blade-ad/post/detail
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 帖子ID |

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "title": "帖子标题",
    "content": "帖子内容",
    "images": ["http://example.com/image1.jpg"],
    "address": "发布地址",
    "publish_time": "2024-01-01 12:00:00",
    "audit_status": "PENDING",
    "geo_location": {
      "latitude": 39.9042,
      "longitude": 116.4074
    },
    "tags": ["标签1", "标签2"],
    "contact_name": "联系人",
    "contact_phone": "13800138000",
    "top": "0",
    "completed": 0,
    "create_time": "2024-01-01 12:00:00",
    "update_time": "2024-01-01 12:00:00"
  }
}
```

### 3. 创建帖子
```
POST /blade-ad/post/save
```

**请求参数:**
```json
{
  "title": "帖子标题",
  "content": "帖子内容",
  "images": ["http://example.com/image1.jpg", "http://example.com/image2.jpg"],
  "address": "发布地址",
  "geo_location": {
    "latitude": 39.9042,
    "longitude": 116.4074
  },
  "tags": ["标签1", "标签2"],
  "contact_name": "联系人",
  "contact_phone": "13800138000"
}
```

**参数说明:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| title | String | 是 | 帖子标题，最大100字符 |
| content | String | 是 | 帖子内容，最大2000字符 |
| images | Array | 否 | 图片URL数组，最多6张 |
| address | String | 否 | 发布地址 |
| geo_location | Object | 否 | 地理位置信息 |
| tags | Array | 否 | 标签数组 |
| contact_name | String | 否 | 联系人姓名 |
| contact_phone | String | 否 | 联系电话 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "创建成功",
  "data": {
    "id": 1,
    "title": "帖子标题",
    "content": "帖子内容",
    "create_time": "2024-01-01 12:00:00"
  }
}
```

### 4. 更新帖子
```
POST /blade-ad/post/update
```

**请求参数:**
```json
{
  "id": 1,
  "title": "更新后的标题",
  "content": "更新后的内容",
  "images": ["http://example.com/image1.jpg"],
  "address": "更新后的地址",
  "tags": ["标签1", "标签3"],
  "contact_name": "新联系人",
  "contact_phone": "13900139000"
}
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 5. 删除帖子
```
POST /blade-ad/post/remove
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | String | 是 | 帖子ID列表，逗号分隔 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

### 6. 审核帖子
```
POST /blade-ad/post/audit
```

**请求参数:**
```json
{
  "id": 1,
  "audit_status": "APPROVED",
  "audit_remark": "审核通过"
}
```

**参数说明:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 帖子ID |
| audit_status | String | 是 | 审核状态：PENDING/APPROVED/REJECTED |
| audit_remark | String | 否 | 审核备注 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "审核成功",
  "data": true
}
```

## 分类管理接口

### 1. 获取分类列表
```
GET /blade-ad/category/list
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| name | String | 否 | 分类名称模糊查询 |
| parent_id | Long | 否 | 上级分类ID |

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "name": "分类名称",
        "parent_id": 0,
        "max_images": 6,
        "allow_tags": ["标签1", "标签2"],
        "tags": [
          {
            "id": 1,
            "tag_name": "标签1",
            "description": "标签描述",
            "sort_order": 1
          }
        ],
        "create_time": "2024-01-01 12:00:00",
        "update_time": "2024-01-01 12:00:00"
      }
    ],
    "total": 10,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

### 2. 获取分类详情
```
GET /blade-ad/category/detail
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 分类ID |

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "name": "分类名称",
    "parent_id": 0,
    "max_images": 6,
    "allow_tags": ["标签1", "标签2"],
    "tags": [
      {
        "id": 1,
        "tag_name": "标签1",
        "description": "标签描述",
        "sort_order": 1
      }
    ],
    "create_time": "2024-01-01 12:00:00",
    "update_time": "2024-01-01 12:00:00"
  }
}
```

### 3. 创建分类
```
POST /blade-ad/category/save
```

**请求参数:**
```json
{
  "name": "分类名称",
  "parent_id": 0,
  "max_images": 6,
  "allow_tags": ["标签1", "标签2"]
}
```

**参数说明:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | String | 是 | 分类名称，最大50字符 |
| parent_id | Long | 否 | 上级分类ID，0表示顶级分类 |
| max_images | Integer | 否 | 最大图片数量，默认6 |
| allow_tags | Array | 否 | 允许的标签数组 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "创建成功",
  "data": {
    "id": 1,
    "name": "分类名称",
    "create_time": "2024-01-01 12:00:00"
  }
}
```

### 4. 更新分类
```
POST /blade-ad/category/update
```

**请求参数:**
```json
{
  "id": 1,
  "name": "更新后的分类名称",
  "parent_id": 0,
  "max_images": 8,
  "allow_tags": ["标签1", "标签2", "标签3"]
}
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 5. 删除分类
```
POST /blade-ad/category/remove
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | String | 是 | 分类ID列表，逗号分隔 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

### 6. 获取分类树形结构
```
GET /blade-ad/category/tree
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "name": "顶级分类",
      "parent_id": 0,
      "children": [
        {
          "id": 2,
          "name": "子分类1",
          "parent_id": 1,
          "children": []
        },
        {
          "id": 3,
          "name": "子分类2",
          "parent_id": 1,
          "children": []
        }
      ]
    }
  ]
}
```

## 标签管理接口

### 1. 获取标签列表
```
GET /blade-ad/tag/list
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| tag_name | String | 否 | 标签名称模糊查询 |
| category_id | Long | 否 | 分类ID |

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "tag_name": "标签名称",
        "description": "标签描述",
        "sort_order": 1,
        "create_time": "2024-01-01 12:00:00",
        "update_time": "2024-01-01 12:00:00"
      }
    ],
    "total": 20,
    "size": 10,
    "current": 1,
    "pages": 2
  }
}
```

### 2. 获取标签详情
```
GET /blade-ad/tag/detail
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 标签ID |

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "tag_name": "标签名称",
    "description": "标签描述",
    "sort_order": 1,
    "create_time": "2024-01-01 12:00:00",
    "update_time": "2024-01-01 12:00:00"
  }
}
```

### 3. 创建标签
```
POST /blade-ad/tag/save
```

**请求参数:**
```json
{
  "tag_name": "标签名称",
  "description": "标签描述",
  "sort_order": 1
}
```

**参数说明:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tag_name | String | 是 | 标签名称，最大50字符 |
| description | String | 否 | 标签描述 |
| sort_order | Integer | 否 | 排序序号，默认0 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "创建成功",
  "data": {
    "id": 1,
    "tag_name": "标签名称",
    "create_time": "2024-01-01 12:00:00"
  }
}
```

### 4. 更新标签
```
POST /blade-ad/tag/update
```

**请求参数:**
```json
{
  "id": 1,
  "tag_name": "更新后的标签名称",
  "description": "更新后的标签描述",
  "sort_order": 2
}
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

### 5. 删除标签
```
POST /blade-ad/tag/remove
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| ids | String | 是 | 标签ID列表，逗号分隔 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": true
}
```

### 6. 根据分类获取标签
```
GET /blade-ad/tag/by-category/{categoryId}
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryId | Long | 是 | 分类ID |

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "id": 1,
      "tag_name": "标签1",
      "description": "标签描述",
      "sort_order": 1
    },
    {
      "id": 2,
      "tag_name": "标签2",
      "description": "标签描述",
      "sort_order": 2
    }
  ]
}
```

## 文件上传接口

### 1. 上传图片
```
POST /blade-file/upload
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| file | File | 是 | 图片文件 |
| category | String | 否 | 文件分类 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "上传成功",
  "data": {
    "url": "http://example.com/uploads/image.jpg",
    "filename": "image.jpg",
    "size": 1024000,
    "mime_type": "image/jpeg"
  }
}
```

### 2. 批量上传图片
```
POST /blade-file/batch-upload
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| files | File[] | 是 | 图片文件数组 |
| category | String | 否 | 文件分类 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "上传成功",
  "data": [
    {
      "url": "http://example.com/uploads/image1.jpg",
      "filename": "image1.jpg",
      "size": 1024000,
      "mime_type": "image/jpeg"
    },
    {
      "url": "http://example.com/uploads/image2.jpg",
      "filename": "image2.jpg",
      "size": 2048000,
      "mime_type": "image/jpeg"
    }
  ]
}
```

## 用户管理接口

### 1. 获取用户信息
```
GET /blade-user/detail
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 用户ID |

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "id": 1,
    "nickname": "用户昵称",
    "mobile": "13800138000",
    "gender": "MALE",
    "signature": "个性签名",
    "create_time": "2024-01-01 12:00:00",
    "update_time": "2024-01-01 12:00:00"
  }
}
```

### 2. 更新用户信息
```
POST /blade-user/update
```

**请求参数:**
```json
{
  "id": 1,
  "nickname": "新昵称",
  "gender": "FEMALE",
  "signature": "新的个性签名"
}
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": true
}
```

## 反馈管理接口

### 1. 提交反馈
```
POST /blade-ad/feedback/save
```

**请求参数:**
```json
{
  "type": "BUG",
  "title": "反馈标题",
  "content": "反馈内容",
  "contact": "联系方式",
  "images": ["http://example.com/image.jpg"]
}
```

**参数说明:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | String | 是 | 反馈类型：BUG/SUGGESTION/COMPLAINT |
| title | String | 是 | 反馈标题 |
| content | String | 是 | 反馈内容 |
| contact | String | 否 | 联系方式 |
| images | Array | 否 | 图片URL数组 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "提交成功",
  "data": {
    "id": 1,
    "type": "BUG",
    "title": "反馈标题",
    "create_time": "2024-01-01 12:00:00"
  }
}
```

### 2. 获取反馈列表
```
GET /blade-ad/feedback/list
```

**请求参数:**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| current | Integer | 否 | 页码，默认1 |
| size | Integer | 否 | 每页大小，默认10 |
| type | String | 否 | 反馈类型 |
| status | String | 否 | 处理状态 |

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "type": "BUG",
        "title": "反馈标题",
        "content": "反馈内容",
        "contact": "联系方式",
        "images": ["http://example.com/image.jpg"],
        "status": "PENDING",
        "create_time": "2024-01-01 12:00:00"
      }
    ],
    "total": 10,
    "size": 10,
    "current": 1,
    "pages": 1
  }
}
```

## 统计接口

### 1. 获取帖子统计
```
GET /blade-ad/post/stats
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "total_posts": 1000,
    "pending_audit": 50,
    "approved_posts": 900,
    "rejected_posts": 50,
    "today_posts": 10,
    "week_posts": 70,
    "month_posts": 300
  }
}
```

### 2. 获取分类统计
```
GET /blade-ad/category/stats
```

**响应示例:**
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": [
    {
      "category_id": 1,
      "category_name": "分类1",
      "post_count": 100,
      "percentage": 10.0
    },
    {
      "category_id": 2,
      "category_name": "分类2",
      "post_count": 200,
      "percentage": 20.0
    }
  ]
}
```

## 错误码说明

### 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 参数验证失败 |
| 1002 | 资源不存在 |
| 1003 | 权限不足 |
| 1004 | 操作失败 |
| 1005 | 数据重复 |
| 1006 | 文件上传失败 |
| 1007 | 审核状态错误 |
| 1008 | 分类不存在 |
| 1009 | 标签不存在 |

### 系统错误码
| 错误码 | 说明 |
|--------|------|
| 5001 | 数据库连接失败 |
| 5002 | 缓存服务异常 |
| 5003 | 文件服务异常 |
| 5004 | 第三方服务异常 |
| 5005 | 系统内部错误 |

## 接口调用示例

### JavaScript示例
```javascript
// 获取帖子列表
async function getPostList(page = 1, size = 10) {
  try {
    const response = await fetch(`/blade-ad/post/list?current=${page}&size=${size}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('获取帖子列表失败:', error);
    throw error;
  }
}

// 创建帖子
async function createPost(postData) {
  try {
    const response = await fetch('/blade-ad/post/save', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(postData)
    });
    
    const result = await response.json();
    if (result.code === 200) {
      return result.data;
    } else {
      throw new Error(result.msg);
    }
  } catch (error) {
    console.error('创建帖子失败:', error);
    throw error;
  }
}
```

### Java示例
```java
// 使用RestTemplate调用接口
@Service
public class PostApiService {
    
    @Autowired
    private RestTemplate restTemplate;
    
    public PostVO getPostById(Long id) {
        String url = "http://localhost:80/blade-ad/post/detail?id=" + id;
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<String> entity = new HttpEntity<>(headers);
        
        ResponseEntity<R<PostVO>> response = restTemplate.exchange(
            url, HttpMethod.GET, entity, 
            new ParameterizedTypeReference<R<PostVO>>() {}
        );
        
        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            return response.getBody().getData();
        }
        
        throw new RuntimeException("获取帖子详情失败");
    }
    
    public PostVO createPost(PostDTO postDTO) {
        String url = "http://localhost:80/blade-ad/post/save";
        
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(token);
        headers.setContentType(MediaType.APPLICATION_JSON);
        
        HttpEntity<PostDTO> entity = new HttpEntity<>(postDTO, headers);
        
        ResponseEntity<R<PostVO>> response = restTemplate.exchange(
            url, HttpMethod.POST, entity, 
            new ParameterizedTypeReference<R<PostVO>>() {}
        );
        
        if (response.getStatusCode() == HttpStatus.OK && response.getBody() != null) {
            return response.getBody().getData();
        }
        
        throw new RuntimeException("创建帖子失败");
    }
}
```

## 注意事项

### 1. 认证要求
- 除登录接口外，所有接口都需要在请求头中携带JWT Token
- Token格式：`Authorization: Bearer <token>`
- Token过期需要调用刷新接口获取新的Token

### 2. 参数验证
- 所有必填参数不能为空
- 字符串长度不能超过限制
- 数值类型需要符合范围要求
- 日期格式统一使用：`yyyy-MM-dd HH:mm:ss`

### 3. 分页查询
- 页码从1开始
- 每页大小建议不超过100
- 返回结果包含分页信息

### 4. 文件上传
- 支持的文件格式：jpg, jpeg, png, gif
- 单个文件大小限制：10MB
- 批量上传最多10个文件

### 5. 错误处理
- 统一使用R对象返回结果
- 错误信息包含具体的错误原因
- 系统异常会返回500状态码

### 6. 性能优化
- 使用分页查询避免返回大量数据
- 合理使用缓存减少数据库查询
- 异步处理耗时操作

### 7. 安全考虑
- 敏感数据需要加密传输
- 接口调用需要频率限制
- 文件上传需要类型和大小验证
</rewritten_file> 