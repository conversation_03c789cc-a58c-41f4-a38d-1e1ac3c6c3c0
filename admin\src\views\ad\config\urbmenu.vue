<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.urbmenu_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      
      <!-- 自定义图片列显示 -->
      <template #image="{ row }">
        <div class="image-cell">
          <el-image
            v-if="row.image"
            :src="row.image"
            :preview-src-list="[row.image]"
            fit="cover"
            class="menu-image"
            @error="handleImageError">
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div v-else class="image-placeholder">
            <el-icon><Picture /></el-icon>
            <span>暂无图片</span>
          </div>
        </div>
      </template>
      
      <!-- 自定义颜色列显示 -->
      <template #color="{ row }">
        <div class="color-cell">
          <div class="color-preview" :style="{ backgroundColor: row.color }"></div>
          <span>{{ row.color }}</span>
        </div>
      </template>
      
      <!-- 自定义分类列显示 -->
      <template #category="{ row }">
        <!-- <el-tag :type="row.category === 0 ? 'primary' : 'success'">
          {{ row.category === 0 ? '菜单' : '滚动图片' }}
        </el-tag> -->
        <el-tag v-if="row.category === 0" type="primary">
          菜单
        </el-tag>
        <el-tag v-if="row.category === 1" type="success">
          滚动图片
        </el-tag>
        <el-tag v-if="row.category === 2" type="warning">
          用户菜单
        </el-tag>
      </template>
    </avue-crud>
    
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/urbmenu";
  import {mapGetters} from "vuex";
  import FileUploadComponent from '@/components/FileUpload/index.vue';
  import { Picture } from '@element-plus/icons-vue';

  export default {
    components: {
      FileUploadComponent,
      Picture
    },
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        // 图片上传相关
        uploadDialogVisible: false,
        uploadImageUrl: '',
        uploadLoading: false,
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "菜单名称",
              prop: "name",
              search: true,
              rules: [{
                required: true,
                message: "请输入菜单名称",
                trigger: "blur"
              }]
            },
            {
              label: "菜单图标",
              prop: "image",
              type: 'upload',
              action: '/blade-system/file-upload/upload',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('blade-auth')}`
              },
              data: {
                uploadSource: 'admin',
                businessType: 'urbmenu-icon'
              },
              accept: 'image/*',
              limit: 1,
              tip: '支持jpg/png/gif格式，建议尺寸32x32px，文件大小不超过2MB',
              slot: true,
              propsHttp: {
                url: 'accessUrl',
                name: 'fileName',
                res: 'data'
              },
              rules: [{
                required: true,
                message: "请上传菜单图标",
                trigger: "change"
              }]
            },
            {
              label: "排序权重",
              prop: "sortWeight",
              type: 'number',
              rules: [{
                required: true,
                message: "请输入排序权重",
                trigger: "blur"
              }]
            },
            {
              label: "背景颜色",
              prop: "color",
              type: 'color',
              slot: true,
              colorFormat: 'hex',
              rules: [{
                required: true,
                message: "请选择背景颜色",
                trigger: "change"
              }]
            },
            {
              label: "链接地址",
              prop: "url",
              span: 24,
              hide: true,
              rules: [{
                required: true,
                message: "请输入链接地址",
                trigger: "blur"
              }]
            },
            {
              label: "菜单类型",
              prop: "category",
              value:0,
              type: 'select',
              dicData: [
                { label: '菜单', value: 0 },
                { label: '滚动图片', value: 1 },
                { label: '用户菜单', value: 2 }
              ],
              search: true,
              slot: true,
              rules: [{
                required: true,
                message: "请选择菜单类型",
                trigger: "change"
              }]
            },
            {
              label: "是否显示",
              prop: "status",
              type: 'select',
              value:1,
              dicData: [
                { label: '显示', value: 1 },
                { label: '隐藏', value: 0 }
              ],
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.urbmenu_add, false),
          viewBtn: this.validData(this.permission.urbmenu_view, false),
          delBtn: this.validData(this.permission.urbmenu_delete, false),
          editBtn: this.validData(this.permission.urbmenu_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleImageError() {
        // 图片加载失败时的处理
        console.log('图片加载失败');
      },
      
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style lang="scss" scoped>
.image-cell {
  display: flex;
  align-items: center;
  
  .menu-image {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }
  
  .image-error {
    width: 32px;
    height: 32px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }
  
  .image-placeholder {
    width: 32px;
    height: 32px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 10px;
    
    .el-icon {
      font-size: 12px;
      margin-bottom: 2px;
    }
  }
}

.color-cell {
  display: flex;
  align-items: center;
  gap: 8px;
  
  .color-preview {
    width: 20px;
    height: 20px;
    border-radius: 2px;
    border: 1px solid #e4e7ed;
  }
}

.upload-dialog-content {
  .upload-tips {
    margin-bottom: 20px;
  }
  
  .upload-area {
    margin-bottom: 20px;
  }
  
  .upload-preview {
    h4 {
      margin-bottom: 12px;
      color: #606266;
    }
    
    .preview-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 12px;
      
      .preview-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background: #fafafa;
        
        .preview-image {
          margin-right: 12px;
          
          .menu-preview-image {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            border: 1px solid #e4e7ed;
          }
        }
        
        .preview-info {
          flex: 1;
          
          .preview-name {
            font-weight: 500;
            margin-bottom: 4px;
            word-break: break-all;
          }
          
          .preview-color {
            width: 16px;
            height: 16px;
            border-radius: 2px;
            border: 1px solid #e4e7ed;
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style>
