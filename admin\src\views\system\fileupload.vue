<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad">
      
      <!-- 自定义工具栏 -->
      <template #menu-left>
        <el-button type="primary" @click="handleUpload">
          上传文件
        </el-button>
        <el-button type="success" @click="handleBatchUpload">
          批量上传
        </el-button>
        <el-button type="danger" :disabled="selectionList.length === 0" @click="handleBatchDelete">
          批量删除
        </el-button>
        <el-button type="info" @click="handleStats">
          统计信息
        </el-button>
        <el-button type="warning" @click="handleCleanExpired">
          清理过期文件
        </el-button>
      </template>

      <!-- 文件预览 -->
      <template #filePreview="{ row }">
        <div class="file-preview">
          <el-image
            v-if="row.fileCategory === 'image'"
            :src="row.accessUrl"
            :preview-src-list="[row.accessUrl]"
            fit="cover"
            style="width: 60px; height: 60px; border-radius: 4px;">
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div v-else class="file-icon">
            <el-icon v-if="row.fileCategory === 'document'"><Document /></el-icon>
            <el-icon v-else-if="row.fileCategory === 'video'"><VideoPlay /></el-icon>
            <el-icon v-else-if="row.fileCategory === 'audio'"><Headset /></el-icon>
            <el-icon v-else-if="row.fileCategory === 'office'"><Files /></el-icon>
            <el-icon v-else><Files /></el-icon>
          </div>
        </div>
      </template>

      <!-- 文件信息 -->
      <template #fileInfo="{ row }">
        <div class="file-info">
          <div class="file-name" :title="row.originalName">{{ row.originalName }}</div>
          <div class="file-meta">
            <span>{{ formatFileSize(row.fileSize) }}</span>
            <span>{{ row.fileExtension?.toUpperCase() }}</span>
            <span v-if="row.fileMd5" class="file-md5" :title="row.fileMd5">MD5: {{ row.fileMd5.substring(0, 8) }}...</span>
          </div>
        </div>
      </template>

      <!-- 文件状态 -->
      <template #fileStatus="{ row }">
        <el-tag :type="getStatusType(row.status)" size="small">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>

      <!-- 业务关联 -->
      <template #businessInfo="{ row }">
        <div v-if="row.businessType && row.businessId" class="business-info">
          <div class="business-type">{{ row.businessType }}</div>
          <div class="business-id">ID: {{ row.businessId }}</div>
        </div>
        <span v-else class="no-business">无关联业务</span>
      </template>

      <!-- 操作按钮 -->
      <template #menu="{ row }">
        <el-button type="primary" size="small" @click="handlePreview(row)">
          预览
        </el-button>
        <el-button type="success" size="small" @click="handleDownload(row)">
          下载
        </el-button>
        <el-button type="info" size="small" @click="handleCopyUrl(row)">
          复制链接
        </el-button>
        <el-button type="danger" size="small" @click="handleDelete(row)">
          删除
        </el-button>
      </template>
    </avue-crud>

    <!-- 文件上传对话框 -->
    <el-dialog v-model="uploadDialogVisible" title="上传文件" width="600px">
      <div class="upload-form">
        <el-form :model="uploadForm" label-width="100px">
          <el-form-item label="业务类型">
            <el-input v-model="uploadForm.businessType" placeholder="可选：关联业务类型" />
          </el-form-item>
          <el-form-item label="业务ID">
            <el-input v-model="uploadForm.businessId" placeholder="可选：关联业务ID" />
          </el-form-item>
        </el-form>
      </div>
      <FileUploadComponent
        ref="fileUploadRef"
        v-model="uploadedFiles"
        :multiple="uploadForm.multiple"
        :limit="uploadForm.limit"
        :upload-source="'admin'"
        :business-type="uploadForm.businessType"
        :business-id="uploadForm.businessId"
        @success="handleUploadSuccess"
        @error="handleUploadError" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitUpload">确定上传</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 批量上传对话框 -->
    <el-dialog v-model="batchUploadDialogVisible" title="批量上传文件" width="700px">
      <div class="upload-form">
        <el-form :model="batchUploadForm" label-width="100px">
          <el-form-item label="业务类型">
            <el-input v-model="batchUploadForm.businessType" placeholder="可选：关联业务类型" />
          </el-form-item>
          <el-form-item label="业务ID">
            <el-input v-model="batchUploadForm.businessId" placeholder="可选：关联业务ID" />
          </el-form-item>
        </el-form>
      </div>
      <FileUploadComponent
        ref="batchFileUploadRef"
        v-model="batchUploadedFiles"
        :multiple="true"
        :limit="20"
        :upload-source="'admin'"
        :business-type="batchUploadForm.businessType"
        :business-id="batchUploadForm.businessId"
        @success="handleBatchUploadSuccess"
        @error="handleBatchUploadError" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="batchUploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitBatchUpload">确定上传</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 统计信息对话框 -->
    <el-dialog v-model="statsDialogVisible" title="文件统计信息" width="800px">
      <div v-if="statsData" class="stats-container">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-card>
              <template #header>
                <span>基础统计</span>
              </template>
              <div class="stats-item">
                <span>总文件数：</span>
                <span class="stats-value">{{ statsData.totalFiles }}</span>
              </div>
              <div class="stats-item">
                <span>总大小：</span>
                <span class="stats-value">{{ formatFileSize(statsData.totalSize) }}</span>
              </div>
              <div class="stats-item">
                <span>今日上传：</span>
                <span class="stats-value">{{ statsData.todayUploads }}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card>
              <template #header>
                <span>上传来源</span>
              </template>
              <div v-for="item in statsData.sourceStats" :key="item.uploadSource" class="stats-item">
                <span>{{ getUploadSourceName(item.uploadSource) }}：</span>
                <span class="stats-value">{{ item.count }}</span>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card>
              <template #header>
                <span>存储提供商</span>
              </template>
              <div v-for="item in statsData.providerStats" :key="item.storageProvider" class="stats-item">
                <span>{{ item.storageProvider }}：</span>
                <span class="stats-value">{{ item.count }}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>
        <el-card style="margin-top: 20px;">
          <template #header>
            <span>文件类型分布</span>
          </template>
          <div class="type-stats">
            <div v-for="item in statsData.typeStats" :key="item.fileCategory" class="type-item">
              <span>{{ getFileCategoryName(item.fileCategory) }}：</span>
              <span class="stats-value">{{ item.count }}</span>
              <span class="stats-percent">({{ ((item.count / statsData.totalFiles) * 100).toFixed(1) }}%)</span>
            </div>
          </div>
        </el-card>
      </div>
    </el-dialog>

    <!-- 文件详情对话框 -->
    <el-dialog v-model="detailDialogVisible" title="文件详情" width="600px">
      <div v-if="detailData" class="file-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="原始文件名">{{ detailData.originalName }}</el-descriptions-item>
          <el-descriptions-item label="存储文件名">{{ detailData.fileName }}</el-descriptions-item>
          <el-descriptions-item label="文件大小">{{ formatFileSize(detailData.fileSize) }}</el-descriptions-item>
          <el-descriptions-item label="文件类型">{{ detailData.contentType }}</el-descriptions-item>
          <el-descriptions-item label="文件分类">{{ getFileCategoryName(detailData.fileCategory) }}</el-descriptions-item>
          <el-descriptions-item label="文件状态">
            <el-tag :type="getStatusType(detailData.status)">{{ getStatusText(detailData.status) }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="上传来源">{{ getUploadSourceName(detailData.uploadSource) }}</el-descriptions-item>
          <el-descriptions-item label="存储提供商">{{ detailData.storageProvider }}</el-descriptions-item>
          <el-descriptions-item label="存储桶">{{ detailData.bucketName }}</el-descriptions-item>
          <el-descriptions-item label="存储路径">{{ detailData.storagePath }}</el-descriptions-item>
          <el-descriptions-item label="MD5值" :span="2">{{ detailData.fileMd5 }}</el-descriptions-item>
          <el-descriptions-item label="访问URL" :span="2">
            <el-input v-model="detailData.accessUrl" readonly>
              <template #append>
                <el-button @click="copyToClipboard(detailData.accessUrl)">复制</el-button>
              </template>
            </el-input>
          </el-descriptions-item>
          <el-descriptions-item label="缩略图URL" :span="2" v-if="detailData.thumbnailUrl">
            <el-input v-model="detailData.thumbnailUrl" readonly>
              <template #append>
                <el-button @click="copyToClipboard(detailData.thumbnailUrl)">复制</el-button>
              </template>
            </el-input>
          </el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '无' }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import { FileUploadAPI } from "@/api/system/fileUpload";
import FileUploadComponent from '@/components/FileUpload/index.vue';
import { mapGetters } from "vuex";

export default {
  name: 'FileUpload',
  components: {
    FileUploadComponent
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      uploadDialogVisible: false,
      batchUploadDialogVisible: false,
      statsDialogVisible: false,
      detailDialogVisible: false,
      statsData: null,
      detailData: null,
      uploadedFiles: [],
      batchUploadedFiles: [],
      uploadForm: {
        multiple: false,
        limit: 1,
        businessType: '',
        businessId: ''
      },
      batchUploadForm: {
        businessType: '',
        businessId: ''
      },
      option: {
        height: 'auto',
        calcHeight: 210,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        column: [
          {
            label: '文件预览',
            prop: 'filePreview',
            width: 80,
            slot: true,
            search: false
          },
          {
            label: '文件信息',
            prop: 'fileInfo',
            slot: true,
            search: false
          },
          {
            label: '原始文件名',
            prop: 'originalName',
            search: true,
            searchSpan: 12
          },
          {
            label: '文件分类',
            prop: 'fileCategory',
            type: 'select',
            dicData: [
              { label: '图片', value: 'image' },
              { label: '文档', value: 'document' },
              { label: '视频', value: 'video' },
              { label: '音频', value: 'audio' },
              { label: '办公文档', value: 'office' },
              { label: '文本', value: 'text' },
              { label: '其他', value: 'other' }
            ],
            search: true
          },
          {
            label: '文件大小',
            prop: 'fileSize',
            search: false,
            formatter: (row) => this.formatFileSize(row.fileSize)
          },
          {
            label: '文件状态',
            prop: 'fileStatus',
            slot: true,
            search: false
          },
          {
            label: '上传来源',
            prop: 'uploadSource',
            type: 'select',
            dicData: [
              { label: '管理后台', value: 'admin' },
              { label: '小程序', value: 'miniapp' },
              { label: '其他', value: 'other' }
            ],
            search: true
          },
          {
            label: '存储提供商',
            prop: 'storageProvider',
            search: false
          },
          {
            label: '业务关联',
            prop: 'businessInfo',
            slot: true,
            search: false
          },
          {
            label: '上传时间',
            prop: 'createTime',
            type: 'datetime',
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            search: true,
            searchSpan: 12
          },
          {
            label: '上传用户',
            prop: 'createUserName',
            search: true
          }
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.validData(this.permission.file_upload_add, false),
        viewBtn: this.validData(this.permission.file_upload_view, false),
        delBtn: this.validData(this.permission.file_upload_delete, false),
        editBtn: this.validData(this.permission.file_upload_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    rowUpdate(row, index, done, loading) {
      FileUploadAPI.updateFileUpload(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return FileUploadAPI.removeFile(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete(row) {
      this.$confirm("确定要删除这个文件吗？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return FileUploadAPI.removeFile(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "删除成功!"
          });
        });
    },
    handleBatchDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm(`确定要删除选中的 ${this.selectionList.length} 个文件吗？`, {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return FileUploadAPI.removeFile(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "删除成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        FileUploadAPI.getFileUploadDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      FileUploadAPI.getFileUploadPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = Number(data.total);
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    // 上传文件
    handleUpload() {
      this.uploadDialogVisible = true;
      this.uploadedFiles = [];
      this.uploadForm = {
        multiple: false,
        limit: 1,
        businessType: '',
        businessId: ''
      };
    },
    // 批量上传
    handleBatchUpload() {
      this.batchUploadDialogVisible = true;
      this.batchUploadedFiles = [];
      this.batchUploadForm = {
        businessType: '',
        businessId: ''
      };
    },
    // 上传成功
    handleUploadSuccess(fileData) {
      this.$message.success('上传成功');
      this.onLoad(this.page);
    },
    // 上传失败
    handleUploadError(error) {
      this.$message.error('上传失败: ' + error.message);
    },
    // 批量上传成功
    handleBatchUploadSuccess(fileData) {
      this.$message.success('批量上传成功');
      this.onLoad(this.page);
    },
    // 批量上传失败
    handleBatchUploadError(error) {
      this.$message.error('批量上传失败: ' + error.message);
    },
    // 提交上传
    submitUpload() {
      this.uploadDialogVisible = false;
      this.onLoad(this.page);
    },
    // 提交批量上传
    submitBatchUpload() {
      this.batchUploadDialogVisible = false;
      this.onLoad(this.page);
    },
    // 预览文件
    handlePreview(row) {
      if (row.fileCategory === 'image') {
        // 图片预览
        window.open(row.accessUrl);
      } else {
        // 其他文件下载
        window.open(row.accessUrl);
      }
    },
    // 下载文件
    handleDownload(row) {
      const link = document.createElement('a');
      link.href = row.accessUrl;
      link.download = row.originalName;
      link.click();
    },
    // 复制链接
    handleCopyUrl(row) {
      this.copyToClipboard(row.accessUrl);
      this.$message.success('链接已复制到剪贴板');
    },
    // 复制到剪贴板
    copyToClipboard(text) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(text);
      } else {
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);
      }
    },
    // 统计信息
    async handleStats() {
      try {
        const res = await FileUploadAPI.getFileStats();
        if (res.data.code === 200) {
          this.statsData = res.data.data;
          this.statsDialogVisible = true;
        } else {
          this.$message.error(res.data.msg || '获取统计信息失败');
        }
      } catch (error) {
        this.$message.error('获取统计信息失败');
      }
    },
    // 清理过期文件
    async handleCleanExpired() {
      try {
        await this.$confirm('确定要清理过期文件吗？此操作不可恢复！', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        });
        
        const res = await FileUploadAPI.cleanExpiredFiles();
        if (res.data.code === 200) {
          this.$message.success('清理过期文件成功');
          this.onLoad(this.page);
        } else {
          this.$message.error(res.data.msg || '清理过期文件失败');
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('清理过期文件失败');
        }
      }
    },
    // 格式化文件大小
    formatFileSize(bytes) {
      if (!bytes || bytes === 0) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    // 获取文件分类名称
    getFileCategoryName(category) {
      const categoryMap = {
        'image': '图片',
        'document': '文档',
        'video': '视频',
        'audio': '音频',
        'office': '办公文档',
        'text': '文本',
        'other': '其他'
      };
      return categoryMap[category] || category;
    },
    // 获取上传来源名称
    getUploadSourceName(source) {
      const sourceMap = {
        'admin': '管理后台',
        'miniapp': '小程序',
        'other': '其他'
      };
      return sourceMap[source] || source;
    },
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        1: 'success',
        0: 'warning',
        '-1': 'danger'
      };
      return statusMap[status] || 'info';
    },
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        1: '正常',
        0: '处理中',
        '-1': '异常'
      };
      return statusMap[status] || '未知';
    }
  }
};
</script>

<style lang="scss" scoped>
.file-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  
  .file-icon {
    width: 60px;
    height: 60px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: #999;
  }
  
  .image-error {
    width: 60px;
    height: 60px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }
}

.file-info {
  .file-name {
    font-weight: 500;
    margin-bottom: 4px;
    word-break: break-all;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 200px;
  }
  
  .file-meta {
    font-size: 12px;
    color: #999;
    
    span {
      margin-right: 8px;
    }
    
    .file-md5 {
      font-family: monospace;
    }
  }
}

.business-info {
  .business-type {
    font-weight: 500;
    margin-bottom: 2px;
  }
  
  .business-id {
    font-size: 12px;
    color: #999;
  }
}

.no-business {
  color: #999;
  font-size: 12px;
}

.upload-form {
  margin-bottom: 20px;
}

.stats-container {
  .stats-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    
    .stats-value {
      font-weight: 500;
      color: #409eff;
    }
  }
  
  .type-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 10px;
    
    .type-item {
      display: flex;
      align-items: center;
      
      .stats-value {
        font-weight: 500;
        color: #409eff;
        margin: 0 4px;
      }
      
      .stats-percent {
        color: #999;
        font-size: 12px;
      }
    }
  }
}

.file-detail {
  .el-descriptions {
    margin-top: 20px;
  }
}
</style>