# 小程序地区选择功能文档

## 功能概述

本功能为小程序提供了完整的地区选择解决方案，支持省市县乡镇四级地区选择，后台可配置开放的地区，确保用户只能选择指定的地区范围。

## 功能特点

1. **多级地区选择**：支持省、市、县、乡镇、村五级地区选择
2. **后台配置管理**：管理员可以配置哪些地区对小程序开放
3. **精确定位**：支持精确到乡镇级别的地区选择
4. **缓存机制**：支持地区数据本地缓存，提升用户体验
5. **搜索功能**：支持地区名称搜索
6. **位置定位**：支持根据GPS定位获取当前地区

## 后端架构

### 数据库表结构

#### miniapp_region_config 表
```sql
CREATE TABLE `miniapp_region_config` (
  `id` bigint(64) NOT NULL COMMENT '主键',
  `region_code` varchar(20) NOT NULL COMMENT '地区编码',
  `region_name` varchar(100) NOT NULL COMMENT '地区名称',
  `parent_code` varchar(20) DEFAULT NULL COMMENT '父级地区编码',
  `level` int(2) NOT NULL COMMENT '地区层级 1-省 2-市 3-县 4-乡镇 5-村',
  `is_open` int(2) NOT NULL DEFAULT '0' COMMENT '是否开放 0-关闭 1-开放',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
  -- 其他基础字段...
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_region_code` (`region_code`),
  KEY `idx_parent_code` (`parent_code`),
  KEY `idx_level` (`level`),
  KEY `idx_is_open` (`is_open`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小程序地区配置表';
```

### 后端API接口

#### 小程序端接口 (miniapp包)

1. **获取省份列表**
   - URL: `GET /miniapp/region/provinces`
   - 返回: 开放的省份列表

2. **获取城市列表**
   - URL: `GET /miniapp/region/cities?provinceCode={code}`
   - 返回: 指定省份下开放的城市列表

3. **获取区县列表**
   - URL: `GET /miniapp/region/districts?cityCode={code}`
   - 返回: 指定城市下开放的区县列表

4. **获取乡镇列表**
   - URL: `GET /miniapp/region/towns?districtCode={code}`
   - 返回: 指定区县下开放的乡镇列表

5. **获取村列表**
   - URL: `GET /miniapp/region/villages?townCode={code}`
   - 返回: 指定乡镇下开放的村列表

6. **获取完整地址**
   - URL: `GET /miniapp/region/full-address?regionCode={code}`
   - 返回: 指定地区的完整地址信息

7. **搜索地区**
   - URL: `GET /miniapp/region/search?keyword={keyword}`
   - 返回: 匹配关键词的开放地区列表

#### 后台管理接口 (system包)

1. **地区配置列表**
   - URL: `GET /blade-system/miniapp-region-config/list`
   - 功能: 分页查询地区配置

2. **配置地区开放状态**
   - URL: `POST /blade-system/miniapp-region-config/config-open`
   - 功能: 设置指定地区的开放状态

3. **批量配置开放状态**
   - URL: `POST /blade-system/miniapp-region-config/batch-config-open`
   - 功能: 批量设置多个地区的开放状态

4. **同步地区数据**
   - URL: `POST /blade-system/miniapp-region-config/sync-data`
   - 功能: 从系统地区表同步数据到小程序配置表

## 前端使用

### 组件使用

#### region-picker 组件

```xml
<region-picker 
  show="{{showRegionPicker}}"
  max-level="{{4}}"
  default-value="{{defaultRegionCode}}"
  bind:confirm="onRegionConfirm"
  bind:close="onRegionClose">
</region-picker>
```

**属性说明：**
- `show`: 是否显示选择器
- `max-level`: 最大选择层级（1-5）
- `default-value`: 默认选中的地区编码
- `bind:confirm`: 确认选择事件
- `bind:close`: 关闭选择器事件

#### custom-nav 组件集成

```xml
<custom-nav 
  title="页面标题"
  show-location="{{true}}"
  bind:locationChange="onLocationChange">
</custom-nav>
```

### JavaScript 使用

```javascript
// 获取地区服务
const regionService = app.globalData.api.getRegionService();

// 获取省份列表
const provinces = await regionService.getProvinces();

// 获取城市列表
const cities = await regionService.getCities('320000');

// 搜索地区
const searchResult = await regionService.searchRegions('连云港');

// 获取当前位置对应的地区
const currentRegion = await regionService.getCurrentLocationRegion();
```

## 部署步骤

### 1. 数据库初始化

执行 `backend/sql/miniapp_region_config.sql` 文件创建表结构并初始化基础数据。

### 2. 后端部署

确保以下文件已正确部署：
- `MiniappRegionConfig.java` - 实体类
- `IMiniappRegionService.java` - 服务接口
- `MiniappRegionServiceImpl.java` - 服务实现
- `RegionController.java` - 小程序API控制器
- `MiniappRegionConfigController.java` - 后台管理控制器
- `MiniappRegionConfigMapper.java` - 数据访问层

### 3. 前端部署

确保以下文件已正确部署：
- `components/region-picker/` - 地区选择器组件
- `services/regionService.js` - 地区服务
- 更新后的 `components/custom-nav/` - 导航栏组件

### 4. 配置管理

1. 登录后台管理系统
2. 进入地区配置管理页面
3. 执行"同步地区数据"操作
4. 配置需要开放的地区（设置 is_open = 1）

## 使用示例

参考 `weapp/pages/region-demo/` 目录下的演示页面，展示了完整的使用方法。

## 注意事项

1. **数据同步**：首次使用前需要执行数据同步操作
2. **权限配置**：确保小程序有获取位置权限
3. **缓存管理**：地区数据会缓存1小时，可根据需要调整
4. **错误处理**：建议在使用时添加适当的错误处理逻辑
5. **性能优化**：大量地区数据建议分页加载或按需加载

## 扩展功能

1. **地理编码**：可集成第三方地图服务实现经纬度转地区功能
2. **热门地区**：可添加热门地区快速选择功能
3. **历史记录**：可添加用户选择历史记录功能
4. **多语言**：可扩展支持多语言地区名称
