-- 标签字段统一修复脚本
-- 将标签表中的name字段统一为tagName

-- 1. 检查当前表结构
DESCRIBE urb_tag;

-- 2. 如果存在name字段，将其数据迁移到tag_name字段
-- 注意：根据之前的SQL文件，urb_tag表应该已经有tag_name字段，这里是为了确保数据一致性

-- 3. 更新现有数据（如果需要）
-- 如果数据库中还有使用name字段的数据，执行以下SQL：
-- UPDATE urb_tag SET tag_name = name WHERE tag_name IS NULL AND name IS NOT NULL;

-- 4. 验证数据
SELECT id, tag_name, description, sort_order, status FROM urb_tag LIMIT 10;

-- 5. 创建索引（如果不存在）
CREATE INDEX IF NOT EXISTS idx_urb_tag_tag_name ON urb_tag(tag_name);
CREATE INDEX IF NOT EXISTS idx_urb_tag_category_id ON urb_tag(category_id);
CREATE INDEX IF NOT EXISTS idx_urb_tag_enabled ON urb_tag(enabled);

-- 6. 验证索引
SHOW INDEX FROM urb_tag; 