/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.time.LocalDateTime;

/**
 * 用户与机构关联表实体类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@TableName("urb_user_institution")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "用户与机构关联表")
public class UserInstitution extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;
    /**
     * 机构ID
     */
    @Schema(description = "机构ID")
    private Long institutionId;
    /**
     * 成员角色（member/admin/owner）
     */
    @Schema(description = "成员角色（member/admin/owner）")
    private String role;


}
