<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.group.mapper.GroupInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="groupInfoResultMap" type="org.springblade.business.group.entity.GroupInfo">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="group_name" property="groupName"/>
        <result column="group_type" property="groupType"/>
        <result column="group_image" property="groupImage"/>
        <result column="group_weixin" property="groupWeixin"/>
        <result column="group_desc" property="groupDesc"/>
        <result column="region_code" property="regionCode"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectGroupInfoPage" resultMap="groupInfoResultMap">
        select * from urb_group_info where is_deleted = 0
        <if test="groupInfo.groupName != null and groupInfo.groupName != ''">
            and group_name like concat('%',#{groupInfo.groupName},'%')
        </if>
        <if test="groupInfo.groupType != null and groupInfo.groupType != ''">
            and group_type = #{groupInfo.groupType}
        </if>
        <if test="groupInfo.regionCode != null and groupInfo.regionCode != ''">
            and region_code = #{groupInfo.regionCode}
        </if>
        order by sort_order ASC, create_time desc
    </select>
</mapper>
