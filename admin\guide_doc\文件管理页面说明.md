# 系统模块 - 文件管理页面

## 功能概述

文件管理页面是基于OpenAPI文档中的文件上传接口开发的完整文件管理系统，提供了文件的上传、下载、预览、删除等核心功能。

## 主要功能

### 1. 文件列表管理
- **分页查询**：支持分页显示文件列表
- **搜索过滤**：支持按文件名、分类、上传来源、时间等条件搜索
- **文件预览**：支持图片预览和文件类型图标显示
- **文件信息**：显示文件名、大小、扩展名、MD5等信息

### 2. 文件上传功能
- **单文件上传**：支持单个文件上传
- **批量上传**：支持多个文件同时上传（最多20个）
- **业务关联**：支持关联业务类型和业务ID
- **上传进度**：显示上传进度和状态

### 3. 文件操作功能
- **文件预览**：支持图片在线预览，其他文件类型直接下载
- **文件下载**：支持文件下载到本地
- **复制链接**：复制文件访问链接到剪贴板
- **文件删除**：支持单个和批量删除文件

### 4. 统计信息
- **基础统计**：总文件数、总大小、今日上传数
- **来源统计**：按上传来源统计文件数量
- **类型统计**：按文件分类统计数量和占比
- **存储统计**：按存储提供商统计文件数量

### 5. 系统管理
- **清理过期文件**：清理系统中的过期文件
- **文件状态管理**：显示文件处理状态（正常、处理中、异常）

## 技术特性

### 1. 响应式设计
- 适配不同屏幕尺寸
- 移动端友好的操作界面

### 2. 用户体验优化
- 拖拽上传支持
- 文件类型图标显示
- 上传进度提示
- 操作确认对话框

### 3. 数据安全
- 文件MD5校验
- 上传来源记录
- 业务关联追踪

## API接口

### 主要接口
- `GET /blade-system/file-upload/list` - 分页查询文件列表
- `POST /blade-system/file-upload/upload` - 单文件上传
- `POST /blade-system/file-upload/upload-batch` - 批量文件上传
- `POST /blade-system/file-upload/remove` - 删除文件
- `GET /blade-system/file-upload/stats` - 获取统计信息
- `GET /blade-system/file-upload/business` - 根据业务查询文件

### 文件状态
- `1` - 正常
- `0` - 处理中
- `-1` - 异常

### 文件分类
- `image` - 图片
- `document` - 文档
- `video` - 视频
- `audio` - 音频
- `office` - 办公文档
- `text` - 文本
- `other` - 其他

### 上传来源
- `admin` - 管理后台
- `miniapp` - 小程序
- `other` - 其他

## 使用说明

### 1. 上传文件
1. 点击"上传文件"按钮
2. 选择要上传的文件
3. 可选择关联业务类型和业务ID
4. 点击"确定上传"

### 2. 批量上传
1. 点击"批量上传"按钮
2. 选择多个文件（最多20个）
3. 设置业务关联信息
4. 点击"确定上传"

### 3. 文件操作
- **预览**：点击预览按钮查看文件
- **下载**：点击下载按钮保存文件到本地
- **复制链接**：点击复制链接按钮复制文件访问地址
- **删除**：点击删除按钮删除文件

### 4. 查看统计
1. 点击"统计信息"按钮
2. 查看文件统计详情
3. 包括基础统计、来源统计、类型统计等

## 注意事项

1. 文件上传大小限制：单个文件不超过10MB
2. 支持的文件格式：jpg/png/gif/pdf/doc/docx/txt/csv等
3. 删除操作不可恢复，请谨慎操作
4. 清理过期文件操作不可恢复，请确认后再执行

## 依赖组件

- `@/components/FileUpload/index.vue` - 文件上传组件
- `@/api/system/fileUpload.js` - 文件上传API接口
- Element Plus UI组件库 