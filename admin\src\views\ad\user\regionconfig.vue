<template>
  <basic-container>
    <avue-crud
      :option="option"
      :table-loading="loading"
      :data="data"
      v-model:search="search"
      v-model:page="page"
      ref="crud"
      @search-change="searchChange"
      @search-reset="searchReset"
      @on-load="onLoad"
      @row-update="rowUpdate"
      @selection-change="selectionChange"
    >
      <template #menu-left>
        <el-button type="success" @click="handleSync">同步地区数据</el-button>
        <el-button type="primary" @click="handleBatchOpen">批量开放</el-button>
        <el-button type="warning" @click="handleBatchClose">批量关闭</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import { getList, configOpen, syncData, batchConfigOpen } from '@/api/ad/regionconfig'
import { ElMessage, ElMessageBox } from 'element-plus'

export default {
  data() {
    return {
      loading: false,
      data: [],
      search: { regionName: undefined, level: undefined },
      page: { pageSize: 10, currentPage: 1, total: 0 },
      selectionList: [],
      option: {
        height: '100%', // 让表格高度自适应父容器
        calcHeight: 0,  // 不再额外减去固定高度
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        addBtn: false,
        selection: true,
        column: [
          { label: '地区编码', prop: 'regionCode', width: 120, search: false },
          { label: '地区名称', prop: 'regionName', search: true, rules: [{ required: true, message: '请输入地区名称', trigger: 'blur' }] },
          { label: '层级', prop: 'level', type: 'select', dicData: [
            { label: '省', value: 1 }, { label: '市', value: 2 }, { label: '区/县', value: 3 }
          ], search: true },
          { label: '父级编码', prop: 'parentCode', width: 120, search: false },
          { label: '开放状态', prop: 'isOpen', type: 'switch',
          dicData: [
            { label: '开放', value: 1 }, { label: '关闭', value: 0 }
          ],
          activeValue: 1,
          inactiveValue: 0,
          width: 100, search: true }
        ]
      }
    }
  },
  methods: {
    onLoad(pageObj, params = {}) {
      this.loading = true
      getList(pageObj.currentPage, pageObj.pageSize, { ...this.search, ...params }).then(res => {
        const d = res.data.data
        this.page.total = Number(d.total)
        this.data = d.records
        this.loading = false
      })
    },
    searchChange(params, done) {
      Object.assign(this.search, params)
      this.page.currentPage = 1
      if (params.regionName === '') delete params.regionName
      this.onLoad(this.page, params)
      done && done()
    },
    searchReset(done) {
      this.search.regionName = ''
      this.search.level = undefined
      this.page.currentPage = 1
      this.onLoad(this.page)
      done && done()
    },
    rowUpdate(row, index, done, loading) {
      done()
    },
    selectionChange(selection) {
      this.selectionList = selection
    },
    handleConfigOpen(row, val) {
      if (!row.regionCode) {
        ElMessage.error('地区编码不能为空')
        row.isOpen = val === 1 ? 0 : 1
        return
      }
      configOpen(row.regionCode, val)
        .then(() => ElMessage.success('设置成功'))
        .catch(() => {
          ElMessage.error('设置失败')
          row.isOpen = val === 1 ? 0 : 1
        })
    },
    handleSync() {
      ElMessageBox.confirm('确定要同步地区数据吗？', '提示', { type: 'warning' })
        .then(() => syncData())
        .then(() => {
          ElMessage.success('同步成功')
          this.onLoad(this.page)
        })
        .catch(() => {})
    },
    handleBatchOpen() {
      if (this.selectionList.length === 0) {
        ElMessage.warning('请选择至少一条数据')
        return
      }
      const regionCodes = this.selectionList.map(item => item.regionCode)
      batchConfigOpen(regionCodes, 1)
        .then(() => {
          ElMessage.success('批量开放成功')
          this.onLoad(this.page)
        })
        .catch(() => ElMessage.error('批量开放失败'))
    },
    handleBatchClose() {
      if (this.selectionList.length === 0) {
        ElMessage.warning('请选择至少一条数据')
        return
      }
      const regionCodes = this.selectionList.map(item => item.regionCode)
      batchConfigOpen(regionCodes, 0)
        .then(() => {
          ElMessage.success('批量关闭成功')
          this.onLoad(this.page)
        })
        .catch(() => ElMessage.error('批量关闭失败'))
    }
  }
}
</script>

<style scoped>
</style> 
