/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.group.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 群信息表实体类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Data
@TableName("urb_group_info")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "群信息表")
public class GroupInfo extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 群名称
     */
    @Schema(description = "群名称")
    private String groupName;
    /**
     * 群类型
     */
    @Schema(description = "群类型")
    private String groupType;
    /**
     * 群图片
     */
    @Schema(description = "群图片")
    private String groupImage;
    /**
     * 微信链接
     */
    @Schema(description = "微信链接")
    private String groupWeixin;
    /**
     * 群描述信息
     */
    @Schema(description = "群描述信息")
    private String groupDesc;
    /**
     * 地区编号
     */
    @Schema(description = "地区编号")
    private String regionCode;


	/**
	 * 排序
	 */
	@Schema(description = "排序")
	private Integer sortOrder;
}
