/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.group.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.group.entity.GroupInfo;
import org.springblade.business.group.vo.GroupInfoVO;
import org.springblade.business.group.wrapper.GroupInfoWrapper;
import org.springblade.business.group.service.IGroupInfoService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 群信息表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/groupinfo")
@io.swagger.v3.oas.annotations.tags.Tag(name = "群信息表", description = "群信息表接口")
public class GroupInfoController extends BladeController {

	private IGroupInfoService groupInfoService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入groupInfo")
	public R<GroupInfoVO> detail(GroupInfo groupInfo) {
		GroupInfo detail = groupInfoService.getOne(Condition.getQueryWrapper(groupInfo));
		return R.data(GroupInfoWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 群信息表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入groupInfo")
	public R<IPage<GroupInfoVO>> list(GroupInfo groupInfo, Query query) {
		//根据排序字段正序排序，创建时间倒叙
		query.setAscs("sort_order");
		query.setDescs("create_time");
		IPage<GroupInfo> pages = groupInfoService.page(Condition.getPage(query), Condition.getQueryWrapper(groupInfo));
		return R.data(GroupInfoWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 群信息表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入groupInfo")
	public R<IPage<GroupInfoVO>> page(GroupInfoVO groupInfo, Query query) {
		IPage<GroupInfoVO> pages = groupInfoService.selectGroupInfoPage(Condition.getPage(query), groupInfo);
		return R.data(pages);
	}

	/**
	 * 新增 群信息表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入groupInfo")
	public R save(@Valid @RequestBody GroupInfo groupInfo) {
		return R.status(groupInfoService.save(groupInfo));
	}

	/**
	 * 修改 群信息表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入groupInfo")
	public R update(@Valid @RequestBody GroupInfo groupInfo) {
		return R.status(groupInfoService.updateById(groupInfo));
	}

	/**
	 * 新增或修改 群信息表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入groupInfo")
	public R submit(@Valid @RequestBody GroupInfo groupInfo) {
		return R.status(groupInfoService.saveOrUpdate(groupInfo));
	}


	/**
	 * 删除 群信息表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(groupInfoService.deleteLogic(Func.toLongList(ids)));
	}

}
