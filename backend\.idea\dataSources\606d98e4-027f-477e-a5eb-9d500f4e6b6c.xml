<?xml version="1.0" encoding="UTF-8"?>
<dataSource name="@14.103.174.210">
  <database-model serializer="dbm" dbms="MYSQL" family-id="MYSQL" format-version="4.49">
    <root id="1">
      <DefaultCasing>lower/lower</DefaultCasing>
      <DefaultEngine>InnoDB</DefaultEngine>
      <DefaultTmpEngine>InnoDB</DefaultTmpEngine>
      <Grants>adb-management|schema||adb-management||ALTER|G
adb-management|schema||adb-management||ALTER ROUTINE|G
adb-management|schema||adb-management||CREATE|G
adb-management|schema||adb-management||CREATE ROUTINE|G
adb-management|schema||adb-management||CREATE TEMPORARY TABLES|G
adb-management|schema||adb-management||CREATE VIEW|G
adb-management|schema||adb-management||DELETE|G
adb-management|schema||adb-management||DROP|G
adb-management|schema||adb-management||EVENT|G
adb-management|schema||adb-management||EXECUTE|G
adb-management|schema||adb-management||INDEX|G
adb-management|schema||adb-management||INSERT|G
adb-management|schema||adb-management||LOCK TABLES|G
adb-management|schema||adb-management||REFERENCES|G
adb-management|schema||adb-management||SELECT|G
adb-management|schema||adb-management||SHOW VIEW|G
adb-management|schema||adb-management||TRIGGER|G
adb-management|schema||adb-management||UPDATE|G
adb-management|schema||adb-management||grant option|G</Grants>
      <ServerVersion>8.0.41</ServerVersion>
    </root>
    <collation id="2" parent="1" name="armscii8_general_ci">
      <Charset>armscii8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="3" parent="1" name="armscii8_bin">
      <Charset>armscii8</Charset>
    </collation>
    <collation id="4" parent="1" name="ascii_general_ci">
      <Charset>ascii</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="5" parent="1" name="ascii_bin">
      <Charset>ascii</Charset>
    </collation>
    <collation id="6" parent="1" name="big5_chinese_ci">
      <Charset>big5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="7" parent="1" name="big5_bin">
      <Charset>big5</Charset>
    </collation>
    <collation id="8" parent="1" name="binary">
      <Charset>binary</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="9" parent="1" name="cp1250_general_ci">
      <Charset>cp1250</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="10" parent="1" name="cp1250_czech_cs">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="11" parent="1" name="cp1250_croatian_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="12" parent="1" name="cp1250_bin">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="13" parent="1" name="cp1250_polish_ci">
      <Charset>cp1250</Charset>
    </collation>
    <collation id="14" parent="1" name="cp1251_bulgarian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="15" parent="1" name="cp1251_ukrainian_ci">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="16" parent="1" name="cp1251_bin">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="17" parent="1" name="cp1251_general_ci">
      <Charset>cp1251</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="18" parent="1" name="cp1251_general_cs">
      <Charset>cp1251</Charset>
    </collation>
    <collation id="19" parent="1" name="cp1256_general_ci">
      <Charset>cp1256</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="20" parent="1" name="cp1256_bin">
      <Charset>cp1256</Charset>
    </collation>
    <collation id="21" parent="1" name="cp1257_lithuanian_ci">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="22" parent="1" name="cp1257_bin">
      <Charset>cp1257</Charset>
    </collation>
    <collation id="23" parent="1" name="cp1257_general_ci">
      <Charset>cp1257</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="24" parent="1" name="cp850_general_ci">
      <Charset>cp850</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="25" parent="1" name="cp850_bin">
      <Charset>cp850</Charset>
    </collation>
    <collation id="26" parent="1" name="cp852_general_ci">
      <Charset>cp852</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="27" parent="1" name="cp852_bin">
      <Charset>cp852</Charset>
    </collation>
    <collation id="28" parent="1" name="cp866_general_ci">
      <Charset>cp866</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="29" parent="1" name="cp866_bin">
      <Charset>cp866</Charset>
    </collation>
    <collation id="30" parent="1" name="cp932_japanese_ci">
      <Charset>cp932</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="31" parent="1" name="cp932_bin">
      <Charset>cp932</Charset>
    </collation>
    <collation id="32" parent="1" name="dec8_swedish_ci">
      <Charset>dec8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="33" parent="1" name="dec8_bin">
      <Charset>dec8</Charset>
    </collation>
    <collation id="34" parent="1" name="eucjpms_japanese_ci">
      <Charset>eucjpms</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="35" parent="1" name="eucjpms_bin">
      <Charset>eucjpms</Charset>
    </collation>
    <collation id="36" parent="1" name="euckr_korean_ci">
      <Charset>euckr</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="37" parent="1" name="euckr_bin">
      <Charset>euckr</Charset>
    </collation>
    <collation id="38" parent="1" name="gb18030_chinese_ci">
      <Charset>gb18030</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="39" parent="1" name="gb18030_bin">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="40" parent="1" name="gb18030_unicode_520_ci">
      <Charset>gb18030</Charset>
    </collation>
    <collation id="41" parent="1" name="gb2312_chinese_ci">
      <Charset>gb2312</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="42" parent="1" name="gb2312_bin">
      <Charset>gb2312</Charset>
    </collation>
    <collation id="43" parent="1" name="gbk_chinese_ci">
      <Charset>gbk</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="44" parent="1" name="gbk_bin">
      <Charset>gbk</Charset>
    </collation>
    <collation id="45" parent="1" name="geostd8_general_ci">
      <Charset>geostd8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="46" parent="1" name="geostd8_bin">
      <Charset>geostd8</Charset>
    </collation>
    <collation id="47" parent="1" name="greek_general_ci">
      <Charset>greek</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="48" parent="1" name="greek_bin">
      <Charset>greek</Charset>
    </collation>
    <collation id="49" parent="1" name="hebrew_general_ci">
      <Charset>hebrew</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="50" parent="1" name="hebrew_bin">
      <Charset>hebrew</Charset>
    </collation>
    <collation id="51" parent="1" name="hp8_english_ci">
      <Charset>hp8</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="52" parent="1" name="hp8_bin">
      <Charset>hp8</Charset>
    </collation>
    <collation id="53" parent="1" name="keybcs2_general_ci">
      <Charset>keybcs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="54" parent="1" name="keybcs2_bin">
      <Charset>keybcs2</Charset>
    </collation>
    <collation id="55" parent="1" name="koi8r_general_ci">
      <Charset>koi8r</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="56" parent="1" name="koi8r_bin">
      <Charset>koi8r</Charset>
    </collation>
    <collation id="57" parent="1" name="koi8u_general_ci">
      <Charset>koi8u</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="58" parent="1" name="koi8u_bin">
      <Charset>koi8u</Charset>
    </collation>
    <collation id="59" parent="1" name="latin1_german1_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="60" parent="1" name="latin1_swedish_ci">
      <Charset>latin1</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="61" parent="1" name="latin1_danish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="62" parent="1" name="latin1_german2_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="63" parent="1" name="latin1_bin">
      <Charset>latin1</Charset>
    </collation>
    <collation id="64" parent="1" name="latin1_general_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="65" parent="1" name="latin1_general_cs">
      <Charset>latin1</Charset>
    </collation>
    <collation id="66" parent="1" name="latin1_spanish_ci">
      <Charset>latin1</Charset>
    </collation>
    <collation id="67" parent="1" name="latin2_czech_cs">
      <Charset>latin2</Charset>
    </collation>
    <collation id="68" parent="1" name="latin2_general_ci">
      <Charset>latin2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="69" parent="1" name="latin2_hungarian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="70" parent="1" name="latin2_croatian_ci">
      <Charset>latin2</Charset>
    </collation>
    <collation id="71" parent="1" name="latin2_bin">
      <Charset>latin2</Charset>
    </collation>
    <collation id="72" parent="1" name="latin5_turkish_ci">
      <Charset>latin5</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="73" parent="1" name="latin5_bin">
      <Charset>latin5</Charset>
    </collation>
    <collation id="74" parent="1" name="latin7_estonian_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="75" parent="1" name="latin7_general_ci">
      <Charset>latin7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="76" parent="1" name="latin7_general_cs">
      <Charset>latin7</Charset>
    </collation>
    <collation id="77" parent="1" name="latin7_bin">
      <Charset>latin7</Charset>
    </collation>
    <collation id="78" parent="1" name="macce_general_ci">
      <Charset>macce</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="79" parent="1" name="macce_bin">
      <Charset>macce</Charset>
    </collation>
    <collation id="80" parent="1" name="macroman_general_ci">
      <Charset>macroman</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="81" parent="1" name="macroman_bin">
      <Charset>macroman</Charset>
    </collation>
    <collation id="82" parent="1" name="sjis_japanese_ci">
      <Charset>sjis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="83" parent="1" name="sjis_bin">
      <Charset>sjis</Charset>
    </collation>
    <collation id="84" parent="1" name="swe7_swedish_ci">
      <Charset>swe7</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="85" parent="1" name="swe7_bin">
      <Charset>swe7</Charset>
    </collation>
    <collation id="86" parent="1" name="tis620_thai_ci">
      <Charset>tis620</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="87" parent="1" name="tis620_bin">
      <Charset>tis620</Charset>
    </collation>
    <collation id="88" parent="1" name="ucs2_general_ci">
      <Charset>ucs2</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="89" parent="1" name="ucs2_bin">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="90" parent="1" name="ucs2_unicode_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="91" parent="1" name="ucs2_icelandic_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="92" parent="1" name="ucs2_latvian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="93" parent="1" name="ucs2_romanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="94" parent="1" name="ucs2_slovenian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="95" parent="1" name="ucs2_polish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="96" parent="1" name="ucs2_estonian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="97" parent="1" name="ucs2_spanish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="98" parent="1" name="ucs2_swedish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="99" parent="1" name="ucs2_turkish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="100" parent="1" name="ucs2_czech_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="101" parent="1" name="ucs2_danish_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="102" parent="1" name="ucs2_lithuanian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="103" parent="1" name="ucs2_slovak_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="104" parent="1" name="ucs2_spanish2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="105" parent="1" name="ucs2_roman_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="106" parent="1" name="ucs2_persian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="107" parent="1" name="ucs2_esperanto_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="108" parent="1" name="ucs2_hungarian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="109" parent="1" name="ucs2_sinhala_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="110" parent="1" name="ucs2_german2_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="111" parent="1" name="ucs2_croatian_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="112" parent="1" name="ucs2_unicode_520_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="113" parent="1" name="ucs2_vietnamese_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="114" parent="1" name="ucs2_general_mysql500_ci">
      <Charset>ucs2</Charset>
    </collation>
    <collation id="115" parent="1" name="ujis_japanese_ci">
      <Charset>ujis</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="116" parent="1" name="ujis_bin">
      <Charset>ujis</Charset>
    </collation>
    <collation id="117" parent="1" name="utf16_general_ci">
      <Charset>utf16</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="118" parent="1" name="utf16_bin">
      <Charset>utf16</Charset>
    </collation>
    <collation id="119" parent="1" name="utf16_unicode_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="120" parent="1" name="utf16_icelandic_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="121" parent="1" name="utf16_latvian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="122" parent="1" name="utf16_romanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="123" parent="1" name="utf16_slovenian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="124" parent="1" name="utf16_polish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="125" parent="1" name="utf16_estonian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="126" parent="1" name="utf16_spanish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="127" parent="1" name="utf16_swedish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="128" parent="1" name="utf16_turkish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="129" parent="1" name="utf16_czech_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="130" parent="1" name="utf16_danish_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="131" parent="1" name="utf16_lithuanian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="132" parent="1" name="utf16_slovak_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="133" parent="1" name="utf16_spanish2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="134" parent="1" name="utf16_roman_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="135" parent="1" name="utf16_persian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="136" parent="1" name="utf16_esperanto_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="137" parent="1" name="utf16_hungarian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="138" parent="1" name="utf16_sinhala_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="139" parent="1" name="utf16_german2_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="140" parent="1" name="utf16_croatian_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="141" parent="1" name="utf16_unicode_520_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="142" parent="1" name="utf16_vietnamese_ci">
      <Charset>utf16</Charset>
    </collation>
    <collation id="143" parent="1" name="utf16le_general_ci">
      <Charset>utf16le</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="144" parent="1" name="utf16le_bin">
      <Charset>utf16le</Charset>
    </collation>
    <collation id="145" parent="1" name="utf32_general_ci">
      <Charset>utf32</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="146" parent="1" name="utf32_bin">
      <Charset>utf32</Charset>
    </collation>
    <collation id="147" parent="1" name="utf32_unicode_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="148" parent="1" name="utf32_icelandic_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="149" parent="1" name="utf32_latvian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="150" parent="1" name="utf32_romanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="151" parent="1" name="utf32_slovenian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="152" parent="1" name="utf32_polish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="153" parent="1" name="utf32_estonian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="154" parent="1" name="utf32_spanish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="155" parent="1" name="utf32_swedish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="156" parent="1" name="utf32_turkish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="157" parent="1" name="utf32_czech_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="158" parent="1" name="utf32_danish_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="159" parent="1" name="utf32_lithuanian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="160" parent="1" name="utf32_slovak_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="161" parent="1" name="utf32_spanish2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="162" parent="1" name="utf32_roman_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="163" parent="1" name="utf32_persian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="164" parent="1" name="utf32_esperanto_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="165" parent="1" name="utf32_hungarian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="166" parent="1" name="utf32_sinhala_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="167" parent="1" name="utf32_german2_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="168" parent="1" name="utf32_croatian_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="169" parent="1" name="utf32_unicode_520_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="170" parent="1" name="utf32_vietnamese_ci">
      <Charset>utf32</Charset>
    </collation>
    <collation id="171" parent="1" name="utf8mb3_general_ci">
      <Charset>utf8mb3</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="172" parent="1" name="utf8mb3_tolower_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="173" parent="1" name="utf8mb3_bin">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="174" parent="1" name="utf8mb3_unicode_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="175" parent="1" name="utf8mb3_icelandic_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="176" parent="1" name="utf8mb3_latvian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="177" parent="1" name="utf8mb3_romanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="178" parent="1" name="utf8mb3_slovenian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="179" parent="1" name="utf8mb3_polish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="180" parent="1" name="utf8mb3_estonian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="181" parent="1" name="utf8mb3_spanish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="182" parent="1" name="utf8mb3_swedish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="183" parent="1" name="utf8mb3_turkish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="184" parent="1" name="utf8mb3_czech_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="185" parent="1" name="utf8mb3_danish_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="186" parent="1" name="utf8mb3_lithuanian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="187" parent="1" name="utf8mb3_slovak_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="188" parent="1" name="utf8mb3_spanish2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="189" parent="1" name="utf8mb3_roman_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="190" parent="1" name="utf8mb3_persian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="191" parent="1" name="utf8mb3_esperanto_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="192" parent="1" name="utf8mb3_hungarian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="193" parent="1" name="utf8mb3_sinhala_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="194" parent="1" name="utf8mb3_german2_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="195" parent="1" name="utf8mb3_croatian_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="196" parent="1" name="utf8mb3_unicode_520_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="197" parent="1" name="utf8mb3_vietnamese_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="198" parent="1" name="utf8mb3_general_mysql500_ci">
      <Charset>utf8mb3</Charset>
    </collation>
    <collation id="199" parent="1" name="utf8mb4_general_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="200" parent="1" name="utf8mb4_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="201" parent="1" name="utf8mb4_unicode_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="202" parent="1" name="utf8mb4_icelandic_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="203" parent="1" name="utf8mb4_latvian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="204" parent="1" name="utf8mb4_romanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="205" parent="1" name="utf8mb4_slovenian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="206" parent="1" name="utf8mb4_polish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="207" parent="1" name="utf8mb4_estonian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="208" parent="1" name="utf8mb4_spanish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="209" parent="1" name="utf8mb4_swedish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="210" parent="1" name="utf8mb4_turkish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="211" parent="1" name="utf8mb4_czech_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="212" parent="1" name="utf8mb4_danish_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="213" parent="1" name="utf8mb4_lithuanian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="214" parent="1" name="utf8mb4_slovak_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="215" parent="1" name="utf8mb4_spanish2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="216" parent="1" name="utf8mb4_roman_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="217" parent="1" name="utf8mb4_persian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="218" parent="1" name="utf8mb4_esperanto_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="219" parent="1" name="utf8mb4_hungarian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="220" parent="1" name="utf8mb4_sinhala_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="221" parent="1" name="utf8mb4_german2_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="222" parent="1" name="utf8mb4_croatian_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="223" parent="1" name="utf8mb4_unicode_520_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="224" parent="1" name="utf8mb4_vietnamese_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="225" parent="1" name="utf8mb4_0900_ai_ci">
      <Charset>utf8mb4</Charset>
      <DefaultForCharset>1</DefaultForCharset>
    </collation>
    <collation id="226" parent="1" name="utf8mb4_de_pb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="227" parent="1" name="utf8mb4_is_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="228" parent="1" name="utf8mb4_lv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="229" parent="1" name="utf8mb4_ro_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="230" parent="1" name="utf8mb4_sl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="231" parent="1" name="utf8mb4_pl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="232" parent="1" name="utf8mb4_et_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="233" parent="1" name="utf8mb4_es_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="234" parent="1" name="utf8mb4_sv_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="235" parent="1" name="utf8mb4_tr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="236" parent="1" name="utf8mb4_cs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="237" parent="1" name="utf8mb4_da_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="238" parent="1" name="utf8mb4_lt_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="239" parent="1" name="utf8mb4_sk_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="240" parent="1" name="utf8mb4_es_trad_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="241" parent="1" name="utf8mb4_la_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="242" parent="1" name="utf8mb4_eo_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="243" parent="1" name="utf8mb4_hu_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="244" parent="1" name="utf8mb4_hr_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="245" parent="1" name="utf8mb4_vi_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="246" parent="1" name="utf8mb4_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="247" parent="1" name="utf8mb4_de_pb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="248" parent="1" name="utf8mb4_is_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="249" parent="1" name="utf8mb4_lv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="250" parent="1" name="utf8mb4_ro_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="251" parent="1" name="utf8mb4_sl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="252" parent="1" name="utf8mb4_pl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="253" parent="1" name="utf8mb4_et_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="254" parent="1" name="utf8mb4_es_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="255" parent="1" name="utf8mb4_sv_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="256" parent="1" name="utf8mb4_tr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="257" parent="1" name="utf8mb4_cs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="258" parent="1" name="utf8mb4_da_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="259" parent="1" name="utf8mb4_lt_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="260" parent="1" name="utf8mb4_sk_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="261" parent="1" name="utf8mb4_es_trad_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="262" parent="1" name="utf8mb4_la_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="263" parent="1" name="utf8mb4_eo_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="264" parent="1" name="utf8mb4_hu_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="265" parent="1" name="utf8mb4_hr_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="266" parent="1" name="utf8mb4_vi_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="267" parent="1" name="utf8mb4_ja_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="268" parent="1" name="utf8mb4_ja_0900_as_cs_ks">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="269" parent="1" name="utf8mb4_0900_as_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="270" parent="1" name="utf8mb4_ru_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="271" parent="1" name="utf8mb4_ru_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="272" parent="1" name="utf8mb4_zh_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="273" parent="1" name="utf8mb4_0900_bin">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="274" parent="1" name="utf8mb4_nb_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="275" parent="1" name="utf8mb4_nb_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="276" parent="1" name="utf8mb4_nn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="277" parent="1" name="utf8mb4_nn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="278" parent="1" name="utf8mb4_sr_latn_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="279" parent="1" name="utf8mb4_sr_latn_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="280" parent="1" name="utf8mb4_bs_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="281" parent="1" name="utf8mb4_bs_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="282" parent="1" name="utf8mb4_bg_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="283" parent="1" name="utf8mb4_bg_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="284" parent="1" name="utf8mb4_gl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="285" parent="1" name="utf8mb4_gl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="286" parent="1" name="utf8mb4_mn_cyrl_0900_ai_ci">
      <Charset>utf8mb4</Charset>
    </collation>
    <collation id="287" parent="1" name="utf8mb4_mn_cyrl_0900_as_cs">
      <Charset>utf8mb4</Charset>
    </collation>
    <schema id="288" parent="1" name="information_schema">
      <CollationName>utf8mb3_general_ci</CollationName>
    </schema>
    <schema id="289" parent="1" name="performance_schema">
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </schema>
    <schema id="290" parent="1" name="adb-management">
      <IntrospectionTimestamp>2025-07-20.23:31:47</IntrospectionTimestamp>
      <LocalIntrospectionTimestamp>2025-07-20.07:31:47</LocalIntrospectionTimestamp>
      <CollationName>utf8mb4_general_ci</CollationName>
    </schema>
    <user id="291" parent="1" name="adb-management"/>
    <table id="292" parent="290" name="blade_client">
      <Comment>客户端表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="293" parent="290" name="blade_code">
      <Comment>代码生成表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="294" parent="290" name="blade_datasource">
      <Comment>数据源配置表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="295" parent="290" name="blade_dept">
      <Comment>部门表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="296" parent="290" name="blade_dict">
      <Comment>字典表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="297" parent="290" name="blade_file_upload">
      <Comment>文件上传表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="298" parent="290" name="blade_log_api">
      <Comment>接口日志表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="299" parent="290" name="blade_log_error">
      <Comment>错误日志表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="300" parent="290" name="blade_log_usual">
      <Comment>通用日志表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="301" parent="290" name="blade_menu">
      <Comment>菜单表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="302" parent="290" name="blade_notice">
      <Comment>通知公告表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="303" parent="290" name="blade_param">
      <Comment>参数表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="304" parent="290" name="blade_post">
      <Comment>岗位表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="305" parent="290" name="blade_region">
      <Comment>行政区划表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="306" parent="290" name="blade_report_file">
      <Comment>报表文件表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="307" parent="290" name="blade_role">
      <Comment>角色表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="308" parent="290" name="blade_role_menu">
      <Comment>角色菜单表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="309" parent="290" name="blade_role_scope">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="310" parent="290" name="blade_scope_data">
      <Comment>数据权限表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="311" parent="290" name="blade_tenant">
      <Comment>租户表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="312" parent="290" name="blade_user">
      <Comment>用户表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="313" parent="290" name="blade_user_oauth">
      <Comment>用户第三方认证表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_general_ci</CollationName>
    </table>
    <table id="314" parent="290" name="urb_audit_post">
      <Comment>审核日志</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="315" parent="290" name="urb_audit_rule">
      <Comment>审核规则</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="316" parent="290" name="urb_call_log">
      <Comment>电话记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="317" parent="290" name="urb_category">
      <Comment>广告分类</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="318" parent="290" name="urb_category_tag">
      <Comment>信息贴-标签关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="319" parent="290" name="urb_contact">
      <Comment>联系人</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="320" parent="290" name="urb_favorite">
      <Comment>收藏记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="321" parent="290" name="urb_feedback">
      <Comment>用户反馈</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="322" parent="290" name="urb_feedback_helpful">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="323" parent="290" name="urb_feedback_tag">
      <Comment>反馈标签</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="324" parent="290" name="urb_feedback_tag_relation">
      <Comment>反馈-标签关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="325" parent="290" name="urb_like">
      <Comment>点赞记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="326" parent="290" name="urb_miniapp_user">
      <Comment>小程序用户表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="327" parent="290" name="urb_post">
      <Comment>百事通信息贴</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="328" parent="290" name="urb_post_category">
      <Comment>信息贴-分类关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="329" parent="290" name="urb_post_favorite">
      <Comment>帖子收藏表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="330" parent="290" name="urb_post_like">
      <Comment>帖子点赞表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="331" parent="290" name="urb_report">
      <Comment>举报记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="332" parent="290" name="urb_report_tag">
      <Comment>举报标签</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="333" parent="290" name="urb_report_tag_relation">
      <Comment>举报-标签关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="334" parent="290" name="urb_tag">
      <Comment>信息标签</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="335" parent="290" name="urb_user">
      <Comment>用户信息</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="336" parent="290" name="urb_user_contact">
      <Comment>用户-联系人关联表</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="337" parent="290" name="urb_view_log">
      <Comment>浏览记录</Comment>
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <table id="338" parent="290" name="url_post_contact">
      <Engine>InnoDB</Engine>
      <CollationName>utf8mb4_0900_ai_ci</CollationName>
    </table>
    <column id="339" parent="292" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="340" parent="292" name="client_id">
      <Comment>客户端id</Comment>
      <DasType>varchar(48)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="341" parent="292" name="client_secret">
      <Comment>客户端密钥</Comment>
      <DasType>varchar(256)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="342" parent="292" name="resource_ids">
      <Comment>资源集合</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="343" parent="292" name="scope">
      <Comment>授权范围</Comment>
      <DasType>varchar(256)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>5</Position>
    </column>
    <column id="344" parent="292" name="authorized_grant_types">
      <Comment>授权类型</Comment>
      <DasType>varchar(256)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>6</Position>
    </column>
    <column id="345" parent="292" name="web_server_redirect_uri">
      <Comment>回调地址</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="346" parent="292" name="authorities">
      <Comment>权限</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="347" parent="292" name="access_token_validity">
      <Comment>令牌过期秒数</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>9</Position>
    </column>
    <column id="348" parent="292" name="refresh_token_validity">
      <Comment>刷新令牌过期秒数</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>10</Position>
    </column>
    <column id="349" parent="292" name="additional_information">
      <Comment>附加说明</Comment>
      <DasType>varchar(4096)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="350" parent="292" name="autoapprove">
      <Comment>自动授权</Comment>
      <DasType>varchar(256)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="351" parent="292" name="create_user">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="352" parent="292" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="353" parent="292" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="354" parent="292" name="update_user">
      <Comment>修改人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="355" parent="292" name="update_time">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="356" parent="292" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>18</Position>
    </column>
    <column id="357" parent="292" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <NotNull>1</NotNull>
      <Position>19</Position>
    </column>
    <index id="358" parent="292" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="359" parent="292" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="360" parent="293" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="361" parent="293" name="datasource_id">
      <Comment>数据源主键</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="362" parent="293" name="service_name">
      <Comment>服务名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="363" parent="293" name="code_name">
      <Comment>模块名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="364" parent="293" name="table_name">
      <Comment>表名</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="365" parent="293" name="table_prefix">
      <Comment>表前缀</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="366" parent="293" name="pk_name">
      <Comment>主键名</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="367" parent="293" name="package_name">
      <Comment>后端包名</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="368" parent="293" name="base_mode">
      <Comment>基础业务模式</Comment>
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="369" parent="293" name="wrap_mode">
      <Comment>包装器模式</Comment>
      <DasType>int|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="370" parent="293" name="api_path">
      <Comment>后端路径</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="371" parent="293" name="web_path">
      <Comment>前端路径</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="372" parent="293" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
    </column>
    <index id="373" parent="293" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="374" parent="293" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="375" parent="294" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="376" parent="294" name="name">
      <Comment>名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="377" parent="294" name="driver_class">
      <Comment>驱动类</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="378" parent="294" name="url">
      <Comment>连接地址</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="379" parent="294" name="username">
      <Comment>用户名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="380" parent="294" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="381" parent="294" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="382" parent="294" name="create_user">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="383" parent="294" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="384" parent="294" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="385" parent="294" name="update_user">
      <Comment>修改人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="386" parent="294" name="update_time">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="387" parent="294" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="388" parent="294" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="389" parent="294" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="390" parent="294" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="391" parent="295" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="392" parent="295" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="393" parent="295" name="parent_id">
      <Comment>父主键</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="394" parent="295" name="ancestors">
      <Comment>祖级列表</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="395" parent="295" name="dept_name">
      <Comment>部门名</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="396" parent="295" name="full_name">
      <Comment>部门全称</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="397" parent="295" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="398" parent="295" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="399" parent="295" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>9</Position>
    </column>
    <index id="400" parent="295" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="401" parent="295" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="402" parent="296" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="403" parent="296" name="parent_id">
      <Comment>父主键</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="404" parent="296" name="code">
      <Comment>字典码</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="405" parent="296" name="dict_key">
      <Comment>字典值</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="406" parent="296" name="dict_value">
      <Comment>字典名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="407" parent="296" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="408" parent="296" name="remark">
      <Comment>字典备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="409" parent="296" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>8</Position>
    </column>
    <index id="410" parent="296" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="411" parent="296" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="412" parent="297" name="id">
      <AutoIncrement>1938009179356086273</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="413" parent="297" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="414" parent="297" name="create_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="415" parent="297" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="416" parent="297" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="417" parent="297" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="418" parent="297" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="419" parent="297" name="update_time">
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="420" parent="297" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="421" parent="297" name="original_name">
      <Comment>原始文件名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="422" parent="297" name="file_name">
      <Comment>存储文件名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="423" parent="297" name="file_extension">
      <Comment>文件扩展名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="424" parent="297" name="file_size">
      <Comment>文件大小（字节）</Comment>
      <DasType>bigint|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="425" parent="297" name="content_type">
      <Comment>文件类型（MIME类型）</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="426" parent="297" name="file_category">
      <Comment>文件分类</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="427" parent="297" name="upload_source">
      <Comment>上传来源</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="428" parent="297" name="storage_provider">
      <Comment>云存储提供商</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="429" parent="297" name="bucket_name">
      <Comment>存储桶名称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="430" parent="297" name="storage_path">
      <Comment>存储路径</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="431" parent="297" name="access_url">
      <Comment>访问URL</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="432" parent="297" name="thumbnail_url">
      <Comment>缩略图URL</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="433" parent="297" name="file_md5">
      <Comment>文件MD5值</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="434" parent="297" name="business_id">
      <Comment>关联业务ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>23</Position>
    </column>
    <column id="435" parent="297" name="business_type">
      <Comment>关联业务类型</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>24</Position>
    </column>
    <column id="436" parent="297" name="remark">
      <Comment>备注信息</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>25</Position>
    </column>
    <index id="437" parent="297" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="438" parent="297" name="idx_create_time">
      <ColNames>create_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="439" parent="297" name="idx_create_user">
      <ColNames>create_user</ColNames>
      <Type>btree</Type>
    </index>
    <index id="440" parent="297" name="idx_file_category">
      <ColNames>file_category</ColNames>
      <Type>btree</Type>
    </index>
    <index id="441" parent="297" name="idx_upload_source">
      <ColNames>upload_source</ColNames>
      <Type>btree</Type>
    </index>
    <index id="442" parent="297" name="idx_file_md5">
      <ColNames>file_md5</ColNames>
      <Type>btree</Type>
    </index>
    <index id="443" parent="297" name="idx_business">
      <ColNames>business_type
business_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="444" parent="297" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="445" parent="298" name="id">
      <Comment>编号</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="446" parent="298" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="447" parent="298" name="service_id">
      <Comment>服务ID</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="448" parent="298" name="server_host">
      <Comment>服务器名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="449" parent="298" name="server_ip">
      <Comment>服务器IP地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="450" parent="298" name="env">
      <Comment>服务器环境</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="451" parent="298" name="type">
      <Comment>日志类型</Comment>
      <DasType>char(1)|0s</DasType>
      <DefaultExpression>&apos;1&apos;</DefaultExpression>
      <Position>7</Position>
    </column>
    <column id="452" parent="298" name="title">
      <Comment>日志标题</Comment>
      <DasType>varchar(255)|0s</DasType>
      <DefaultExpression>&apos;&apos;</DefaultExpression>
      <Position>8</Position>
    </column>
    <column id="453" parent="298" name="method">
      <Comment>操作方式</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="454" parent="298" name="request_uri">
      <Comment>请求URI</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="455" parent="298" name="user_agent">
      <Comment>用户代理</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="456" parent="298" name="remote_ip">
      <Comment>操作IP地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="457" parent="298" name="method_class">
      <Comment>方法类</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="458" parent="298" name="method_name">
      <Comment>方法名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="459" parent="298" name="params">
      <Comment>操作提交的数据</Comment>
      <DasType>text|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="460" parent="298" name="time">
      <Comment>执行时间</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="461" parent="298" name="create_by">
      <Comment>创建者</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="462" parent="298" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>18</Position>
    </column>
    <index id="463" parent="298" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="464" parent="298" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="465" parent="299" name="id">
      <Comment>编号</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="466" parent="299" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="467" parent="299" name="service_id">
      <Comment>服务ID</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="468" parent="299" name="server_host">
      <Comment>服务器名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="469" parent="299" name="server_ip">
      <Comment>服务器IP地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="470" parent="299" name="env">
      <Comment>系统环境</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="471" parent="299" name="method">
      <Comment>操作方式</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="472" parent="299" name="request_uri">
      <Comment>请求URI</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="473" parent="299" name="user_agent">
      <Comment>用户代理</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="474" parent="299" name="stack_trace">
      <Comment>堆栈</Comment>
      <DasType>text|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="475" parent="299" name="exception_name">
      <Comment>异常名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="476" parent="299" name="message">
      <Comment>异常信息</Comment>
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="477" parent="299" name="line_number">
      <Comment>错误行数</Comment>
      <DasType>int|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="478" parent="299" name="remote_ip">
      <Comment>操作IP地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="479" parent="299" name="method_class">
      <Comment>方法类</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="480" parent="299" name="file_name">
      <Comment>文件名</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="481" parent="299" name="method_name">
      <Comment>方法名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="482" parent="299" name="params">
      <Comment>操作提交的数据</Comment>
      <DasType>text|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="483" parent="299" name="time">
      <Comment>执行时间</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="484" parent="299" name="create_by">
      <Comment>创建者</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="485" parent="299" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>21</Position>
    </column>
    <index id="486" parent="299" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="487" parent="299" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="488" parent="300" name="id">
      <Comment>编号</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="489" parent="300" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="490" parent="300" name="service_id">
      <Comment>服务ID</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="491" parent="300" name="server_host">
      <Comment>服务器名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="492" parent="300" name="server_ip">
      <Comment>服务器IP地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="493" parent="300" name="env">
      <Comment>系统环境</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="494" parent="300" name="log_level">
      <Comment>日志级别</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="495" parent="300" name="log_id">
      <Comment>日志业务id</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="496" parent="300" name="log_data">
      <Comment>日志数据</Comment>
      <DasType>text|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="497" parent="300" name="method">
      <Comment>操作方式</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="498" parent="300" name="request_uri">
      <Comment>请求URI</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="499" parent="300" name="remote_ip">
      <Comment>操作IP地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="500" parent="300" name="method_class">
      <Comment>方法类</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="501" parent="300" name="method_name">
      <Comment>方法名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="502" parent="300" name="user_agent">
      <Comment>用户代理</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="503" parent="300" name="params">
      <Comment>操作提交的数据</Comment>
      <DasType>text|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="504" parent="300" name="time">
      <Comment>执行时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="505" parent="300" name="create_by">
      <Comment>创建者</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="506" parent="300" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>19</Position>
    </column>
    <index id="507" parent="300" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="508" parent="300" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="509" parent="301" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="510" parent="301" name="parent_id">
      <Comment>父级菜单</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="511" parent="301" name="code">
      <Comment>菜单编号</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="512" parent="301" name="name">
      <Comment>菜单名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="513" parent="301" name="alias">
      <Comment>菜单别名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="514" parent="301" name="path">
      <Comment>请求地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="515" parent="301" name="source">
      <Comment>菜单资源</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="516" parent="301" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="517" parent="301" name="category">
      <Comment>菜单类型</Comment>
      <DasType>int|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="518" parent="301" name="action">
      <Comment>操作按钮类型</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>10</Position>
    </column>
    <column id="519" parent="301" name="is_open">
      <Comment>是否打开新页面</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>11</Position>
    </column>
    <column id="520" parent="301" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="521" parent="301" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
    </column>
    <index id="522" parent="301" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="523" parent="301" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="524" parent="302" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="525" parent="302" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="526" parent="302" name="title">
      <Comment>标题</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="527" parent="302" name="category">
      <Comment>类型</Comment>
      <DasType>int|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="528" parent="302" name="release_time">
      <Comment>发布时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="529" parent="302" name="content">
      <Comment>内容</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="530" parent="302" name="create_user">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="531" parent="302" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="532" parent="302" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="533" parent="302" name="update_user">
      <Comment>修改人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="534" parent="302" name="update_time">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="535" parent="302" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="536" parent="302" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="537" parent="302" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="538" parent="302" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="539" parent="303" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="540" parent="303" name="param_name">
      <Comment>参数名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="541" parent="303" name="param_key">
      <Comment>参数键</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="542" parent="303" name="param_value">
      <Comment>参数值</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="543" parent="303" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="544" parent="303" name="create_user">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="545" parent="303" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="546" parent="303" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="547" parent="303" name="update_user">
      <Comment>修改人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="548" parent="303" name="update_time">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="549" parent="303" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="550" parent="303" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <index id="551" parent="303" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="552" parent="303" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="553" parent="304" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="554" parent="304" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="555" parent="304" name="category">
      <Comment>岗位类型</Comment>
      <DasType>int|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="556" parent="304" name="post_code">
      <Comment>岗位编号</Comment>
      <DasType>varchar(12)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="557" parent="304" name="post_name">
      <Comment>岗位名称</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="558" parent="304" name="sort">
      <Comment>岗位排序</Comment>
      <DasType>int|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="559" parent="304" name="remark">
      <Comment>岗位描述</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="560" parent="304" name="create_user">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="561" parent="304" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="562" parent="304" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="563" parent="304" name="update_user">
      <Comment>修改人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="564" parent="304" name="update_time">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="565" parent="304" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="566" parent="304" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="567" parent="304" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="568" parent="304" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="569" parent="305" name="code">
      <Comment>区划编号</Comment>
      <DasType>varchar(12)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="570" parent="305" name="parent_code">
      <Comment>父区划编号</Comment>
      <DasType>varchar(12)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="571" parent="305" name="ancestors">
      <Comment>祖区划编号</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="572" parent="305" name="name">
      <Comment>区划名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="573" parent="305" name="province_code">
      <Comment>省级区划编号</Comment>
      <DasType>varchar(12)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="574" parent="305" name="province_name">
      <Comment>省级名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="575" parent="305" name="city_code">
      <Comment>市级区划编号</Comment>
      <DasType>varchar(12)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="576" parent="305" name="city_name">
      <Comment>市级名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="577" parent="305" name="district_code">
      <Comment>区级区划编号</Comment>
      <DasType>varchar(12)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="578" parent="305" name="district_name">
      <Comment>区级名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="579" parent="305" name="town_code">
      <Comment>镇级区划编号</Comment>
      <DasType>varchar(12)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="580" parent="305" name="town_name">
      <Comment>镇级名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="581" parent="305" name="village_code">
      <Comment>村级区划编号</Comment>
      <DasType>varchar(12)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="582" parent="305" name="village_name">
      <Comment>村级名称</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="583" parent="305" name="level">
      <Comment>层级</Comment>
      <DasType>int|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="584" parent="305" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="585" parent="305" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>17</Position>
    </column>
    <index id="586" parent="305" name="PRIMARY">
      <ColNames>code</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="587" parent="305" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="588" parent="306" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="589" parent="306" name="name">
      <Comment>文件名</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
      <CollationName>utf8mb3_general_ci</CollationName>
    </column>
    <column id="590" parent="306" name="content">
      <Comment>文件内容</Comment>
      <DasType>mediumblob|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="591" parent="306" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="592" parent="306" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="593" parent="306" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <index id="594" parent="306" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="595" parent="306" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="596" parent="307" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="597" parent="307" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="598" parent="307" name="parent_id">
      <Comment>父主键</Comment>
      <DasType>bigint|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>3</Position>
    </column>
    <column id="599" parent="307" name="role_name">
      <Comment>角色名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="600" parent="307" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="601" parent="307" name="role_alias">
      <Comment>角色别名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="602" parent="307" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>7</Position>
    </column>
    <index id="603" parent="307" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="604" parent="307" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="605" parent="308" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="606" parent="308" name="menu_id">
      <Comment>菜单id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="607" parent="308" name="role_id">
      <Comment>角色id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="608" parent="308" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="609" parent="308" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="610" parent="309" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="611" parent="309" name="scope_id">
      <Comment>数据权限id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="612" parent="309" name="role_id">
      <Comment>角色id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="613" parent="309" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="614" parent="309" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="615" parent="310" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="616" parent="310" name="menu_id">
      <Comment>菜单主键</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="617" parent="310" name="resource_code">
      <Comment>资源编号</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="618" parent="310" name="scope_name">
      <Comment>数据权限名称</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="619" parent="310" name="scope_field">
      <Comment>数据权限字段</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="620" parent="310" name="scope_class">
      <Comment>数据权限类名</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="621" parent="310" name="scope_column">
      <Comment>数据权限字段</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="622" parent="310" name="scope_type">
      <Comment>数据权限类型</Comment>
      <DasType>int|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="623" parent="310" name="scope_value">
      <Comment>数据权限值域</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="624" parent="310" name="remark">
      <Comment>数据权限备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="625" parent="310" name="create_user">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="626" parent="310" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="627" parent="310" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="628" parent="310" name="update_user">
      <Comment>修改人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="629" parent="310" name="update_time">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="630" parent="310" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="631" parent="310" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <Position>17</Position>
    </column>
    <index id="632" parent="310" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="633" parent="310" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="634" parent="311" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="635" parent="311" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="636" parent="311" name="tenant_name">
      <Comment>租户名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="637" parent="311" name="domain">
      <Comment>域名地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="638" parent="311" name="linkman">
      <Comment>联系人</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="639" parent="311" name="contact_number">
      <Comment>联系电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="640" parent="311" name="address">
      <Comment>联系地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="641" parent="311" name="create_user">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="642" parent="311" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="643" parent="311" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="644" parent="311" name="update_user">
      <Comment>修改人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="645" parent="311" name="update_time">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="646" parent="311" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="647" parent="311" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
    </column>
    <index id="648" parent="311" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="649" parent="311" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="650" parent="312" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="651" parent="312" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <DefaultExpression>&apos;000000&apos;</DefaultExpression>
      <Position>2</Position>
    </column>
    <column id="652" parent="312" name="code">
      <Comment>用户编号</Comment>
      <DasType>varchar(12)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="653" parent="312" name="account">
      <Comment>账号</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="654" parent="312" name="password">
      <Comment>密码</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="655" parent="312" name="name">
      <Comment>昵称</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="656" parent="312" name="real_name">
      <Comment>真名</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="657" parent="312" name="avatar">
      <Comment>头像</Comment>
      <DasType>varchar(2000)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="658" parent="312" name="email">
      <Comment>邮箱</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="659" parent="312" name="phone">
      <Comment>手机</Comment>
      <DasType>varchar(45)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="660" parent="312" name="birthday">
      <Comment>生日</Comment>
      <DasType>datetime|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="661" parent="312" name="sex">
      <Comment>性别</Comment>
      <DasType>smallint|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="662" parent="312" name="role_id">
      <Comment>角色id</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="663" parent="312" name="dept_id">
      <Comment>部门id</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="664" parent="312" name="post_id">
      <Comment>岗位id</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="665" parent="312" name="create_user">
      <Comment>创建人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="666" parent="312" name="create_dept">
      <Comment>创建部门</Comment>
      <DasType>bigint|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="667" parent="312" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="668" parent="312" name="update_user">
      <Comment>修改人</Comment>
      <DasType>bigint|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="669" parent="312" name="update_time">
      <Comment>修改时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>20</Position>
    </column>
    <column id="670" parent="312" name="status">
      <Comment>状态</Comment>
      <DasType>int|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="671" parent="312" name="is_deleted">
      <Comment>是否已删除</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>22</Position>
    </column>
    <index id="672" parent="312" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="673" parent="312" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="674" parent="313" name="id">
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="675" parent="313" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(12)|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="676" parent="313" name="uuid">
      <Comment>第三方系统用户ID</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="677" parent="313" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="678" parent="313" name="username">
      <Comment>账号</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="679" parent="313" name="nickname">
      <Comment>用户名</Comment>
      <DasType>varchar(64)|0s</DasType>
      <Position>6</Position>
    </column>
    <column id="680" parent="313" name="avatar">
      <Comment>头像</Comment>
      <DasType>varchar(1000)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="681" parent="313" name="blog">
      <Comment>应用主页</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="682" parent="313" name="company">
      <Comment>公司名</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="683" parent="313" name="location">
      <Comment>地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="684" parent="313" name="email">
      <Comment>邮件</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="685" parent="313" name="remark">
      <Comment>备注</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="686" parent="313" name="gender">
      <Comment>性别</Comment>
      <DasType>varchar(16)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="687" parent="313" name="source">
      <Comment>来源</Comment>
      <DasType>varchar(16)|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="688" parent="313" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="689" parent="313" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="690" parent="314" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="691" parent="314" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="692" parent="314" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="693" parent="314" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="694" parent="314" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="695" parent="314" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="696" parent="314" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="697" parent="314" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="698" parent="314" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="699" parent="314" name="post_id">
      <Comment>信息贴ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="700" parent="314" name="audit_time">
      <Comment>审核时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="701" parent="314" name="audit_status">
      <Comment>审核状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="702" parent="314" name="audit_user">
      <Comment>审核人ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="703" parent="314" name="audit_remark">
      <Comment>审核备注</Comment>
      <DasType>text|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="704" parent="314" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="705" parent="314" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="706" parent="315" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="707" parent="315" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="708" parent="315" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="709" parent="315" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="710" parent="315" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="711" parent="315" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="712" parent="315" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="713" parent="315" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="714" parent="315" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="715" parent="315" name="rule_name">
      <Comment>规则名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="716" parent="315" name="keywords">
      <Comment>违禁词列表</Comment>
      <DasType>json|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="717" parent="315" name="max_content_length">
      <Comment>最大内容长度</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="718" parent="315" name="enable_image_check">
      <Comment>启用图片审核</Comment>
      <DasType>tinyint(1)|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>13</Position>
    </column>
    <index id="719" parent="315" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="720" parent="315" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="721" parent="316" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="722" parent="316" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="723" parent="316" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="724" parent="316" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="725" parent="316" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="726" parent="316" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="727" parent="316" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="728" parent="316" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="729" parent="316" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="730" parent="316" name="post_id">
      <Comment>信息贴ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="731" parent="316" name="call_time">
      <Comment>拨打时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="732" parent="316" name="duration">
      <Comment>通话时长</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="733" parent="316" name="call_status">
      <Comment>接通状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="734" parent="316" name="name">
      <Comment>联系人姓名</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="735" parent="316" name="phone">
      <Comment>联系电话</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>15</Position>
    </column>
    <index id="736" parent="316" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="737" parent="316" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="738" parent="317" name="id">
      <AutoIncrement>28</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="739" parent="317" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="740" parent="317" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="741" parent="317" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="742" parent="317" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="743" parent="317" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="744" parent="317" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="745" parent="317" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="746" parent="317" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="747" parent="317" name="name">
      <Comment>分类名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="748" parent="317" name="parent_id">
      <Comment>上级分类ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="749" parent="317" name="icon">
      <Comment>分类图标</Comment>
      <DasType>varchar(200)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="750" parent="317" name="description">
      <Comment>分类描述</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="751" parent="317" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="752" parent="317" name="enabled">
      <Comment>是否启用：0-否，1-是</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="753" parent="317" name="enable_audit">
      <Comment>是否启用审核：0-否，1-是</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>16</Position>
    </column>
    <column id="754" parent="317" name="tip">
      <Comment>提示语言</Comment>
      <DasType>text|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="755" parent="317" name="sort_order">
      <Comment>顺序</Comment>
      <DasType>int|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="756" parent="317" name="max_images">
      <DasType>int|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="757" parent="317" name="allow_tags">
      <DasType>varchar(1024)|0s</DasType>
      <Position>20</Position>
    </column>
    <index id="758" parent="317" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="759" parent="317" name="idx_sort">
      <ColNames>sort</ColNames>
      <Type>btree</Type>
    </index>
    <index id="760" parent="317" name="idx_enabled">
      <ColNames>enabled</ColNames>
      <Type>btree</Type>
    </index>
    <key id="761" parent="317" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="762" parent="318" name="id">
      <AutoIncrement>9</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="763" parent="318" name="category_id">
      <Comment>信息贴ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="764" parent="318" name="tag_id">
      <Comment>标签ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <index id="765" parent="318" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="766" parent="318" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="767" parent="319" name="id">
      <AutoIncrement>1902030876163375107</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="768" parent="319" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="769" parent="319" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="770" parent="319" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="771" parent="319" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="772" parent="319" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="773" parent="319" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="774" parent="319" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="775" parent="319" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="776" parent="319" name="name">
      <Comment>联系人姓名</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="777" parent="319" name="phone">
      <Comment>联系电话</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="778" parent="319" name="wechat">
      <Comment>微信账号</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="779" parent="319" name="contact_type">
      <Comment>联系方式类型</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="780" parent="319" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="781" parent="319" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="782" parent="320" name="id">
      <AutoIncrement>36</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="783" parent="320" name="post_id">
      <Comment>信息贴ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="784" parent="320" name="user_id">
      <Comment>操作用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="785" parent="320" name="fav_time">
      <Comment>收藏时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="786" parent="320" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="787" parent="320" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="788" parent="321" name="id">
      <AutoIncrement>1907341101594697731</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="789" parent="321" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="790" parent="321" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="791" parent="321" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="792" parent="321" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="793" parent="321" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="794" parent="321" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="795" parent="321" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="796" parent="321" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="797" parent="321" name="post_id">
      <Comment>信息贴ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="798" parent="321" name="user_id">
      <Comment>反馈用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="799" parent="321" name="content">
      <Comment>反馈内容</Comment>
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="800" parent="321" name="audit_status">
      <Comment>审核状态</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>13</Position>
    </column>
    <column id="801" parent="321" name="reason">
      <Comment>标签</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="802" parent="321" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="803" parent="321" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="804" parent="322" name="id">
      <AutoIncrement>62</AutoIncrement>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="805" parent="322" name="user_id">
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="806" parent="322" name="feedback_id">
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="807" parent="322" name="create_time">
      <DasType>datetime|0s</DasType>
      <NotNull>1</NotNull>
      <Position>4</Position>
    </column>
    <index id="808" parent="322" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="809" parent="322" name="idx_user_feedback">
      <ColNames>user_id
feedback_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="810" parent="322" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="811" parent="322" name="idx_user_feedback">
      <UnderlyingIndexName>idx_user_feedback</UnderlyingIndexName>
    </key>
    <column id="812" parent="323" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="813" parent="323" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="814" parent="323" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="815" parent="323" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="816" parent="323" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="817" parent="323" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="818" parent="323" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="819" parent="323" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="820" parent="323" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="821" parent="323" name="label">
      <Comment>标签名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="822" parent="323" name="description">
      <Comment>标签说明</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="823" parent="323" name="sort_order">
      <Comment>排序序号</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <index id="824" parent="323" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="825" parent="323" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="826" parent="324" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="827" parent="324" name="feedback_id">
      <Comment>反馈ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="828" parent="324" name="tag_id">
      <Comment>标签ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="829" parent="324" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="830" parent="324" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="831" parent="325" name="id">
      <AutoIncrement>40</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="832" parent="325" name="post_id">
      <Comment>信息贴ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="833" parent="325" name="user_id">
      <Comment>操作用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="834" parent="325" name="like_time">
      <Comment>点赞时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="835" parent="325" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="836" parent="325" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="837" parent="326" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="838" parent="326" name="open_id">
      <Comment>微信OpenID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="839" parent="326" name="union_id">
      <Comment>微信UnionID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="840" parent="326" name="nick_name">
      <Comment>昵称</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="841" parent="326" name="avatar_url">
      <Comment>头像</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>5</Position>
    </column>
    <column id="842" parent="326" name="gender">
      <Comment>性别：0-未知，1-男，2-女</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="843" parent="326" name="country">
      <Comment>国家</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="844" parent="326" name="province">
      <Comment>省份</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="845" parent="326" name="city">
      <Comment>城市</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="846" parent="326" name="language">
      <Comment>语言</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="847" parent="326" name="phone">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="848" parent="326" name="status">
      <Comment>状态：0-禁用，1-正常</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="849" parent="326" name="last_login_time">
      <Comment>最后登录时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="850" parent="326" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>14</Position>
    </column>
    <column id="851" parent="326" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>15</Position>
    </column>
    <index id="852" parent="326" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="853" parent="326" name="uk_open_id">
      <ColNames>open_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="854" parent="326" name="idx_union_id">
      <ColNames>union_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="855" parent="326" name="idx_status">
      <ColNames>status</ColNames>
      <Type>btree</Type>
    </index>
    <key id="856" parent="326" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="857" parent="326" name="uk_open_id">
      <UnderlyingIndexName>uk_open_id</UnderlyingIndexName>
    </key>
    <column id="858" parent="327" name="id">
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="859" parent="327" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="860" parent="327" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="861" parent="327" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="862" parent="327" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="863" parent="327" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="864" parent="327" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="865" parent="327" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="866" parent="327" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="867" parent="327" name="title">
      <Comment>标题</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="868" parent="327" name="content">
      <Comment>内容</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="869" parent="327" name="images">
      <Comment>图片</Comment>
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="870" parent="327" name="address">
      <Comment>发布地址</Comment>
      <DasType>varchar(255)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="871" parent="327" name="publish_time">
      <Comment>发布时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="872" parent="327" name="audit_status">
      <Comment>审核状态</Comment>
      <DasType>varchar(20)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>15</Position>
    </column>
    <column id="873" parent="327" name="geo_location">
      <Comment>地理位置</Comment>
      <DasType>json|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="874" parent="327" name="tags">
      <Comment>标签</Comment>
      <DasType>varchar(128)|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="875" parent="327" name="contact_name">
      <Comment>联系人</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="876" parent="327" name="contact_phone">
      <Comment>联系电话</Comment>
      <DasType>varchar(32)|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="877" parent="327" name="completed">
      <Comment>是否已完成</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>20</Position>
    </column>
    <column id="878" parent="327" name="category_id">
      <Comment>分类ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>21</Position>
    </column>
    <column id="879" parent="327" name="miniapp_user_id">
      <Comment>小程序用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>22</Position>
    </column>
    <column id="880" parent="327" name="open_id">
      <Comment>小程序用户OpenID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <Position>23</Position>
    </column>
    <column id="881" parent="327" name="like_count">
      <Comment>点赞数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>24</Position>
    </column>
    <column id="882" parent="327" name="favorite_count">
      <Comment>收藏数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>25</Position>
    </column>
    <column id="883" parent="327" name="view_count">
      <Comment>浏览数</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>26</Position>
    </column>
    <column id="884" parent="327" name="audit_remark">
      <Comment>审核备注</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>27</Position>
    </column>
    <column id="885" parent="327" name="audit_time">
      <Comment>审核时间</Comment>
      <DasType>datetime|0s</DasType>
      <Position>28</Position>
    </column>
    <column id="886" parent="327" name="audit_user_id">
      <Comment>审核人ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>29</Position>
    </column>
    <column id="887" parent="327" name="reject_reason">
      <Comment>拒绝原因</Comment>
      <DasType>varchar(500)|0s</DasType>
      <Position>30</Position>
    </column>
    <column id="888" parent="327" name="enable_audit">
      <Comment>是否启用审核：0-否，1-是</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>31</Position>
    </column>
    <column id="889" parent="327" name="top">
      <Comment>是否置顶</Comment>
      <DasType>varchar(32)|0s</DasType>
      <DefaultExpression>&apos;0&apos;</DefaultExpression>
      <Position>32</Position>
    </column>
    <index id="890" parent="327" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="891" parent="327" name="idx_publish_time">
      <ColNames>publish_time</ColNames>
      <Type>btree</Type>
    </index>
    <index id="892" parent="327" name="idx_audit_status">
      <ColNames>audit_status</ColNames>
      <Type>btree</Type>
    </index>
    <index id="893" parent="327" name="idx_category_id">
      <ColNames>category_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="894" parent="327" name="idx_open_id">
      <ColNames>open_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="895" parent="327" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="896" parent="328" name="id">
      <AutoIncrement>37</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="897" parent="328" name="post_id">
      <Comment>信息贴ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="898" parent="328" name="category_id">
      <Comment>分类ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="899" parent="328" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="900" parent="328" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="901" parent="329" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="902" parent="329" name="post_id">
      <Comment>帖子ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="903" parent="329" name="open_id">
      <Comment>用户OpenID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="904" parent="329" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="905" parent="329" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>5</Position>
    </column>
    <index id="906" parent="329" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="907" parent="329" name="uk_post_user">
      <ColNames>post_id
open_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="908" parent="329" name="idx_post_id">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="909" parent="329" name="idx_open_id">
      <ColNames>open_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="910" parent="329" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="911" parent="329" name="uk_post_user">
      <UnderlyingIndexName>uk_post_user</UnderlyingIndexName>
    </key>
    <column id="912" parent="330" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="913" parent="330" name="post_id">
      <Comment>帖子ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>2</Position>
    </column>
    <column id="914" parent="330" name="open_id">
      <Comment>用户OpenID</Comment>
      <DasType>varchar(100)|0s</DasType>
      <NotNull>1</NotNull>
      <Position>3</Position>
    </column>
    <column id="915" parent="330" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <Position>4</Position>
    </column>
    <column id="916" parent="330" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime|0s</DasType>
      <DefaultExpression>CURRENT_TIMESTAMP</DefaultExpression>
      <OnUpdate>CURRENT_TIMESTAMP</OnUpdate>
      <Position>5</Position>
    </column>
    <index id="917" parent="330" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="918" parent="330" name="uk_post_user">
      <ColNames>post_id
open_id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="919" parent="330" name="idx_post_id">
      <ColNames>post_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="920" parent="330" name="idx_open_id">
      <ColNames>open_id</ColNames>
      <Type>btree</Type>
    </index>
    <key id="921" parent="330" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <key id="922" parent="330" name="uk_post_user">
      <UnderlyingIndexName>uk_post_user</UnderlyingIndexName>
    </key>
    <column id="923" parent="331" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="924" parent="331" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="925" parent="331" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="926" parent="331" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="927" parent="331" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="928" parent="331" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="929" parent="331" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="930" parent="331" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="931" parent="331" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="932" parent="331" name="post_id">
      <Comment>信息贴ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="933" parent="331" name="user_id">
      <Comment>举报用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="934" parent="331" name="content">
      <Comment>举报内容</Comment>
      <DasType>text|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="935" parent="331" name="images">
      <Comment>举报图片</Comment>
      <DasType>text|0s</DasType>
      <Position>13</Position>
    </column>
    <index id="936" parent="331" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="937" parent="331" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="938" parent="332" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="939" parent="332" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="940" parent="332" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="941" parent="332" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="942" parent="332" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="943" parent="332" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="944" parent="332" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="945" parent="332" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="946" parent="332" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="947" parent="332" name="label">
      <Comment>标签名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="948" parent="332" name="description">
      <Comment>标签说明</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="949" parent="332" name="sort_order">
      <Comment>排序序号</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <index id="950" parent="332" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="951" parent="332" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="952" parent="333" name="id">
      <AutoIncrement>1</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="953" parent="333" name="report_id">
      <Comment>举报ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="954" parent="333" name="tag_id">
      <Comment>标签ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="955" parent="333" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="956" parent="333" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="957" parent="334" name="id">
      <AutoIncrement>1937948207312412715</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="958" parent="334" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="959" parent="334" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="960" parent="334" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="961" parent="334" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="962" parent="334" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="963" parent="334" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="964" parent="334" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="965" parent="334" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="966" parent="334" name="tag_name">
      <Comment>标签名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="967" parent="334" name="description">
      <Comment>描述说明</Comment>
      <DasType>text|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="968" parent="334" name="sort_order">
      <Comment>排序序号</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>12</Position>
    </column>
    <column id="969" parent="334" name="name">
      <Comment>标签名称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="970" parent="334" name="category_id">
      <Comment>分类id</Comment>
      <DasType>bigint|0s</DasType>
      <Position>14</Position>
    </column>
    <column id="971" parent="334" name="color">
      <Comment>颜色</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>15</Position>
    </column>
    <column id="972" parent="334" name="icon">
      <Comment>图标</Comment>
      <DasType>varchar(1024)|0s</DasType>
      <Position>16</Position>
    </column>
    <column id="973" parent="334" name="sort">
      <Comment>排序</Comment>
      <DasType>int|0s</DasType>
      <Position>17</Position>
    </column>
    <column id="974" parent="334" name="enabled">
      <Comment>是否启用</Comment>
      <DasType>int|0s</DasType>
      <Position>18</Position>
    </column>
    <column id="975" parent="334" name="use_count">
      <Comment>使用次数</Comment>
      <DasType>int|0s</DasType>
      <Position>19</Position>
    </column>
    <column id="976" parent="334" name="is_system">
      <Comment>是否系统标签</Comment>
      <DasType>int|0s</DasType>
      <Position>20</Position>
    </column>
    <index id="977" parent="334" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <index id="978" parent="334" name="idx_category_id">
      <ColNames>category_id</ColNames>
      <Type>btree</Type>
    </index>
    <index id="979" parent="334" name="idx_sort">
      <ColNames>sort</ColNames>
      <Type>btree</Type>
    </index>
    <index id="980" parent="334" name="idx_enabled">
      <ColNames>enabled</ColNames>
      <Type>btree</Type>
    </index>
    <index id="981" parent="334" name="idx_use_count">
      <ColNames>use_count</ColNames>
      <Type>btree</Type>
    </index>
    <key id="982" parent="334" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="983" parent="335" name="id">
      <AutoIncrement>1927648543157985283</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="984" parent="335" name="create_dept">
      <Comment>创建部门ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="985" parent="335" name="create_time">
      <Comment>创建时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="986" parent="335" name="create_user">
      <Comment>创建用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>4</Position>
    </column>
    <column id="987" parent="335" name="is_deleted">
      <Comment>是否删除（0：未删除，1：已删除）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>0</DefaultExpression>
      <Position>5</Position>
    </column>
    <column id="988" parent="335" name="status">
      <Comment>状态（0：禁用，1：启用）</Comment>
      <DasType>int|0s</DasType>
      <DefaultExpression>1</DefaultExpression>
      <Position>6</Position>
    </column>
    <column id="989" parent="335" name="tenant_id">
      <Comment>租户ID</Comment>
      <DasType>varchar(6)|0s</DasType>
      <Position>7</Position>
    </column>
    <column id="990" parent="335" name="update_time">
      <Comment>更新时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>8</Position>
    </column>
    <column id="991" parent="335" name="update_user">
      <Comment>更新用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>9</Position>
    </column>
    <column id="992" parent="335" name="nickname">
      <Comment>昵称</Comment>
      <DasType>varchar(50)|0s</DasType>
      <Position>10</Position>
    </column>
    <column id="993" parent="335" name="mobile">
      <Comment>手机号</Comment>
      <DasType>varchar(20)|0s</DasType>
      <Position>11</Position>
    </column>
    <column id="994" parent="335" name="gender">
      <Comment>性别</Comment>
      <DasType>varchar(10)|0s</DasType>
      <Position>12</Position>
    </column>
    <column id="995" parent="335" name="signature">
      <Comment>个性签名</Comment>
      <DasType>text|0s</DasType>
      <Position>13</Position>
    </column>
    <column id="996" parent="335" name="avatar">
      <Comment>头像</Comment>
      <DasType>varchar(1024)|0s</DasType>
      <Position>14</Position>
    </column>
    <index id="997" parent="335" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="998" parent="335" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="999" parent="336" name="id">
      <AutoIncrement>6</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1000" parent="336" name="user_id">
      <Comment>用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1001" parent="336" name="contact_id">
      <Comment>联系人ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <index id="1002" parent="336" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1003" parent="336" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1004" parent="337" name="id">
      <AutoIncrement>485</AutoIncrement>
      <Comment>主键ID</Comment>
      <DasType>bigint unsigned|0s</DasType>
      <NotNull>1</NotNull>
      <Position>1</Position>
    </column>
    <column id="1005" parent="337" name="post_id">
      <Comment>信息贴ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1006" parent="337" name="user_id">
      <Comment>浏览用户ID</Comment>
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
    <column id="1007" parent="337" name="view_time">
      <Comment>浏览时间</Comment>
      <DasType>datetime(6)|0s</DasType>
      <Position>4</Position>
    </column>
    <index id="1008" parent="337" name="PRIMARY">
      <ColNames>id</ColNames>
      <Type>btree</Type>
      <Unique>1</Unique>
    </index>
    <key id="1009" parent="337" name="PRIMARY">
      <NameSurrogate>1</NameSurrogate>
      <Primary>1</Primary>
      <UnderlyingIndexName>PRIMARY</UnderlyingIndexName>
    </key>
    <column id="1010" parent="338" name="id">
      <DasType>bigint|0s</DasType>
      <Position>1</Position>
    </column>
    <column id="1011" parent="338" name="contact_id">
      <DasType>bigint|0s</DasType>
      <Position>2</Position>
    </column>
    <column id="1012" parent="338" name="post_id">
      <DasType>bigint|0s</DasType>
      <Position>3</Position>
    </column>
  </database-model>
</dataSource>