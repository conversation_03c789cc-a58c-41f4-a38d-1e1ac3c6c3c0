# 文件分享功能说明

## 功能概述

文件分享功能是一个轻量级的文件和数据分享服务，支持文件上传和文本内容分享。用户可以通过简单的操作生成6位分享密钥，有效期1天，方便临时分享文件或文本内容。

## 主要特性

### 1. 文件分享
- **文件上传**: 支持任意类型文件上传
- **自动生成密钥**: 上传后自动生成6位随机分享密钥
- **有效期管理**: 所有分享默认1天有效期
- **文件下载**: 通过密钥直接下载文件
- **下载统计**: 记录文件下载次数

### 2. 文本分享
- **文本内容**: 支持不超过1024个字符的文本分享
- **即时生成**: 输入文本后立即生成分享密钥
- **内容获取**: 通过密钥获取原始文本内容
- **访问统计**: 记录文本内容访问次数

### 3. 安全特性
- **密钥唯一性**: 6位密钥确保唯一性
- **自动过期**: 1天后自动失效
- **状态管理**: 支持正常、过期、删除状态
- **访问控制**: 通过密钥控制访问权限

## 技术架构

### 后端技术栈
- **框架**: Spring Boot 3.x + SpringBlade 4.4.2
- **数据库**: MySQL 8.0+ + MyBatis Plus
- **文件存储**: 本地文件系统
- **API文档**: Swagger 3 (Knife4j)
- **工具库**: Hutool 5.8.26

### 项目结构
```
org.springblade.fileShare/
├── controller/          # 控制器层
│   └── FileShareController.java
├── service/            # 服务层
│   ├── IFileShareService.java
│   └── impl/FileShareServiceImpl.java
├── mapper/             # 数据访问层
│   └── FileShareMapper.java
├── entity/             # 实体类
│   └── FileShare.java
├── dto/                # 数据传输对象
│   └── FileShareDTO.java
├── vo/                 # 视图对象
│   └── FileShareVO.java
└── wrapper/            # 包装器
    └── FileShareWrapper.java
```

## 数据库设计

### 表结构
```sql
CREATE TABLE `file_share` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `share_key` varchar(6) NOT NULL COMMENT '分享密钥（6位字符）',
  `share_type` varchar(10) NOT NULL COMMENT '分享类型（FILE-文件，TEXT-文本）',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件类型',
  `text_content` text COMMENT '文本内容',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `status` int DEFAULT '0' COMMENT '状态（0-正常，1-已过期，2-已删除）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `update_user` bigint DEFAULT NULL COMMENT '更新人',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_key` (`share_key`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件分享表';
```

### 索引设计
- **主键索引**: `id` - 自增主键
- **唯一索引**: `share_key` - 确保密钥唯一性
- **普通索引**: `share_type` - 按类型查询
- **普通索引**: `expire_time` - 过期时间查询
- **普通索引**: `status` - 状态查询
- **普通索引**: `create_time` - 创建时间查询

## API接口

### 1. 文件上传
```
POST /blade-fileshare/upload
Content-Type: multipart/form-data

参数: file (MultipartFile)
```

### 2. 文本分享
```
POST /blade-fileshare/share-text
Content-Type: application/json

请求体:
{
  "shareType": "TEXT",
  "textContent": "要分享的文本内容"
}
```

### 3. 获取分享信息
```
GET /blade-fileshare/info/{shareKey}
```

### 4. 下载文件
```
GET /blade-fileshare/download/{shareKey}
```

### 5. 获取文本内容
```
GET /blade-fileshare/text/{shareKey}
```

### 6. 生成分享密钥
```
GET /blade-fileshare/generate-key
```

## 核心功能实现

### 1. 密钥生成算法
```java
public String generateShareKey() {
    String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
    Random random = new Random();
    StringBuilder sb = new StringBuilder();
    
    for (int i = 0; i < SHARE_KEY_LENGTH; i++) {
        sb.append(chars.charAt(random.nextInt(chars.length())));
    }
    
    String shareKey = sb.toString();
    
    // 检查密钥是否已存在，如果存在则递归生成
    if (count(queryWrapper) > 0) {
        return generateShareKey();
    }
    
    return shareKey;
}
```

### 2. 文件上传处理
```java
public FileShareVO uploadFile(MultipartFile file) {
    // 1. 验证文件
    if (file.isEmpty()) {
        throw new RuntimeException("上传的文件不能为空");
    }
    
    // 2. 创建上传目录
    Path uploadPath = Paths.get(UPLOAD_DIR);
    if (!Files.exists(uploadPath)) {
        Files.createDirectories(uploadPath);
    }
    
    // 3. 生成唯一文件名
    String fileName = IdUtil.fastSimpleUUID() + fileExtension;
    
    // 4. 保存文件
    Path filePath = uploadPath.resolve(fileName);
    Files.copy(file.getInputStream(), filePath);
    
    // 5. 创建分享记录
    FileShare fileShare = new FileShare();
    fileShare.setShareKey(generateShareKey());
    fileShare.setShareType(SHARE_TYPE_FILE);
    // ... 设置其他字段
    
    // 6. 保存到数据库
    save(fileShare);
    
    return convertToVO(fileShare);
}
```

### 3. 过期检查机制
```java
public FileShareVO getByShareKey(String shareKey) {
    // 1. 查询分享记录
    FileShare fileShare = getOne(queryWrapper);
    
    if (fileShare == null) {
        throw new RuntimeException("分享记录不存在");
    }
    
    // 2. 检查是否过期
    if (LocalDateTime.now().isAfter(fileShare.getExpireTime())) {
        fileShare.setStatus(1); // 标记为已过期
        updateById(fileShare);
        throw new RuntimeException("分享已过期");
    }
    
    // 3. 检查状态
    if (fileShare.getStatus() != 0) {
        throw new RuntimeException("分享已被删除或已过期");
    }
    
    return convertToVO(fileShare);
}
```

## 部署说明

### 1. 环境要求
- JDK 17+
- MySQL 8.0+
- Maven 3.6+

### 2. 数据库配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: password
```

### 3. 文件上传配置
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 256MB
      max-request-size: 1024MB
```

### 4. 部署步骤
1. 执行数据库脚本 `file_share.sql`
2. 配置数据库连接信息
3. 启动应用服务
4. 访问测试页面 `http://localhost:80/fileshare-test.html`

## 测试验证

### 1. 功能测试
- 文件上传测试
- 文本分享测试
- 密钥生成测试
- 文件下载测试
- 文本获取测试
- 过期处理测试

### 2. 性能测试
- 并发上传测试
- 大文件上传测试
- 密钥生成性能测试
- 数据库查询性能测试

### 3. 安全测试
- 密钥唯一性测试
- 过期机制测试
- 文件类型验证测试
- 访问权限测试

## 扩展功能

### 1. 可扩展功能
- **文件类型限制**: 支持指定允许的文件类型
- **文件大小限制**: 支持自定义文件大小限制
- **有效期自定义**: 支持自定义分享有效期
- **访问密码**: 支持为分享添加访问密码
- **分享统计**: 支持详细的访问统计信息
- **批量操作**: 支持批量删除过期分享

### 2. 安全增强
- **文件扫描**: 集成病毒扫描功能
- **内容过滤**: 支持文本内容敏感词过滤
- **访问日志**: 记录详细的访问日志
- **频率限制**: 支持访问频率限制
- **IP白名单**: 支持IP访问控制

### 3. 存储优化
- **云存储**: 支持阿里云OSS、腾讯云COS等
- **CDN加速**: 支持CDN文件加速
- **文件压缩**: 支持文件自动压缩
- **缩略图**: 支持图片自动生成缩略图

## 注意事项

### 1. 安全考虑
- 定期清理过期文件
- 监控异常访问行为
- 限制文件上传大小
- 验证文件类型安全性

### 2. 性能优化
- 合理设置数据库索引
- 定期清理过期数据
- 优化文件存储路径
- 监控系统资源使用

### 3. 运维建议
- 定期备份数据库
- 监控磁盘空间使用
- 设置日志轮转策略
- 配置告警机制

## 总结

文件分享功能提供了简单易用的文件和数据分享服务，具有以下特点：

1. **简单易用**: 6位密钥，操作简单
2. **安全可靠**: 自动过期，状态管理
3. **功能完整**: 支持文件和文本分享
4. **扩展性强**: 支持多种扩展功能
5. **性能优良**: 优化的数据库设计和文件处理

该功能可以满足临时文件分享、文本内容分享等常见需求，为用户提供便捷的分享服务。 