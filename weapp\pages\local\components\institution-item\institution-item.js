// pkg_user/pages/local/components/institution-item/institution-item.js
Component({
  properties: {
    institution: {
      type: Object,
      value: {}
    }
  },

  data: {
    imageList: []
  },

  lifetimes: {
    attached() {
      this.processInstitutionData();
    }
  },

  observers: {
    'institution': function(institution) {
      this.processInstitutionData();
    }
  },

  methods: {
    // 获取分类名称
    getCategoryName(category) {
      const categoryMap = {
        'all': '全部',
        'education': '教育培训',
        'medical': '医疗健康',
        'finance': '金融服务',
        'government': '政府机构',
        'business': '商业服务'
      };
      return categoryMap[category] || '其他';
    },

    // 处理机构数据
    processInstitutionData() {
      const institution = this.data.institution;
      if (!institution) return;

      // 处理图片列表
      let imageList = [];
      if (institution.logo) {
        imageList.push(institution.logo);
      }
      if (institution.images && institution.images.length > 0) {
        imageList = imageList.concat(institution.images);
      }

      // 处理服务标签（如果是字符串，转换为数组）
      let services = institution.services || [];
      if (typeof services === 'string') {
        try {
          services = JSON.parse(services);
        } catch (e) {
          services = services.split(',').map(s => s.trim()).filter(s => s);
        }
      }

      // 处理浏览量和点赞量，确保是数字
      const viewCount = parseInt(institution.viewCount) || 0;
      const likeCount = parseInt(institution.likeCount) || 0;

      this.setData({
        imageList: imageList,
        'institution.services': services,
        'institution.viewCount': viewCount,
        'institution.likeCount': likeCount
      });
    },

    // 点击机构卡片
    onTap() {
      this.triggerEvent('tap', {
        institution: this.data.institution
      });
    },

    // 拨打电话
    onCallTap(e) {
      e.stopPropagation();
      const phone = this.data.institution.phone;
      if (phone) {
        wx.makePhoneCall({
          phoneNumber: phone,
          fail: () => {
            wx.showToast({
              title: '拨号失败',
              icon: 'none'
            });
          }
        });
      } else {
        wx.showToast({
          title: '暂无联系电话',
          icon: 'none'
        });
      }
    },

    // 点赞机构
    onLikeTap(e) {
      e.stopPropagation();
      const institution = this.data.institution;
      const currentLikeCount = institution.likeCount || 0;

      // 更新点赞数
      institution.likeCount = currentLikeCount + 1;

      this.setData({
        institution: institution
      });

      wx.showToast({
        title: '点赞成功',
        icon: 'success'
      });
    },

    // 导航到机构
    onNavigateTap(e) {
      e.stopPropagation();
      const institution = this.data.institution;
      if (institution.latitude && institution.longitude) {
        wx.openLocation({
          latitude: parseFloat(institution.latitude) || 30.2741,
          longitude: parseFloat(institution.longitude) || 120.1551,
          name: institution.name,
          address: institution.address
        });
      } else {
        wx.showToast({
          title: '暂无位置信息',
          icon: 'none'
        });
      }
    },

    // 预览图片
    previewImage(e) {
      const current = e.currentTarget.dataset.current;
      wx.previewImage({
        current: current,
        urls: this.data.imageList
      });
    },

    // 点击更多按钮
    onTapMore(e) {
      e.stopPropagation();
      const institution = this.data.institution;
      
      wx.showActionSheet({
        itemList: ['拨打电话', '查看位置', '收藏机构'],
        success: (res) => {
          switch(res.tapIndex) {
            case 0:
              if (institution.phone) {
                wx.makePhoneCall({
                  phoneNumber: institution.phone
                });
              }
              break;
            case 1:
              if (institution.latitude && institution.longitude) {
                wx.openLocation({
                  latitude: parseFloat(institution.latitude),
                  longitude: parseFloat(institution.longitude),
                  name: institution.name,
                  address: institution.address
                });
              }
              break;
            case 2:
              this.toggleFavorite();
              break;
          }
        }
      });
    },

    // 切换收藏状态
    toggleFavorite() {
      // 这里可以调用收藏API
      wx.showToast({
        title: '收藏功能开发中',
        icon: 'none'
      });
    }
  }
});
