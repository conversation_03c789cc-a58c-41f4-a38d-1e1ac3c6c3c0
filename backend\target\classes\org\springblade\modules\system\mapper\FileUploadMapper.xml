<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.system.mapper.FileUploadMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="fileUploadResultMap" type="org.springblade.modules.system.entity.FileUpload">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="original_name" property="originalName"/>
        <result column="file_name" property="fileName"/>
        <result column="file_extension" property="fileExtension"/>
        <result column="file_size" property="fileSize"/>
        <result column="content_type" property="contentType"/>
        <result column="file_category" property="fileCategory"/>
        <result column="upload_source" property="uploadSource"/>
        <result column="storage_provider" property="storageProvider"/>
        <result column="bucket_name" property="bucketName"/>
        <result column="storage_path" property="storagePath"/>
        <result column="access_url" property="accessUrl"/>
        <result column="thumbnail_url" property="thumbnailUrl"/>
        <result column="file_md5" property="fileMd5"/>
        <result column="business_id" property="businessId"/>
        <result column="business_type" property="businessType"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 文件上传VO结果映射 -->
    <resultMap id="fileUploadVOResultMap" type="org.springblade.modules.system.vo.FileUploadVO">
        <result column="id" property="id"/>
        <result column="tenant_id" property="tenantId"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="original_name" property="originalName"/>
        <result column="file_name" property="fileName"/>
        <result column="file_extension" property="fileExtension"/>
        <result column="file_size" property="fileSize"/>
        <result column="content_type" property="contentType"/>
        <result column="file_category" property="fileCategory"/>
        <result column="upload_source" property="uploadSource"/>
        <result column="storage_provider" property="storageProvider"/>
        <result column="bucket_name" property="bucketName"/>
        <result column="storage_path" property="storagePath"/>
        <result column="access_url" property="accessUrl"/>
        <result column="thumbnail_url" property="thumbnailUrl"/>
        <result column="file_md5" property="fileMd5"/>
        <result column="business_id" property="businessId"/>
        <result column="business_type" property="businessType"/>
        <result column="remark" property="remark"/>
        <result column="file_size_formatted" property="fileSizeFormatted"/>
        <result column="create_time_formatted" property="createTimeFormatted"/>
        <result column="create_user_name" property="createUserName"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="baseColumnList">
        select id,
        create_user AS createUser,
        create_time AS createTime,
        update_user AS updateUser,
        update_time AS updateTime,
        status,
        is_deleted AS isDeleted,
        original_name, file_name, file_extension, file_size, content_type, file_category,
        upload_source, storage_provider, bucket_name, storage_path, access_url, thumbnail_url,
        file_md5, business_id, business_type, remark
    </sql>

    <!-- 分页查询文件上传 -->
    <select id="selectFileUploadPage" resultMap="fileUploadVOResultMap">
        SELECT
            f.*,
            CASE
                WHEN f.file_size >= ********** THEN CONCAT(ROUND(f.file_size / **********, 2), ' GB')
                WHEN f.file_size >= 1048576 THEN CONCAT(ROUND(f.file_size / 1048576, 2), ' MB')
                WHEN f.file_size >= 1024 THEN CONCAT(ROUND(f.file_size / 1024, 2), ' KB')
                ELSE CONCAT(f.file_size, ' B')
            END AS file_size_formatted,
            DATE_FORMAT(f.create_time, '%Y-%m-%d %H:%i:%s') AS create_time_formatted,
            u.name AS create_user_name
        FROM blade_file_upload f
        LEFT JOIN blade_user u ON f.create_user = u.id
        WHERE f.is_deleted = 0
        <if test="fileUpload != null">
            <if test="fileUpload.originalName != null and fileUpload.originalName != ''">
                AND f.original_name LIKE CONCAT('%', #{fileUpload.originalName}, '%')
            </if>
            <if test="fileUpload.fileCategory != null and fileUpload.fileCategory != ''">
                AND f.file_category = #{fileUpload.fileCategory}
            </if>
            <if test="fileUpload.uploadSource != null and fileUpload.uploadSource != ''">
                AND f.upload_source = #{fileUpload.uploadSource}
            </if>
            <if test="fileUpload.storageProvider != null and fileUpload.storageProvider != ''">
                AND f.storage_provider = #{fileUpload.storageProvider}
            </if>
            <if test="fileUpload.businessType != null and fileUpload.businessType != ''">
                AND f.business_type = #{fileUpload.businessType}
            </if>
            <if test="fileUpload.businessId != null">
                AND f.business_id = #{fileUpload.businessId}
            </if>
            <if test="fileUpload.status != null">
                AND f.status = #{fileUpload.status}
            </if>
            <if test="fileUpload.createTime != null">
                AND DATE(f.create_time) = DATE(#{fileUpload.createTime})
            </if>
        </if>
        ORDER BY f.create_time DESC
    </select>

    <!-- 获取文件总大小 -->
    <select id="getTotalFileSize" resultType="java.lang.Long">
        SELECT COALESCE(SUM(file_size), 0)
        FROM blade_file_upload
        WHERE is_deleted = 0 AND status = 1
    </select>

    <!-- 按文件类型统计 -->
    <select id="getFileTypeStats" resultType="java.util.Map">
        SELECT
            file_category,
            COUNT(*) AS count,
            SUM(file_size) AS total_size
        FROM blade_file_upload
        WHERE is_deleted = 0 AND status = 1
        GROUP BY file_category
        ORDER BY count DESC
    </select>

    <!-- 按上传来源统计 -->
    <select id="getUploadSourceStats" resultType="java.util.Map">
        SELECT
            upload_source,
            COUNT(*) AS count,
            SUM(file_size) AS total_size
        FROM blade_file_upload
        WHERE is_deleted = 0 AND status = 1
        GROUP BY upload_source
        ORDER BY count DESC
    </select>

</mapper>
