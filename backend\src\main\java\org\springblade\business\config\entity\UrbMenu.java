/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Data
@TableName("urb_menu")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "UrbMenu对象")
public class UrbMenu extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 唯一，菜单名称
     */
    @Schema(description = "唯一，菜单名称")
    private String name;
    /**
     * 图片地址
     */
    @Schema(description = "图片地址")
    private String image;
    /**
     * 排序权重
     */
    @Schema(description = "排序权重")
    private String sortWeight;
    /**
     * 用于存储16进制颜色数据
     */
    @Schema(description = "用于存储16进制颜色数据")
    private String color;
    /**
     * 用于存储链接地址
     */
    @Schema(description = "用于存储链接地址")
    private String url;
    /**
     * 0-菜单，1-滚动图片
     */
    @Schema(description = "0-菜单，1-滚动图片 2-用户菜单")
    private Integer category;


}
