-- 积分和签到功能数据库表结构
-- 创建时间: 2024-01-01
-- 说明: 包含用户积分、积分记录、签到记录等表

-- 1. 用户积分表
CREATE TABLE `urb_user_points` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户OpenID',
  `nick_name` varchar(100) DEFAULT NULL COMMENT '用户昵称',
  `avatar_url` varchar(500) DEFAULT NULL COMMENT '用户头像',
  `points` int DEFAULT '0' COMMENT '当前积分余额',
  `total_earned` int DEFAULT '0' COMMENT '累计获得积分',
  `total_spent` int DEFAULT '0' COMMENT '累计消费积分',
  `level` int DEFAULT '1' COMMENT '等级',
  `level_name` varchar(50) DEFAULT '新手' COMMENT '等级名称',
  `experience` int DEFAULT '0' COMMENT '经验值',
  `next_level_exp` int DEFAULT '100' COMMENT '升级所需经验',
  `last_signin_time` varchar(20) DEFAULT NULL COMMENT '最后签到时间',
  `continuous_days` int DEFAULT '0' COMMENT '连续签到天数',
  `total_signin_days` int DEFAULT '0' COMMENT '总签到天数',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id` (`open_id`),
  KEY `idx_points` (`points`),
  KEY `idx_level` (`level`),
  KEY `idx_continuous_days` (`continuous_days`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分表';

-- 2. 积分记录表
CREATE TABLE `urb_points_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户OpenID',
  `points` int NOT NULL COMMENT '积分变动数量（正数为获得，负数为消费）',
  `before_points` int DEFAULT NULL COMMENT '变动前积分',
  `after_points` int DEFAULT NULL COMMENT '变动后积分',
  `type` varchar(20) NOT NULL COMMENT '积分类型（SIGNIN：签到，EXCHANGE：兑换，SHARE：分享，INVITE：邀请，ADMIN：管理员操作）',
  `type_name` varchar(50) DEFAULT NULL COMMENT '积分类型名称',
  `business_id` bigint DEFAULT NULL COMMENT '关联业务ID',
  `business_type` varchar(50) DEFAULT NULL COMMENT '关联业务类型',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `operate_time` datetime(6) DEFAULT NULL COMMENT '操作时间',
  `operator` varchar(100) DEFAULT NULL COMMENT '操作人',
  `status` int DEFAULT '1' COMMENT '状态（0：无效，1：有效）',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_type` (`type`),
  KEY `idx_operate_time` (`operate_time`),
  KEY `idx_business_id` (`business_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- 3. 签到记录表
CREATE TABLE `urb_signin_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户OpenID',
  `signin_date` date NOT NULL COMMENT '签到日期',
  `signin_time` datetime(6) DEFAULT NULL COMMENT '签到时间',
  `year` int DEFAULT NULL COMMENT '签到年份',
  `month` int DEFAULT NULL COMMENT '签到月份',
  `day` int DEFAULT NULL COMMENT '签到日',
  `week_day` int DEFAULT NULL COMMENT '签到星期（1-7，对应周一到周日）',
  `points` int DEFAULT '0' COMMENT '获得积分',
  `continuous_reward` int DEFAULT '0' COMMENT '连续签到奖励积分',
  `continuous_days` int DEFAULT '0' COMMENT '连续签到天数',
  `signin_type` varchar(20) DEFAULT 'NORMAL' COMMENT '签到类型（NORMAL：正常签到，MAKEUP：补签）',
  `signin_ip` varchar(50) DEFAULT NULL COMMENT '签到IP',
  `device_info` varchar(500) DEFAULT NULL COMMENT '签到设备信息',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `status` int DEFAULT '1' COMMENT '状态（0：无效，1：有效）',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_open_id_date` (`open_id`, `signin_date`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_signin_date` (`signin_date`),
  KEY `idx_year_month` (`year`, `month`),
  KEY `idx_signin_type` (`signin_type`),
  KEY `idx_continuous_days` (`continuous_days`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='签到记录表';

-- 4. 积分商城商品表
CREATE TABLE `urb_points_goods` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `goods_name` varchar(100) NOT NULL COMMENT '商品名称',
  `goods_desc` text COMMENT '商品描述',
  `goods_image` varchar(500) DEFAULT NULL COMMENT '商品图片',
  `points_price` int NOT NULL COMMENT '积分价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价',
  `stock` int DEFAULT '0' COMMENT '库存数量',
  `exchange_limit` int DEFAULT '0' COMMENT '兑换限制（0：无限制）',
  `category` varchar(50) DEFAULT NULL COMMENT '商品分类',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `status` int DEFAULT '1' COMMENT '状态（0：下架，1：上架）',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_category` (`category`),
  KEY `idx_points_price` (`points_price`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分商城商品表';

-- 5. 积分兑换记录表
CREATE TABLE `urb_points_exchange` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户OpenID',
  `goods_id` bigint NOT NULL COMMENT '商品ID',
  `goods_name` varchar(100) DEFAULT NULL COMMENT '商品名称',
  `quantity` int DEFAULT '1' COMMENT '兑换数量',
  `points_cost` int NOT NULL COMMENT '消耗积分',
  `exchange_time` datetime(6) DEFAULT NULL COMMENT '兑换时间',
  `status` varchar(20) DEFAULT 'PENDING' COMMENT '状态（PENDING：待处理，PROCESSING：处理中，COMPLETED：已完成，CANCELLED：已取消）',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  KEY `idx_open_id` (`open_id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_exchange_time` (`exchange_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分兑换记录表';

-- 6. 连续签到奖励配置表
CREATE TABLE `urb_signin_reward_config` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `days` int NOT NULL COMMENT '连续签到天数',
  `reward_points` int NOT NULL COMMENT '奖励积分',
  `reward_desc` varchar(200) DEFAULT NULL COMMENT '奖励描述',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_days` (`days`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='连续签到奖励配置表';

-- 插入默认的连续签到奖励配置
INSERT INTO `urb_signin_reward_config` (`days`, `reward_points`, `reward_desc`, `status`, `sort_order`) VALUES
(1, 10, '连续签到1天奖励', 1, 1),
(3, 15, '连续签到3天奖励', 1, 2),
(7, 25, '连续签到7天奖励', 1, 3),
(15, 50, '连续签到15天奖励', 1, 4),
(30, 100, '连续签到30天奖励', 1, 5);

-- 插入示例积分商城商品
INSERT INTO `urb_points_goods` (`goods_name`, `goods_desc`, `points_price`, `original_price`, `stock`, `category`, `sort_order`, `status`) VALUES
('微信红包10元', '微信红包10元，可直接提现到微信钱包', 1000, 10.00, 999, '红包', 1, 1),
('微信红包5元', '微信红包5元，可直接提现到微信钱包', 500, 5.00, 999, '红包', 2, 1),
('优惠券10元', '通用优惠券10元，可用于商城购物', 800, 10.00, 999, '优惠券', 3, 1),
('优惠券5元', '通用优惠券5元，可用于商城购物', 400, 5.00, 999, '优惠券', 4, 1),
('实物礼品', '精美实物礼品，随机发放', 2000, 20.00, 100, '实物', 5, 1);

-- 创建索引优化查询性能
CREATE INDEX idx_user_points_open_id_status ON urb_user_points(open_id, status);
CREATE INDEX idx_points_record_open_id_type ON urb_points_record(open_id, type);
CREATE INDEX idx_signin_record_open_id_date ON urb_signin_record(open_id, signin_date);
CREATE INDEX idx_points_goods_category_status ON urb_points_goods(category, status);
CREATE INDEX idx_points_exchange_open_id_status ON urb_points_exchange(open_id, status);
