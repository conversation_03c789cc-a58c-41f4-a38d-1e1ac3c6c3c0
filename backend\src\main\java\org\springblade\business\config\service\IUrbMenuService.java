/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.service;

import jakarta.validation.Valid;
import org.springblade.business.config.entity.UrbMenu;
import org.springblade.business.config.vo.UrbMenuVO;
import org.springblade.core.mp.base.BaseService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
public interface IUrbMenuService extends BaseService<UrbMenu> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param urbMenu
	 * @return
	 */
	IPage<UrbMenuVO> selectUrbMenuPage(IPage<UrbMenuVO> page, UrbMenuVO urbMenu);

    boolean saveMenu(@Valid UrbMenu urbMenu);

	boolean updateMenuById(@Valid UrbMenu urbMenu);

	boolean saveOrUpdateMenu(@Valid UrbMenu urbMenu);

	boolean deleteLogicMenu(String ids);
}
