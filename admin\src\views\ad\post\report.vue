<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"

                   icon="el-icon-delete"
                   plain
                   v-if="permission.report_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template #menu="scope">
        <el-button text
                   type="primary"
                   icon="el-icon-view"
                   @click="handleViewPost(scope.row)">查看帖子
        </el-button>
      </template>
    </avue-crud>

    <!-- 点击查看帖子 - 帖子详情弹窗 -->
    <!-- currentPost = row.post -->
    <el-dialog v-model="postDetailVisible" title="帖子详情" width="800px" :close-on-click-modal="false">
      <div v-if="currentPost" class="post-detail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="发布状态">
            <el-tag :type="currentPost.publishStatus === '1' ? 'success' : 'warning'">
              {{ currentPost.publishStatus === '1' ? '已发布' : '未发布' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getAuditStatusType(currentPost.auditStatus)">
              {{ getAuditStatusText(currentPost.auditStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分类">{{ currentPost.categoryName || '未分类' }}</el-descriptions-item>
          <el-descriptions-item label="联系人">{{ currentPost.contactName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ currentPost.contactPhone || '-' }}</el-descriptions-item>
          <el-descriptions-item label="位置">{{ currentPost.location || '-' }}</el-descriptions-item>
          <el-descriptions-item label="地址">{{ currentPost.address || '-' }}</el-descriptions-item>
          <el-descriptions-item label="创建时间" :span="2">{{ currentPost.createTime }}</el-descriptions-item>
        </el-descriptions>

        <div class="post-content" style="margin-top: 20px;">
          <h4>帖子内容：</h4>
          <div style="padding: 10px; border: 1px solid #ddd; border-radius: 4px; background-color: #f9f9f9;">
            {{ currentPost.content || '无内容' }}
          </div>
        </div>

        <div v-if="currentPost.images" class="post-images" style="margin-top: 20px;">
          <h4>帖子图片：</h4>
          <div style="display: flex; flex-wrap: wrap; gap: 10px;">
            <img v-for="(image, index) in getImageList(currentPost.images)"
                 :key="index"
                 :src="image"
                 style="width: 100px; height: 100px; object-fit: cover; border-radius: 4px; cursor: pointer;"
                 @click="previewImage(image)">
          </div>
        </div>

        <div v-if="currentPostStats" class="post-stats" style="margin-top: 20px;">
          <h4>统计信息：</h4>
          <el-row :gutter="20">
            <el-col :span="6">
              <el-statistic title="点赞数" :value="currentPostStats.likeCount || 0" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="浏览数" :value="currentPostStats.viewCount || 0" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="收藏数" :value="currentPostStats.favoriteCount || 0" />
            </el-col>
            <el-col :span="6">
              <el-statistic title="反馈数" :value="currentPostStats.feedbackCount || 0" />
            </el-col>
          </el-row>
        </div>
      </div>

      <template #footer>
        <el-button @click="postDetailVisible = false">关闭</el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/report";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "举报ID",
              prop: "id",
              width: 100,
              addDisplay: false,
              editDisplay: false,
              search: false
            },
            {
              label: "帖子分类",
              prop: "postCategory.categoryName",
              width: 120,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                return row.postCategory ? row.postCategory.categoryName || '未分类' : '-';
              }
            },
            {
              label: "帖子内容",
              prop: "post.content",
              width: 250,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                if (!row.post || !row.post.content) return '-';
                return row.post.content.length > 50
                  ? row.post.content.substring(0, 50) + '...'
                  : row.post.content;
              }
            },
            {
              label: "点赞数",
              prop: "postStats.likeCount",
              width: 80,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                return row.postStats ? (row.postStats.likeCount || 0) : 0;
              }
            },
            {
              label: "浏览数",
              prop: "postStats.viewCount",
              width: 80,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                return row.postStats ? (row.postStats.viewCount || 0) : 0;
              }
            },
            {
              label: "收藏数",
              prop: "postStats.favoriteCount",
              width: 80,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                return row.postStats ? (row.postStats.favoriteCount || 0) : 0;
              }
            },
            {
              label: "举报用户",
              prop: "reportUser.nickname",
              width: 120,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                return row.reportUser ? row.reportUser.nickname || '未知用户' : '-';
              }
            },
            {
              label: "举报用户手机",
              prop: "reportUser.mobile",
              width: 130,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                return row.reportUser ? row.reportUser.mobile || '无' : '-';
              }
            },
            
            {
              label: "举报内容",
              prop: "content",
              width: 200,
              type: "textarea",
              search: false,
              formatter: (_, __, cellValue) => {
                if (!cellValue) return '-';
                return cellValue.length > 30
                  ? cellValue.substring(0, 30) + '...'
                  : cellValue;
              },
              rules: [{
                required: true,
                message: "请输入举报内容",
                trigger: "blur"
              }]
            },
            {
              label: "举报图片",
              prop: "images",
              width: 120,
              type: "upload",
              listType: "picture-img",
              search: false,
              addDisplay: false,
              editDisplay: false,
              formatter: (_, __, cellValue) => {
                return cellValue ? '有图片' : '无图片';
              }
            },
            {
              label: "被举报用户",
              prop: "reportedUser.nickname",
              width: 120,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                return row.reportedUser ? row.reportedUser.nickname || '未知用户' : '-';
              }
            },
            {
              label: "被举报电话",
              prop: "reportedUser.mobile",
              width: 130,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                return row.reportedUser ? row.reportedUser.mobile || '无' : '-';
              }
            },
            {
              label: "被举报次数",
              prop: "reportedUser.userReportedCount",
              width: 100,
              addDisplay: false,
              editDisplay: false,
              search: false,
              formatter: (row, value, label, column) => {
                return row.reportedUser ? (row.reportedUser.userReportedCount || 0) : 0;
              }
            },
            {
              label: "审核状态",
              prop: "auditStatus",
              width: 120,
              type: "select",
              dicData: [
                { label: "待审核", value: "0" },
                { label: "审核通过", value: "1" },
                { label: "审核拒绝", value: "2" }
              ],
              search: true,
              formatter: (row, value, label, column) => {
                const statusMap = { "0": "待审核", "1": "审核通过", "2": "审核拒绝" };
                return statusMap[row.post.auditStatus] || "未知";
              }
            },
            {
              label: "创建时间",
              prop: "createTime",
              width: 160,
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              addDisplay: false,
              editDisplay: false,
              search: false
            },
          ]
        },
        data: [],
        postDetailVisible: false,
        currentPost: null,
        currentPostStats: null
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.report_add, false),
          viewBtn: this.validData(this.permission.report_view, false),
          delBtn: this.validData(this.permission.report_delete, false),
          editBtn: this.validData(this.permission.report_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      // 查看帖子详情
      handleViewPost(row) {
        if (!row.post) {
          this.$message.warning("该举报没有关联的帖子信息");
          return;
        }
        this.currentPost = {...row.post, ...row.postCategory};
        this.currentPostStats = row.postStats;
        this.postDetailVisible = true;
      },
      // 获取审核状态文本
      getAuditStatusText(status) {
        const statusMap = { "0": "待审核", "1": "审核通过", "2": "审核拒绝" };
        return statusMap[status] || "未知";
      },
      // 获取审核状态类型
      getAuditStatusType(status) {
        const typeMap = { "0": "warning", "1": "success", "2": "danger" };
        return typeMap[status] || "info";
      },
      // 获取图片列表
      getImageList(images) {
        if (!images) return [];
        try {
          return typeof images === 'string' ? JSON.parse(images) : images;
        } catch (e) {
          return images.split(',').filter(img => img.trim());
        }
      },
      // 预览图片
      previewImage(imageUrl) {
        // 这里可以使用 Element Plus 的图片预览组件
        window.open(imageUrl, '_blank');
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
