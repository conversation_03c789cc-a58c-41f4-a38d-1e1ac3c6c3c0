package org.springblade.business.config.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.AllArgsConstructor;
import org.springblade.core.boot.ctrl.BladeController;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.business.config.entity.MiniappRegionConfig;
import org.springblade.miniapp.service.WeChatRegionService;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 小程序地区配置管理控制器
 *
 * <AUTHOR>
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/miniapp-region-config")
@Tag(name = "小程序地区配置管理", description = "小程序地区配置管理接口")
public class RegionConfigController extends BladeController {

	private final WeChatRegionService miniappRegionService;

	/**
	 * 分页查询小程序地区配置
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "分页查询", description = "分页查询小程序地区配置")
	public R<IPage<MiniappRegionConfig>> list(MiniappRegionConfig config, Query query) {
		IPage<MiniappRegionConfig> pages = miniappRegionService.page(
			Condition.getPage(query),
			Condition.getQueryWrapper(config)
		);
		return R.data(pages);
	}

	/**
	 * 根据层级查询地区配置
	 */
	@GetMapping("/list-by-level")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "按层级查询", description = "根据层级查询地区配置")
	public R<List<MiniappRegionConfig>> listByLevel(@Parameter(description = "地区层级", required = true) @RequestParam Integer level) {
		List<MiniappRegionConfig> configs = miniappRegionService.list(
			Wrappers.<MiniappRegionConfig>lambdaQuery()
				.eq(MiniappRegionConfig::getLevel, level)
				.orderByAsc(MiniappRegionConfig::getSort)
		);
		return R.data(configs);
	}

	/**
	 * 根据父级编码查询子级地区配置
	 */
	@GetMapping("/list-by-parent")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "按父级查询", description = "根据父级编码查询子级地区配置")
	public R<List<MiniappRegionConfig>> listByParent(@Parameter(description = "父级编码", required = true) @RequestParam String parentCode) {
		List<MiniappRegionConfig> configs = miniappRegionService.list(
			Wrappers.<MiniappRegionConfig>lambdaQuery()
				.eq(MiniappRegionConfig::getParentCode, parentCode)
				.orderByAsc(MiniappRegionConfig::getSort)
		);
		return R.data(configs);
	}

	/**
	 * 配置地区开放状态
	 */
	@PostMapping("/config-open")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "配置开放状态", description = "配置地区开放状态")
	public R configOpen(
		@Parameter(description = "地区编码", required = true) @RequestParam String regionCode,
		@Parameter(description = "是否开放 0-关闭 1-开放", required = true) @RequestParam Integer isOpen) {

		// 参数验证
		if (regionCode == null || regionCode.trim().isEmpty()) {
			return R.fail("地区编码不能为空");
		}
		if (isOpen == null || (isOpen != 0 && isOpen != 1)) {
			return R.fail("开放状态参数错误，只能是0或1");
		}

		boolean result = miniappRegionService.configRegionOpen(regionCode, isOpen);
		return R.status(result);
	}

	/**
	 * 批量配置地区开放状态
	 */
	@PostMapping("/batch-config-open")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "批量配置开放状态", description = "批量配置地区开放状态")
	public R batchConfigOpen(@RequestBody BatchConfigRequest request) {
		boolean result = miniappRegionService.batchConfigRegionOpen(request.getRegionCodes(), request.getIsOpen());
		return R.status(result);
	}

	/**
	 * 同步地区数据
	 */
	@PostMapping("/sync-data")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "同步地区数据", description = "同步地区数据到小程序配置表")
	public R syncData() {
		boolean result = miniappRegionService.syncRegionData();
		return R.status(result);
	}



	/**
	 * 批量配置请求对象
	 */
	public static class BatchConfigRequest {
		private List<String> regionCodes;
		private Integer isOpen;

		public List<String> getRegionCodes() {
			return regionCodes;
		}

		public void setRegionCodes(List<String> regionCodes) {
			this.regionCodes = regionCodes;
		}

		public Integer getIsOpen() {
			return isOpen;
		}

		public void setIsOpen(Integer isOpen) {
			this.isOpen = isOpen;
		}
	}

}
