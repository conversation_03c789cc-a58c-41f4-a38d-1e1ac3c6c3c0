# 文件分享功能API接口文档

## 功能概述

文件分享功能提供文件上传和文本分享服务，支持：
- 文件上传后生成6位分享密钥，有效期1天
- 文本内容分享，生成6位分享密钥，有效期1天
- 通过密钥下载文件或获取文本内容
- 自动过期处理

## 接口列表

### 1. 上传文件

**接口地址**: `POST /blade-fileshare/upload`

**接口描述**: 上传文件并生成6位分享密钥，有效期1天

**请求参数**:
- `file` (MultipartFile): 上传的文件

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "shareKey": "ABC123",
    "shareType": "FILE",
    "fileName": "test.txt",
    "fileSize": 1024,
    "fileType": "text/plain",
    "expireTime": "2024-01-02T10:30:00",
    "downloadCount": 0,
    "status": 0,
    "createTime": "2024-01-01T10:30:00"
  },
  "msg": "操作成功"
}
```

### 2. 分享文本

**接口地址**: `POST /blade-fileshare/share-text`

**接口描述**: 分享文本内容并生成6位分享密钥，有效期1天

**请求参数**:
```json
{
  "shareType": "TEXT",
  "textContent": "要分享的文本内容，不超过1024个字符"
}
```

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "shareKey": "DEF456",
    "shareType": "TEXT",
    "textContent": "要分享的文本内容，不超过1024个字符",
    "expireTime": "2024-01-02T10:30:00",
    "downloadCount": 0,
    "status": 0,
    "createTime": "2024-01-01T10:30:00"
  },
  "msg": "操作成功"
}
```

### 3. 获取分享信息

**接口地址**: `GET /blade-fileshare/info/{shareKey}`

**接口描述**: 通过6位分享密钥获取分享信息

**路径参数**:
- `shareKey` (String): 6位分享密钥

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": {
    "shareKey": "ABC123",
    "shareType": "FILE",
    "fileName": "test.txt",
    "fileSize": 1024,
    "fileType": "text/plain",
    "expireTime": "2024-01-02T10:30:00",
    "downloadCount": 0,
    "status": 0,
    "createTime": "2024-01-01T10:30:00"
  },
  "msg": "操作成功"
}
```

### 4. 下载文件

**接口地址**: `GET /blade-fileshare/download/{shareKey}`

**接口描述**: 通过6位分享密钥下载文件

**路径参数**:
- `shareKey` (String): 6位分享密钥

**响应**: 文件流，浏览器会自动下载文件

### 5. 获取文本内容

**接口地址**: `GET /blade-fileshare/text/{shareKey}`

**接口描述**: 通过6位分享密钥获取文本内容

**路径参数**:
- `shareKey` (String): 6位分享密钥

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": "要分享的文本内容，不超过1024个字符",
  "msg": "操作成功"
}
```

### 6. 生成分享密钥（测试用）

**接口地址**: `GET /blade-fileshare/generate-key`

**接口描述**: 生成6位随机分享密钥（测试用）

**响应示例**:
```json
{
  "code": 200,
  "success": true,
  "data": "XYZ789",
  "msg": "操作成功"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 分享记录不存在 |
| 410 | 分享已过期 |
| 500 | 服务器内部错误 |

## 错误响应示例

```json
{
  "code": 404,
  "success": false,
  "data": null,
  "msg": "分享记录不存在"
}
```

```json
{
  "code": 410,
  "success": false,
  "data": null,
  "msg": "分享已过期"
}
```

## 使用示例

### 1. 上传文件示例

```bash
curl -X POST "http://localhost:80/blade-fileshare/upload" \
  -H "Content-Type: multipart/form-data" \
  -F "file=@/path/to/your/file.txt"
```

### 2. 分享文本示例

```bash
curl -X POST "http://localhost:80/blade-fileshare/share-text" \
  -H "Content-Type: application/json" \
  -d '{
    "shareType": "TEXT",
    "textContent": "这是一个测试文本内容"
  }'
```

### 3. 获取分享信息示例

```bash
curl -X GET "http://localhost:80/blade-fileshare/info/ABC123"
```

### 4. 下载文件示例

```bash
curl -X GET "http://localhost:80/blade-fileshare/download/ABC123" \
  -o downloaded_file.txt
```

### 5. 获取文本内容示例

```bash
curl -X GET "http://localhost:80/blade-fileshare/text/DEF456"
```

## 注意事项

1. **文件大小限制**: 建议上传文件大小不超过100MB
2. **文本长度限制**: 文本内容不能超过1024个字符
3. **有效期**: 所有分享的有效期都是1天
4. **密钥格式**: 分享密钥为6位大写字母和数字的组合
5. **文件存储**: 文件存储在服务器的 `uploads/fileshare/` 目录下
6. **安全性**: 建议在生产环境中添加访问频率限制和文件类型验证

## 数据库表结构

```sql
CREATE TABLE `file_share` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `share_key` varchar(6) NOT NULL COMMENT '分享密钥（6位字符）',
  `share_type` varchar(10) NOT NULL COMMENT '分享类型（FILE-文件，TEXT-文本）',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件类型',
  `text_content` text COMMENT '文本内容',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `status` int DEFAULT '0' COMMENT '状态（0-正常，1-已过期，2-已删除）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `update_user` bigint DEFAULT NULL COMMENT '更新人',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_key` (`share_key`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件分享表';
``` 