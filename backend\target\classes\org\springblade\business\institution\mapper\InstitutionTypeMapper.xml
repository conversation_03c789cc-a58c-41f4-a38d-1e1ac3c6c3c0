<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.institution.mapper.InstitutionTypeMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="institutionTypeResultMap" type="org.springblade.business.institution.entity.InstitutionType">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="name" property="name"/>
        <result column="icon" property="icon"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectInstitutionTypePage" resultMap="institutionTypeResultMap">
        select * from urb_institution_type where is_deleted = 0
    </select>

</mapper>
