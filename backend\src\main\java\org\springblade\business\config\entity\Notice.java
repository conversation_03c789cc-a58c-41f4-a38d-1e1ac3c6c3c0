/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;

import org.apache.ibatis.type.Alias;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 通知公告表实体类
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
@TableName("urb_notice")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "通知公告表")
@Alias("BusinessNotice")
public class Notice extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 公告标题
     */
    @Schema(description = "公告标题")
    private String title;
    /**
     * 公告内容
     */
    @Schema(description = "公告内容")
    private String content;
    /**
     * 分类
     */
    @Schema(description = "分类")
    private String tag;


}
