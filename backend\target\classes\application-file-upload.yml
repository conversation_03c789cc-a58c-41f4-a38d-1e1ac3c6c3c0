# 文件上传配置示例
blade:
  storage:
    # 存储提供商：aliyun-oss, cloudflare
    provider: aliyun-oss

    # 阿里云OSS配置
    aliyun-oss:
      access-key-id: LTAI5tSfX9tTMf79fvC26oX3
      access-key-secret: ******************************
      endpoint: oss-cn-hangzhou.aliyuncs.com
      bucket-name: gerenuk
      domain: https://gerenuk.oss-cn-hangzhou.aliyuncs.com   # 可选，自定义域名
      region: cn-hangzhou

    # Cloudflare配置
    cloudflare:
      account-id: your-account-id
      api-token: your-api-token
      bucket-name: your-bucket-name
      domain: https://your-domain.com

    # 文件上传配置
    upload:
      # 最大文件大小（MB）
      max-file-size: 10

      # 允许的文件类型
      allowed-types:
        - image/jpeg
        - image/png
        - image/gif
        - image/webp
        - application/pdf
        - application/msword
        - application/vnd.openxmlformats-officedocument.wordprocessingml.document
        - text/plain
        - text/csv
        - video/mp4
        - video/avi
        - audio/mpeg
        - audio/wav

      # 存储路径前缀
      path-prefix: uploads

      # 是否生成缩略图
      generate-thumbnail: true

      # 缩略图尺寸
      thumbnail-width: 200
      thumbnail-height: 200

# 文件上传大小限制
spring:
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
