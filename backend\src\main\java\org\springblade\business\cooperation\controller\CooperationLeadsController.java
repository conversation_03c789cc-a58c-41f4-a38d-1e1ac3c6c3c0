/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.cooperation.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.cooperation.entity.CooperationLeads;
import org.springblade.business.cooperation.vo.CooperationLeadsVO;
import org.springblade.business.cooperation.wrapper.CooperationLeadsWrapper;
import org.springblade.business.cooperation.service.ICooperationLeadsService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 合作线索表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/cooperationleads")
@io.swagger.v3.oas.annotations.tags.Tag(name = "合作线索表", description = "合作线索表接口")
public class CooperationLeadsController extends BladeController {

	private ICooperationLeadsService cooperationLeadsService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入cooperationLeads")
	public R<CooperationLeadsVO> detail(CooperationLeads cooperationLeads) {
		CooperationLeads detail = cooperationLeadsService.getOne(Condition.getQueryWrapper(cooperationLeads));
		return R.data(CooperationLeadsWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 合作线索表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入cooperationLeads")
	public R<IPage<CooperationLeadsVO>> list(CooperationLeads cooperationLeads, Query query) {
		IPage<CooperationLeads> pages = cooperationLeadsService.page(Condition.getPage(query), Condition.getQueryWrapper(cooperationLeads));
		return R.data(CooperationLeadsWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 合作线索表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入cooperationLeads")
	public R<IPage<CooperationLeadsVO>> page(CooperationLeadsVO cooperationLeads, Query query) {
		IPage<CooperationLeadsVO> pages = cooperationLeadsService.selectCooperationLeadsPage(Condition.getPage(query), cooperationLeads);
		return R.data(pages);
	}

	/**
	 * 新增 合作线索表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入cooperationLeads")
	public R save(@Valid @RequestBody CooperationLeads cooperationLeads) {
		return R.status(cooperationLeadsService.save(cooperationLeads));
	}

	/**
	 * 修改 合作线索表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入cooperationLeads")
	public R update(@Valid @RequestBody CooperationLeads cooperationLeads) {
		return R.status(cooperationLeadsService.updateById(cooperationLeads));
	}

	/**
	 * 新增或修改 合作线索表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入cooperationLeads")
	public R submit(@Valid @RequestBody CooperationLeads cooperationLeads) {
		return R.status(cooperationLeadsService.saveOrUpdate(cooperationLeads));
	}

	
	/**
	 * 删除 合作线索表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(cooperationLeadsService.deleteLogic(Func.toLongList(ids)));
	}

	
}
