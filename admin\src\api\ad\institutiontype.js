import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/institutiontype/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getPage = (current, size, params) => {
  return request({
    url: '/blade-ad/institutiontype/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-ad/institutiontype/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/institutiontype/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-ad/institutiontype/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-ad/institutiontype/update',
    method: 'post',
    data: row
  })
}

export const submit = (row) => {
  return request({
    url: '/blade-ad/institutiontype/submit',
    method: 'post',
    data: row
  })
}

