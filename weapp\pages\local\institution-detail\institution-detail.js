/**
 * 机构详情页面
 * 按照小程序开发规范重构，使用Store模式管理数据
 */

const {
  getInstitutionDetail,
  getInstitutionServices,
  getInstitutionReviews,
  toggleInstitutionFavorite,
  bookInstitutionService,
  recordInstitutionView,
  getDefaultInstitutionDetail
} = require('../../../../stores/institutionDetailStore.js');

Page({
  data: {
    // 机构详情
    institution: null,
    // 加载状态
    loading: true,
    // 机构ID
    institutionId: '',
    // 服务列表
    serviceList: [],
    // 评价列表
    reviewList: [],
    // 当前Tab
    currentTab: 'info', // info, service, review
    // 操作状态
    favoriteLoading: false,
    bookingLoading: false,
    // 错误状态
    error: null
  },

  /**
   * 页面加载
   */
  onLoad(options) {
    const { id } = options;
    if (id) {
      this.setData({
        institutionId: id,
        institution: getDefaultInstitutionDetail()
      });
      this.initPageData(id);
    } else {
      this.handleError('参数错误', true);
    }
  },

  /**
   * 初始化页面数据
   */
  async initPageData(id) {
    try {
      // 记录浏览
      recordInstitutionView(id);

      // 加载机构详情
      await this.loadInstitutionDetail(id);
    } catch (error) {
      this.handleError('页面初始化失败');
    }
  },

  /**
   * 加载机构详情
   */
  async loadInstitutionDetail(id) {
    try {
      this.setData({ loading: true, error: null });

      const institution = await getInstitutionDetail(id);

      this.setData({
        institution,
        loading: false
      });

      // 设置页面标题
      wx.setNavigationBarTitle({
        title: institution.name || '机构详情'
      });

      // 加载服务列表
      this.loadServiceList(id);

    } catch (error) {
      console.error('加载机构详情失败:', error);
      this.setData({
        loading: false,
        error: error.message || '加载失败'
      });

      this.handleError('加载失败，请重试');
    }
  },

  /**
   * 加载服务列表
   */
  async loadServiceList(id) {
    try {
      const serviceList = await getInstitutionServices(id);
      this.setData({ serviceList });
    } catch (error) {
      console.error('加载服务列表失败:', error);
      // 服务列表加载失败不影响主要功能，只记录日志
    }
  },

  /**
   * 切换Tab
   */
  onTabChange(e) {
    const tab = e.currentTarget.dataset.tab;
    this.setData({ currentTab: tab });

    if (tab === 'review' && this.data.reviewList.length === 0) {
      this.loadReviewList();
    }
  },

  /**
   * 加载评价列表
   */
  async loadReviewList() {
    try {
      const reviewList = await getInstitutionReviews(this.data.institutionId);
      this.setData({ reviewList });
    } catch (error) {
      console.error('加载评价列表失败:', error);
      // 评价列表加载失败不影响主要功能
    }
  },

  /**
   * 拨打电话
   */
  onCallPhone() {
    const phone = this.data.institution?.phone;
    if (phone) {
      wx.makePhoneCall({
        phoneNumber: phone,
        fail: (error) => {
          console.error('拨打电话失败:', error);
          wx.showToast({
            title: '拨打失败',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '暂无联系电话',
        icon: 'none'
      });
    }
  },

  /**
   * 查看位置
   */
  onViewLocation() {
    const institution = this.data.institution;
    if (institution?.latitude && institution?.longitude) {
      wx.openLocation({
        latitude: parseFloat(institution.latitude),
        longitude: parseFloat(institution.longitude),
        name: institution.name,
        address: institution.address,
        fail: (error) => {
          console.error('打开地图失败:', error);
          wx.showToast({
            title: '打开地图失败',
            icon: 'none'
          });
        }
      });
    } else {
      wx.showToast({
        title: '暂无位置信息',
        icon: 'none'
      });
    }
  },

  /**
   * 分享机构
   */
  onShareAppMessage() {
    const institution = this.data.institution;
    return {
      title: `推荐机构：${institution?.name || '本地机构'}`,
      path: `/pkg_user/pages/local/institution-detail/institution-detail?id=${this.data.institutionId}`,
      imageUrl: institution?.logo || ''
    };
  },

  /**
   * 收藏/取消收藏机构
   */
  async onToggleFavorite() {
    if (this.data.favoriteLoading) return;

    try {
      this.setData({ favoriteLoading: true });

      const currentFavorite = this.data.institution.isFavorite;
      const newFavoriteStatus = await toggleInstitutionFavorite(
        this.data.institutionId,
        currentFavorite
      );

      this.setData({
        'institution.isFavorite': newFavoriteStatus,
        favoriteLoading: false
      });

      wx.showToast({
        title: newFavoriteStatus ? '已收藏' : '已取消收藏',
        icon: 'success'
      });

    } catch (error) {
      console.error('收藏操作失败:', error);
      this.setData({ favoriteLoading: false });

      wx.showToast({
        title: error.message || '操作失败',
        icon: 'none'
      });
    }
  },

  /**
   * 预约服务
   */
  onBookService(e) {
    const service = e.currentTarget.dataset.service;

    if (!service || !service.isAvailable) {
      wx.showToast({
        title: '该服务暂不可用',
        icon: 'none'
      });
      return;
    }

    wx.showModal({
      title: '预约服务',
      content: `确定要预约"${service.name}"服务吗？`,
      success: (res) => {
        if (res.confirm) {
          this.bookService(service);
        }
      }
    });
  },

  /**
   * 执行预约
   */
  async bookService(service) {
    if (this.data.bookingLoading) return;

    try {
      this.setData({ bookingLoading: true });

      await bookInstitutionService(this.data.institutionId, service.id);

      this.setData({ bookingLoading: false });

      wx.showToast({
        title: '预约成功',
        icon: 'success'
      });

    } catch (error) {
      console.error('预约失败:', error);
      this.setData({ bookingLoading: false });

      wx.showToast({
        title: error.message || '预约失败',
        icon: 'none'
      });
    }
  },

  /**
   * 处理错误
   */
  handleError(message, shouldGoBack = false) {
    wx.showToast({
      title: message,
      icon: 'none'
    });

    if (shouldGoBack) {
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 重新加载数据
   */
  onRetry() {
    if (this.data.institutionId) {
      this.initPageData(this.data.institutionId);
    }
  },

  /**
   * 下拉刷新
   */
  async onPullDownRefresh() {
    try {
      if (this.data.institutionId) {
        await this.loadInstitutionDetail(this.data.institutionId);
      }
    } finally {
      wx.stopPullDownRefresh();
    }
  },

  /**
   * 预览图片
   */
  onPreviewImage(e) {
    const { url, urls } = e.currentTarget.dataset;
    wx.previewImage({
      current: url,
      urls: urls || [url]
    });
  }
});
