-- 文件上传表
CREATE TABLE `blade_file_upload` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  
  -- 文件基本信息
  `original_name` varchar(255) DEFAULT NULL COMMENT '原始文件名',
  `file_name` varchar(255) DEFAULT NULL COMMENT '存储文件名',
  `file_extension` varchar(50) DEFAULT NULL COMMENT '文件扩展名',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `content_type` varchar(100) DEFAULT NULL COMMENT '文件类型（MIME类型）',
  `file_category` varchar(50) DEFAULT NULL COMMENT '文件分类',
  
  -- 上传信息
  `upload_source` varchar(50) DEFAULT NULL COMMENT '上传来源',
  `storage_provider` varchar(50) DEFAULT NULL COMMENT '云存储提供商',
  `bucket_name` varchar(100) DEFAULT NULL COMMENT '存储桶名称',
  `storage_path` varchar(500) DEFAULT NULL COMMENT '存储路径',
  `access_url` varchar(500) DEFAULT NULL COMMENT '访问URL',
  `thumbnail_url` varchar(500) DEFAULT NULL COMMENT '缩略图URL',
  `file_md5` varchar(32) DEFAULT NULL COMMENT '文件MD5值',
  
  -- 业务关联
  `business_id` bigint DEFAULT NULL COMMENT '关联业务ID',
  `business_type` varchar(100) DEFAULT NULL COMMENT '关联业务类型',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注信息',
  
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_upload_source` (`upload_source`),
  KEY `idx_file_category` (`file_category`),
  KEY `idx_business` (`business_type`, `business_id`),
  KEY `idx_file_md5` (`file_md5`),
  KEY `idx_create_user` (`create_user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='文件上传表';

-- 插入示例数据
INSERT INTO `blade_file_upload` (
  `id`, `create_time`, `create_user`, `original_name`, `file_name`, 
  `file_extension`, `file_size`, `content_type`, `file_category`, 
  `upload_source`, `storage_provider`, `bucket_name`, `storage_path`, 
  `access_url`, `file_md5`, `business_type`, `business_id`, `remark`
) VALUES (
  1, NOW(), 1, '示例图片.jpg', 'abc123.jpg', 
  'jpg', 1024000, 'image/jpeg', 'image', 
  'admin', 'aliyun-oss', 'my-bucket', 'uploads/admin/2024/01/01/abc123.jpg', 
  'https://my-bucket.oss-cn-hangzhou.aliyuncs.com/uploads/admin/2024/01/01/abc123.jpg', 
  'd41d8cd98f00b204e9800998ecf8427e', 'post', 1, '示例文件'
); 