import request from '@/axios';

// 获取标签列表（分页）
export const getTagList = (params) => {
  return request({
    url: '/blade-ad/tag/admin/list',
    method: 'get',
    params
  });
};

// 根据分类和标签类型获取标签
export const getTagsByCategoryAndType = (categoryId, type) => {
  return request({
    url: '/blade-ad/tag/admin/categorisation',
    method: 'get',
    params: { categoryId, type }
  });
};

// 创建标签并添加到分类
export const createTag = (data) => {
  return request({
    url: '/blade-ad/tag/admin/create',
    method: 'post',
    data
  });
};
// 绑定标签
export const bindTag = (data) => {
  return request({
    url: '/blade-ad/tag',
    method: 'post',
    data
  });
};

export const add = (data) => {
  return request({
    url: '/blade-ad/tag/submit',
    method: 'post',
    data
  })
}

export const update = (data) => {
  return request({
    url: '/blade-ad/tag/update',
    method: 'put',
    data
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/tag/remove',
    method: 'post',
    params:{ids}
  })
}

// 从分类移除标签
export const removeTagFromCategory = (data) => {
  return request({
    url: '/blade-ad/tag/admin/remove',
    method: 'delete',
    data
  });
};

export const getDetail = (id) => {
  return request({
    url: `/blade-ad/tag/detail`,
    method: 'get',
    params: { id }
  });
};

// 根据分类获取标签
export const getTagsByCategory = (categoryId) => {
  return request({
    url: `/blade-ad/tag/admin/category/${categoryId}`,
    method: 'get'
  })
}

// 获取热门标签
export const getHotTags = (limit = 10) => {
  return request({
    url: '/blade-ad/tag/admin/hot',
    method: 'get',
    params: { limit }
  });
};

