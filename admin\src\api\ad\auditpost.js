import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/post/admin/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-ad/post/admin/detail/' + id,
    method: 'get'
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/post/admin/batch',
    method: 'delete',
    params: {
      postIds: ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-ad/post/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-ad/post/admin/audit',
    method: 'post',
    data: row
  })
}
// 批量审核
export const batchAudit = (auditData) => {
  return request({
    url: '/blade-ad/post/batch-audit',
    method: 'post',
    params: auditData
  })
}

// 获取审核统计
export const getAuditStats = () => {
  return request({
    url: '/blade-ad/post/audit-stats',
    method: 'get'
  })
}

// 获取帖子统计
export const getPostStats = () => {
  return request({
    url: '/blade-ad/post/stats',
    method: 'get'
  })
}

