/* pkg_user/pages/local/local.wxss */

/* 页面整体 */
page {
  height: 100vh;
  background: #f5f5f5;
  overflow: hidden; /* 禁止页面整体滚动 */
}

/* 滚动容器 */
.scroll-container {
  position: relative;
  background: #f5f5f5;
  box-sizing: border-box;
  height: 100vh;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  scrollbar-width: none; /* Firefox */
}

.scroll-container::-webkit-scrollbar {
  display: none;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  z-index: 1;
  width: 100%;
  box-sizing: border-box;
  padding-bottom: env(safe-area-inset-bottom);
  min-height: calc(100vh - 100rpx); /* 确保内容区域有足够高度 */
}

/* 吸顶分类栏 */
.sticky-category-bar {
  position: fixed;
  left: 0;
  right: 0;
  z-index: 999;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

/* 刷新动画已移除 */

/* 主Tab区域样式 */
.main-tab-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
  background: #fff;
}

.main-tabs {
  display: flex;
  align-items: center;
}

.main-tab-item {
  padding: 16rpx 0;
  margin-right: 40rpx;
  font-size: 32rpx;
  font-weight: 500;
  color: #666;
  position: relative;
  transition: all 0.3s ease;
  cursor: pointer;
}

.main-tab-item:last-child {
  margin-right: 0;
}

.main-tab-item.active {
  color: #ff6b6b;
  font-weight: 600;
}

.main-tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 0;
  right: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #ff6b6b, #ff8585);
  border-radius: 2rpx;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    transform: scaleX(0);
  }
  to {
    transform: scaleX(1);
  }
}

/* 申请入驻按钮 */
.apply-settle-btn {
  padding: 12rpx 24rpx;
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  font-size: 26rpx;
  font-weight: 500;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.3);
  transition: all 0.3s ease;
}

.apply-settle-btn:active {
  transform: scale(0.95);
  box-shadow: 0 2rpx 8rpx rgba(255, 107, 107, 0.4);
}

/* 卡片区域 - 参考index页面样式 */
.card-section {
  background-color: var(--card-background, #fff);
  border-radius: 24rpx;
  margin: 0; /* 移除外边距，让内容填满 */
  overflow: hidden;
}

.card-section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 0 20rpx;
}

.section-title {
  display: flex;
  align-items: center;
}

.title-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 分类标签 */
.category-tabs-scroll {
  margin-bottom: 20rpx;
}

.category-tabs {
  display: flex;
  padding: 0 10rpx;
}

.category-tabs.single-tab {
  justify-content: center;
}

.tab-item {
  flex-shrink: 0;
  padding: 16rpx 28rpx;
  margin: 0 12rpx;
  background: #f8f9fa;
  border-radius: 24rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: center;
  white-space: nowrap;
  border: 2rpx solid transparent;
  font-weight: 500;
}

.tab-item.active {
  background: linear-gradient(135deg, #ff6b6b, #ff8585);
  color: #fff;
  transform: scale(1.05);
  border-color: rgba(255, 107, 107, 0.3);
  box-shadow: 0 4rpx 12rpx rgba(255, 107, 107, 0.25);
}

.tab-item:first-child {
  margin-left: 0;
}

.tab-item:last-child {
  margin-right: 0;
}

/* 机构列表容器 */
.institutions-container {
  padding: 0;
}

/* 底部状态区域 */
.bottom-status {
  padding: 40rpx 0;
  text-align: center;
}

.loading-more {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  color: #666;
}

.loading-more.preloading {
  color: #ff6b6b;
}

.reached-bottom,
.no-more {
  font-size: 26rpx;
  color: #999;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  margin: 0 20rpx;
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
  margin: 20rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.6;
  animation: float 3s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10rpx);
  }
}

.empty-text {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.empty-tip {
  font-size: 28rpx;
  color: #999;
  text-align: center;
  line-height: 1.5;
}

/* 旧样式已移除，使用组件化设计 */
