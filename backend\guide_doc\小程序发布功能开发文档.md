# 小程序发布功能开发文档

## 1. 功能概述

本文档描述了小程序发布功能的后端实现，包括帖子发布、审核、分类管理、标签管理等功能，同时支持后台管理系统的管理功能。

## 2. 技术架构

### 2.1 技术栈
- **框架**: Spring Boot 2.7.x
- **ORM**: MyBatis Plus
- **数据库**: MySQL 8.0
- **权限控制**: Spring Security + JWT
- **API文档**: Swagger 3.0
- **构建工具**: Maven

### 2.2 项目结构
```
backend/src/main/java/org/springblade/business/post/
├── controller/           # 控制器层
│   ├── MiniappPostController.java    # 小程序发布控制器
│   ├── AdminPostController.java      # 后台管理帖子控制器
│   ├── AdminCategoryController.java  # 后台管理分类控制器
│   └── AdminTagController.java       # 后台管理标签控制器
├── entity/              # 实体类
│   ├── SupPost.java     # 帖子实体
│   ├── Category.java    # 分类实体
│   └── Tag.java         # 标签实体
├── dto/                 # 数据传输对象
│   ├── PostPublishDTO.java  # 发布帖子DTO
│   └── PostAuditDTO.java    # 审核帖子DTO
├── service/             # 服务层
│   ├── ISupPostService.java
│   ├── ICategoryService.java
│   ├── ITagService.java
│   └── impl/            # 服务实现
└── mapper/              # 数据访问层
```

## 3. 数据库设计

### 3.1 主要表结构

#### 3.1.1 帖子表 (urb_post)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| title | varchar(200) | 标题 |
| content | text | 内容 |
| images | varchar(1000) | 图片 |
| address | varchar(200) | 发布地址 |
| geo_location | varchar(200) | 地理位置 |
| tags | varchar(500) | 标签 |
| contact_name | varchar(50) | 联系人姓名 |
| contact_phone | varchar(20) | 联系电话 |
| category_id | bigint | 分类ID |
| open_id | varchar(100) | 小程序用户OpenID |
| audit_status | varchar(20) | 审核状态 |
| like_count | int | 点赞数 |
| favorite_count | int | 收藏数 |
| view_count | int | 浏览数 |
| audit_remark | varchar(500) | 审核备注 |
| audit_time | datetime | 审核时间 |
| audit_user_id | bigint | 审核人ID |
| reject_reason | varchar(500) | 拒绝原因 |
| enable_audit | int | 是否启用审核 |

#### 3.1.2 分类表 (urb_category)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| name | varchar(100) | 分类名称 |
| parent_id | bigint | 上级分类ID |
| icon | varchar(200) | 分类图标 |
| description | varchar(500) | 分类描述 |
| sort | int | 排序 |
| enabled | int | 是否启用 |
| enable_audit | int | 是否启用审核 |
| tip | varchar(200) | 提示信息 |

#### 3.1.3 标签表 (urb_tag)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| name | varchar(50) | 标签名称 |
| category_id | bigint | 分类ID |
| color | varchar(20) | 标签颜色 |
| icon | varchar(200) | 标签图标 |
| sort | int | 排序 |
| enabled | int | 是否启用 |
| use_count | int | 使用次数 |
| is_system | int | 是否系统标签 |

### 3.2 关联表

#### 3.2.1 点赞表 (urb_post_like)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| post_id | bigint | 帖子ID |
| open_id | varchar(100) | 用户OpenID |
| create_time | datetime | 创建时间 |

#### 3.2.2 收藏表 (urb_post_favorite)
| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键 |
| post_id | bigint | 帖子ID |
| open_id | varchar(100) | 用户OpenID |
| create_time | datetime | 创建时间 |

## 4. 核心功能实现

### 4.1 帖子发布功能

#### 4.1.1 发布流程
1. 用户选择分类
2. 填写帖子信息（标题、内容、图片等）
3. 选择标签
4. 提交发布
5. 根据分类审核设置决定是否自动通过

#### 4.1.2 关键代码
```java
@Override
@Transactional(rollbackFor = Exception.class)
public SupPost publishPost(PostPublishDTO postPublishDTO) {
    // 检查分类是否存在且启用
    Category category = categoryService.getById(postPublishDTO.getCategoryId());
    if (category == null || category.getEnabled() == 0) {
        throw new RuntimeException("分类不存在或已禁用");
    }

    // 创建帖子
    SupPost post = new SupPost();
    // ... 设置帖子信息

    // 设置审核状态
    if (category.getEnableAudit() == 1) {
        post.setAuditStatus("PENDING");
    } else {
        post.setAuditStatus("APPROVED");
    }

    // 保存帖子
    save(post);

    // 更新标签使用次数
    if (CollUtil.isNotEmpty(postPublishDTO.getTags())) {
        updateTagUseCount(postPublishDTO.getTags());
    }

    return post;
}
```

### 4.2 审核功能

#### 4.2.1 审核流程
1. 管理员查看待审核帖子
2. 审核帖子内容
3. 通过或拒绝
4. 记录审核信息

#### 4.2.2 关键代码
```java
@Override
@Transactional(rollbackFor = Exception.class)
public Boolean auditPost(PostAuditDTO postAuditDTO) {
    SupPost post = getById(postAuditDTO.getPostId());
    if (post == null) {
        return false;
    }

    post.setAuditStatus(postAuditDTO.getAuditStatus());
    post.setAuditRemark(postAuditDTO.getAuditRemark());
    post.setAuditTime(LocalDateTime.now());
    post.setAuditUserId(Func.getUserId());

    if ("REJECTED".equals(postAuditDTO.getAuditStatus())) {
        post.setRejectReason(postAuditDTO.getRejectReason());
    }

    return updateById(post);
}
```

### 4.3 分类管理功能

#### 4.3.1 分类树形结构
```java
@Override
public List<Category> getCategoryTree() {
    // 获取所有分类
    List<Category> allCategories = list(queryWrapper);
    
    // 构建树形结构
    return buildCategoryTree(allCategories, 0L);
}

private List<Category> buildCategoryTree(List<Category> allCategories, Long parentId) {
    List<Category> tree = new ArrayList<>();
    
    for (Category category : allCategories) {
        if (parentId.equals(category.getParentId())) {
            // 递归构建子分类
            List<Category> children = buildCategoryTree(allCategories, category.getId());
            category.setChildren(children);
            
            // 加载标签
            List<Tag> tags = tagService.getTagsByCategory(category.getId());
            category.setTags(tags);
            
            tree.add(category);
        }
    }
    
    return tree;
}
```

### 4.4 标签管理功能

#### 4.4.1 动态标签创建
```java
@Override
@Transactional(rollbackFor = Exception.class)
public Tag createTag(String name, Long categoryId) {
    // 检查标签是否已存在
    Tag existingTag = getOne(queryWrapper);
    
    if (existingTag != null) {
        // 如果标签已存在，增加使用次数
        existingTag.setUseCount(existingTag.getUseCount() + 1);
        updateById(existingTag);
        return existingTag;
    }
    
    // 创建新标签
    Tag tag = new Tag();
    tag.setName(name);
    tag.setCategoryId(categoryId);
    tag.setUseCount(1);
    tag.setIsSystem(0); // 用户创建的标签
    
    save(tag);
    return tag;
}
```

## 5. API接口设计

### 5.1 小程序接口

#### 5.1.1 发布帖子
```
POST /miniapp/post/publish
Content-Type: application/json

{
    "title": "帖子标题",
    "content": "帖子内容",
    "images": ["图片1", "图片2"],
    "categoryId": 1,
    "tags": ["标签1", "标签2"],
    "contactName": "联系人",
    "contactPhone": "联系电话",
    "openId": "用户OpenID"
}
```

#### 5.1.2 获取分类列表
```
GET /miniapp/post/categories
```

#### 5.1.3 根据分类获取标签
```
GET /miniapp/post/tags/{categoryId}
```

### 5.2 后台管理接口

#### 5.2.1 审核帖子
```
POST /admin/post/audit
Content-Type: application/json

{
    "postId": 1,
    "auditStatus": "APPROVED",
    "auditRemark": "审核备注"
}
```

#### 5.2.2 获取分类树
```
GET /admin/category/tree
```

#### 5.2.3 启用/禁用分类审核
```
POST /admin/category/{id}/enable-audit?enableAudit=true
```

## 6. 权限控制

### 6.1 小程序接口权限
- 小程序接口无需登录验证
- 通过OpenID识别用户身份

### 6.2 后台管理权限
- 需要管理员登录
- 使用Spring Security进行权限控制
- 支持细粒度权限控制

```java
@PreAuth("hasPermission('post:audit')")
public R<Boolean> auditPost(@Valid @RequestBody PostAuditDTO postAuditDTO) {
    // 审核逻辑
}
```

## 7. 数据验证

### 7.1 实体验证
```java
@Data
@Schema(description = "小程序发布帖子DTO")
public class PostPublishDTO {
    @NotBlank(message = "标题不能为空")
    @Schema(description = "标题")
    private String title;
    
    @NotBlank(message = "内容不能为空")
    @Schema(description = "内容")
    private String content;
    
    @NotNull(message = "分类不能为空")
    @Schema(description = "分类ID")
    private Long categoryId;
}
```

### 7.2 业务验证
- 分类存在性验证
- 分类启用状态验证
- 用户权限验证
- 数据完整性验证

## 8. 性能优化

### 8.1 数据库优化
- 添加必要的索引
- 使用分页查询
- 优化SQL语句

### 8.2 缓存策略
- 分类数据缓存
- 标签数据缓存
- 热门帖子缓存

### 8.3 异步处理
- 图片上传异步处理
- 审核通知异步发送
- 统计数据异步更新

## 9. 安全考虑

### 9.1 数据安全
- 输入数据验证和过滤
- SQL注入防护
- XSS攻击防护

### 9.2 接口安全
- 接口频率限制
- 数据加密传输
- 敏感信息脱敏

### 9.3 权限安全
- 细粒度权限控制
- 操作日志记录
- 异常监控告警

## 10. 部署说明

### 10.1 数据库初始化
```sql
-- 执行数据库更新脚本
source post_management_update.sql
```

### 10.2 配置文件
```yaml
# application.yml
spring:
  datasource:
    url: ********************************************************************************************************
    username: root
    password: password
  
  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
```

### 10.3 启动命令
```bash
# 打包
mvn clean package -Dmaven.test.skip=true

# 启动
java -jar target/urb-post-1.0.0.jar
```

## 11. 测试建议

### 11.1 单元测试
- 服务层方法测试
- 数据验证测试
- 异常处理测试

### 11.2 集成测试
- API接口测试
- 数据库操作测试
- 权限控制测试

### 11.3 性能测试
- 并发发布测试
- 大量数据查询测试
- 系统压力测试

## 12. 监控运维

### 12.1 日志监控
- 操作日志记录
- 错误日志监控
- 性能日志分析

### 12.2 业务监控
- 发布量监控
- 审核效率监控
- 用户活跃度监控

### 12.3 系统监控
- 服务器资源监控
- 数据库性能监控
- 接口响应时间监控

## 13. 扩展功能

### 13.1 未来规划
- 消息推送功能
- 用户积分系统
- 内容推荐算法
- 多语言支持

### 13.2 技术升级
- 微服务架构
- 容器化部署
- 云原生支持
- AI内容审核

---

**文档版本**: v1.0  
**更新时间**: 2025-03-10  
**维护人员**: 开发团队 