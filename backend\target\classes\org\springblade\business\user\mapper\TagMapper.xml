<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.TagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="tagResultMap" type="org.springblade.business.post.entity.Tag">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="tag_name" property="tagName"/>
        <result column="description" property="description"/>
        <result column="`sort`" property="sort"/>
    </resultMap>


    <select id="selectTagPage" resultMap="tagResultMap">
        select * from urb_tag
        where is_deleted = 0
        <if test="tag.tagName != null and tag.tagName != ''">
            and tag_name like concat('%', #{tag.tagName}, '%')
        </if>
        <if test="tag.categoryId != null">
            and category_id = #{tag.categoryId}
        </if>
        <if test="tag.enabled != null">
            and enabled = #{tag.enabled}
        </if>
        <if test="tag.type != null">
            and type = #{tag.type}
        </if>
        order by sort asc,sort_order asc, create_time desc
    </select>

</mapper>
