import request from '@/axios'

/**
 * 文件上传接口
 */
export const FileUploadAPI = {
  /**
   * 分页查询文件上传
   */
  getFileUploadPage: (current, size, params = {}) => {
    return request({
      url: '/blade-system/file-upload/list',
      method: 'get',
      params: {
        current,
        size,
        ...params
      }
    })
  },

  /**
   * 获取文件上传详情
   */
  getFileUploadDetail: (id) => {
    return request({
      url: '/blade-system/file-upload/detail',
      method: 'get',
      params: { id }
    })
  },

  /**
   * 新增文件上传
   */
  addFileUpload: (row) => {
    return request({
      url: '/blade-system/file-upload/submit',
      method: 'post',
      data: row
    })
  },

  /**
   * 修改文件上传
   */
  updateFileUpload: (row) => {
    return request({
      url: '/blade-system/file-upload/submit',
      method: 'post',
      data: row
    })
  },

  /**
   * 删除文件上传
   */
  removeFile: (ids) => {
    return request({
      url: '/blade-system/file-upload/remove',
      method: 'post',
      params: { ids }
    })
  },

  /**
   * 获取文件统计信息
   */
  getFileStats: () => {
    return request({
      url: '/blade-system/file-upload/stats',
      method: 'get'
    })
  },

  /**
   * 上传文件
   */
  uploadFile: (file, uploadSource, businessType, businessId) => {
    const formData = {}
    formData.append('file', file)
    formData.append('uploadSource', uploadSource)
    if (businessType) {
      formData.append('businessType', businessType)
    }
    if (businessId) {
      formData.append('businessId', businessId)
    }
    
    return request({
      url: '/blade-system/file-upload/upload',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 批量上传文件
   */
  uploadFiles: (files, uploadSource, businessType, businessId) => {
    const formData = {}
    files.forEach((file, index) => {
      formData.append(`files`, file)
    })
    formData.append('uploadSource', uploadSource)
    if (businessType) {
      formData.append('businessType', businessType)
    }
    if (businessId) {
      formData.append('businessId', businessId)
    }
    
    return request({
      url: '/blade-system/file-upload/upload-batch',
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  /**
   * 获取文件访问URL
   */
  getFileUrl: (fileId) => {
    return request({
      url: '/blade-system/file-upload/url',
      method: 'get',
      params: { fileId }
    })
  },

  /**
   * 下载文件
   */
  downloadFile: (fileId) => {
    return request({
      url: '/blade-system/file-upload/download',
      method: 'get',
      params: { fileId },
      responseType: 'blob'
    })
  },

  /**
   * 获取文件列表（不分页）
   */
  getFileList: (params) => {
    return request({
      url: '/blade-system/file-upload/list',
      method: 'get',
      params: params
    })
  },

  /**
   * 根据业务类型获取文件列表
   */
  getFilesByBusiness: (businessType, businessId) => {
    return request({
      url: '/blade-system/file-upload/business',
      method: 'get',
      params: { businessType, businessId }
    })
  },

  /**
   * 清理过期文件
   */
  cleanExpiredFiles: () => {
    return request({
      url: '/blade-system/file-upload/clean-expired',
      method: 'post'
    })
  },

  /**
   * 获取存储配置
   */
  getStorageConfig: () => {
    return request({
      url: '/blade-system/file-upload/storage-config',
      method: 'get'
    })
  },

  /**
   * 更新存储配置
   */
  updateStorageConfig: (config) => {
    return request({
      url: '/blade-system/file-upload/storage-config',
      method: 'post',
      data: config
    })
  }
} 