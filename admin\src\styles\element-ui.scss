.el-card.is-always-shadow {
    box-shadow: none;
    border: none !important;
}
.el-menu {
    border-right: none;
}

.el-message__icon,
.el-message__content {
    display: inline-block;
}

.el-date-editor .el-range-input,
.el-date-editor .el-range-separator {
    height: auto;
    overflow: hidden;
}

.el-dialog__wrapper {
    z-index: 2048;
}


.el-col {
    margin-bottom: 8px;
}

.el-main {
    padding: 0 !important;
}
.el-dropdown-menu__item--divided:before, .el-menu, .el-menu--horizontal>.el-menu-item:not(.is-disabled):focus, .el-menu--horizontal>.el-menu-item:not(.is-disabled):hover, .el-menu--horizontal>.el-sub-menu .el-sub-menu__title:hover {
    background-color:transparent;
}


.el-dropdown-menu__item--divided:before, .el-menu, .el-menu--horizontal>.el-menu-item:not(.is-disabled):focus, .el-menu--horizontal>.el-menu-item:not(.is-disabled):hover, .el-menu--horizontal>.el-sub-menu .el-sub-menu__title:hover{
    background-color: transparent !important;
}
.el-collapse-item__header{
  height:auto;
  overflow: hidden;
}

.el-button.is-text:not(.is-disabled):active{
  background-color: transparent;
}
.el-button.is-text:not(.is-disabled):focus, .el-button.is-text:not(.is-disabled):hover{
  background-color: transparent;
}
.avue-icon i, .avue-icon svg{
  line-height: 20px;
}