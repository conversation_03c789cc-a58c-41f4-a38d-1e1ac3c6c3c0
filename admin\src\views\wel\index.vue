<template>
  <basic-container>
    <div style="display: flex; gap: 24px; margin-bottom: 16px;">
      <div style="background: #fff; border-radius: 8px; padding: 16px; flex: 1; min-width: 180px;">
        <div style="font-size: 14px; color: #888;">今日访问人数</div>
        <div style="font-size: 32px; font-weight: bold;">{{ todayCount }}</div>
        <div>
          <span :style="{color: percentColor, fontWeight: 'bold'}">
            {{ percentText }}
          </span>
          <span style="font-size: 12px; color: #aaa; margin-left: 8px;">较昨日</span>
        </div>
      </div>
    </div>
    <div style="margin-bottom: 16px;">
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @change="fetchStats"
        style="width: 300px;"
      />
    </div>
    <div ref="chartRef" style="width: 100%; height: 400px;"></div>
  </basic-container>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as echarts from 'echarts';
import axios from 'axios';

const chartRef = ref(null);
const dateRange = ref([]);
let chartInstance = null;

// 今日、昨日访问人数及环比
const todayCount = ref(0);
const yesterdayCount = ref(0);
const percentText = ref('');
const percentColor = ref('#333');

const fetchTodayAndYesterday = async () => {
  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);
  const format = d => d.toISOString().slice(0, 10);
  const todayStr = format(today);
  const yesterdayStr = format(yesterday);

  // 获取今日数据
  const resToday = await axios.get('/blade-ad/user/stats/getUserRequestStats', {
    params: { startDate: todayStr, endDate: todayStr }
  });
  // 获取昨日数据
  const resYesterday = await axios.get('/blade-ad/user/stats/getUserRequestStats', {
    params: { startDate: yesterdayStr, endDate: yesterdayStr }
  });

  // 取出人数（假设返回 [{date: '2024-07-26', count: 5}]）
  todayCount.value = (resToday.data.data && resToday.data.data[0]?.count) || 0;
  yesterdayCount.value = (resYesterday.data.data && resYesterday.data.data[0]?.count) || 0;

  // 计算环比
  if (yesterdayCount.value === 0) {
    percentText.value = todayCount.value === 0 ? '0%' : '+100%';
    percentColor.value = todayCount.value > 0 ? '#67C23A' : '#333';
  } else {
    const percent = ((todayCount.value - yesterdayCount.value) / yesterdayCount.value * 100).toFixed(2);
    percentText.value = (percent > 0 ? '+' : '') + percent + '%';
    percentColor.value = percent > 0 ? '#67C23A' : (percent < 0 ? '#F56C6C' : '#333');
  }
};

const fetchStats = async () => {
  if (!dateRange.value || dateRange.value.length !== 2) return;
  const [start, end] = dateRange.value;
  const res = await axios.get('/blade-ad/user/stats/getUserRequestStats', {
    params: {
      startDate: start,
      endDate: end
    }
  });
  const data = res.data.data || [];
  // 兼容后端返回格式：[{date: '2024-06-01', count: 10}, ...] 或 [{x: '2024-06-01', y: 10}, ...]
  const xData = data.map(item => item.date || item.x);
  const yData = data.map(item => item.count || item.y);
  renderChart(xData, yData);
};

const renderChart = (xData, yData) => {
  if (!chartInstance) {
    chartInstance = echarts.init(chartRef.value);
  }
  const option = {
    title: { text: '用户活跃度趋势' },
    tooltip: { trigger: 'axis' },
    xAxis: { type: 'category', data: xData },
    yAxis: { type: 'value' },
    series: [
      {
        name: '活跃用户数',
        type: 'line',
        data: yData,
        smooth: true
      }
    ]
  };
  chartInstance.setOption(option);
};

onMounted(() => {
  // 默认展示最近7天
  const today = new Date();
  const lastWeek = new Date();
  lastWeek.setDate(today.getDate() - 6);
  const format = d => d.toISOString().slice(0, 10);
  dateRange.value = [format(lastWeek), format(today)];
  fetchStats();
  fetchTodayAndYesterday();
});

// 响应式窗口自适应
window.addEventListener('resize', () => {
  if (chartInstance) chartInstance.resize();
});
</script>

<style>
</style>

