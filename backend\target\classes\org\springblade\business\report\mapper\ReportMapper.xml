<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.report.mapper.ReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="reportResultMap" type="org.springblade.business.report.entity.Report">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="post_id" property="postId"/>
        <result column="user_id" property="userId"/>
        <result column="content" property="content"/>
        <result column="images" property="images"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="reason" property="reason"/>
    </resultMap>

    <!-- 举报记录VO映射结果，包含帖子信息 -->
    <resultMap id="reportVOResultMap" type="org.springblade.business.report.vo.ReportVO" extends="reportResultMap">
        <!-- 帖子信息 -->
        <association property="post" javaType="org.springblade.business.post.entity.SupPost">
            <id column="post_id" property="id"/>
            <result column="post_title" property="title"/>
            <result column="post_content" property="content"/>
            <result column="post_images" property="images"/>
            <result column="post_create_time" property="createTime"/>
            <result column="post_create_user" property="createUser"/>
            <result column="post_category_id" property="categoryId"/>
            <result column="post_audit_status" property="auditStatus"/>
            <result column="post_publish_status" property="publishStatus"/>
            <result column="post_location" property="location"/>
            <result column="post_address" property="address"/>
            <result column="post_contact_name" property="contactName"/>
            <result column="post_contact_phone" property="contactPhone"/>
            <result column="post_contact_type" property="contactType"/>
<!--            <result column="post_contact_number" property="contactNumber"/>-->
        </association>

        <!-- 帖子统计信息 -->
        <association property="postStats" javaType="org.springblade.business.post.dto.PostStatsDTO">
            <result column="like_count" property="likeCount"/>
            <result column="view_count" property="viewCount"/>
            <result column="feedback_count" property="feedbackCount"/>
            <result column="favorite_count" property="favoriteCount"/>
        </association>

        <!-- 举报用户信息 -->
        <association property="reportUser" javaType="org.springblade.business.report.dto.ReportUserDTO">
            <result column="report_user_id" property="userId"/>
            <result column="report_user_nickname" property="nickname"/>
            <result column="report_user_mobile" property="mobile"/>
            <result column="report_user_avatar" property="avatar"/>
        </association>

        <!-- 被举报用户信息 -->
        <association property="reportedUser" javaType="org.springblade.business.report.dto.ReportedUserDTO">
            <result column="reported_user_id" property="userId"/>
            <result column="reported_user_nickname" property="nickname"/>
            <result column="reported_user_mobile" property="mobile"/>
            <result column="reported_user_avatar" property="avatar"/>
            <result column="reported_user_reported_count" property="userReportedCount"/>
            <result column="reported_post_reported_count" property="postReportedCount"/>
        </association>

        <!-- 帖子分类信息 -->
        <association property="postCategory" javaType="org.springblade.business.report.dto.PostCategoryDTO">
            <result column="category_id" property="categoryId"/>
            <result column="category_name" property="categoryName"/>
            <result column="category_description" property="categoryDescription"/>
        </association>
    </resultMap>


    <select id="selectReportPage" resultMap="reportVOResultMap">
        SELECT
            r.*,
            -- 帖子基本信息
            p.title as post_title,
            p.content as post_content,
            p.images as post_images,
            p.create_time as post_create_time,
            p.create_user as post_create_user,
            p.category_id as post_category_id,
            p.audit_status as post_audit_status,
            p.publish_status as post_publish_status,
            p.location as post_location,
            p.address as post_address,
            p.contact_name as post_contact_name,
            p.contact_phone as post_contact_phone,
            p.contact_type as post_contact_type,
/*            p.contact_number as post_contact_number,*/
            -- 帖子统计信息
            COALESCE(stats.like_count, 0) as like_count,
            COALESCE(stats.view_count, 0) as view_count,
            COALESCE(stats.feedback_count, 0) as feedback_count,
            COALESCE(stats.favorite_count, 0) as favorite_count,
            -- 举报用户信息
            r.user_id as report_user_id,
            ru.nickname as report_user_nickname,
            ru.mobile as report_user_mobile,
            ru.avatar as report_user_avatar,
            -- 被举报用户信息
            p.create_user as reported_user_id,
            pu.nickname as reported_user_nickname,
            pu.mobile as reported_user_mobile,
            pu.avatar as reported_user_avatar,
            COALESCE(user_reported_count_stats.user_reported_count, 0) as reported_user_reported_count,
            COALESCE(post_reported_count_stats.post_reported_count, 0) as reported_post_reported_count,
            -- 帖子分类信息
            c.id as category_id,
            c.name as category_name,
            c.description as category_description
        FROM urb_report r
        LEFT JOIN urb_post p ON r.post_id = p.id AND p.is_deleted = 0
        LEFT JOIN urb_user ru ON r.user_id = ru.id AND ru.is_deleted = 0
        LEFT JOIN urb_user pu ON p.create_user = pu.id AND pu.is_deleted = 0
        LEFT JOIN urb_category c ON p.category_id = c.id AND c.is_deleted = 0
        LEFT JOIN (
            SELECT
                p2.id as post_id,
                COALESCE(like_stats.like_count, 0) as like_count,
                COALESCE(view_stats.view_count, 0) as view_count,
                COALESCE(feedback_stats.feedback_count, 0) as feedback_count,
                COALESCE(favorite_stats.favorite_count, 0) as favorite_count
            FROM urb_post p2
            LEFT JOIN (
                SELECT post_id, COUNT(*) as like_count
                FROM urb_like
                WHERE is_deleted = 0
                GROUP BY post_id
            ) like_stats ON p2.id = like_stats.post_id
            LEFT JOIN (
                SELECT relevancy_id, COUNT(*) as view_count
                FROM urb_view_log
                WHERE is_deleted = 0
                GROUP BY relevancy_id
            ) view_stats ON p2.id = view_stats.relevancy_id
            LEFT JOIN (
                SELECT post_id, COUNT(*) as feedback_count
                FROM urb_feedback
                WHERE is_deleted = 0
                GROUP BY post_id
            ) feedback_stats ON p2.id = feedback_stats.post_id
            LEFT JOIN (
                SELECT post_id, COUNT(*) as favorite_count
                FROM urb_favorite
                WHERE is_deleted = 0
                GROUP BY post_id
            ) favorite_stats ON p2.id = favorite_stats.post_id
            WHERE p2.is_deleted = 0
        ) stats ON p.id = stats.post_id
        LEFT JOIN (
            SELECT
                p3.create_user as user_id,
                COUNT(*) as user_reported_count
            FROM urb_report r2
            LEFT JOIN urb_post p3 ON r2.post_id = p3.id AND p3.is_deleted = 0
            WHERE r2.is_deleted = 0 AND r2.audit_status = '1'
            GROUP BY p3.create_user
        ) user_reported_count_stats ON p.create_user = user_reported_count_stats.user_id
        left join (
            select post_id, count(*) as post_reported_count
            from urb_report
            where is_deleted = 0 and audit_status = '1'
            group by post_id
        ) post_reported_count_stats on p.id = post_reported_count_stats.post_id
        WHERE r.is_deleted = 0
        ORDER BY r.create_time DESC
    </select>
    <select id="getReportCount" resultType="java.lang.Integer">
        select count(1) from urb_report where post_id = #{postId} and is_deleted = 0 and audit_status = '1'
    </select>

</mapper>
