/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.config.entity.FormConfig;
import org.springblade.business.config.mapper.FormConfigMapper;
import org.springblade.business.config.service.IFormConfigService;
import org.springblade.business.config.vo.FormConfigVO;
import org.springblade.business.config.wrapper.FormConfigWrapper;
import org.springblade.core.log.exception.ServiceException;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;

import java.util.Optional;

import static org.springblade.common.cache.CacheNames.WECHAT_CATEGORY_FORMS;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-24
 */
@Service
public class FormConfigServiceImpl extends BaseServiceImpl<FormConfigMapper, FormConfig> implements IFormConfigService {

	@Override
	public IPage<FormConfigVO> selectFormConfigPage(IPage<FormConfigVO> page, FormConfigVO formConfig) {
		return page.setRecords(baseMapper.selectFormConfigPage(page, formConfig));
	}

    @Override
	@Cacheable(cacheNames = WECHAT_CATEGORY_FORMS)
    public FormConfigVO getByCategoryId(Long id) {
        LambdaQueryWrapper<FormConfig> wp = new LambdaQueryWrapper<>();
        wp.eq(FormConfig::getCategoryId, id);
        return FormConfigWrapper.build().entityVO(Optional.ofNullable(this.getOne(wp))
                .orElseThrow(() -> new ServiceException("找不到表单记录")));
    }

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_CATEGORY_FORMS,allEntries = true)
	})
	public boolean saveFromConfig(FormConfig formConfig) {
		return this.save(formConfig);
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_CATEGORY_FORMS,allEntries = true)
	})
	public boolean updateFromConfigById(FormConfig formConfig) {
		return this.updateById(formConfig);
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_CATEGORY_FORMS,allEntries = true)
	})
	public boolean saveOrUpdateFromConfig(FormConfig formConfig) {
		return this.saveOrUpdate(formConfig);
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_CATEGORY_FORMS,allEntries = true)
	})
	public boolean deleteLogicFromConfig(String ids) {
		return this.deleteLogic(Func.toLongList(ids));
	}

}
