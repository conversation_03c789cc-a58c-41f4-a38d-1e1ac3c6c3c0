-- 小程序地区配置表
CREATE TABLE `miniapp_region_config` (
  `id` bigint(64) NOT NULL COMMENT '主键',
  `region_code` varchar(20) NOT NULL COMMENT '地区编码',
  `region_name` varchar(100) NOT NULL COMMENT '地区名称',
  `parent_code` varchar(20) DEFAULT NULL COMMENT '父级地区编码',
  `level` int(2) NOT NULL COMMENT '地区层级 1-省 2-市 3-县 4-乡镇 5-村',
  `is_open` int(2) NOT NULL DEFAULT '0' COMMENT '是否开放 0-关闭 1-开放',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
  `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` int(2) DEFAULT '1' COMMENT '状态',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_region_code` (`region_code`),
  KEY `idx_parent_code` (`parent_code`),
  KEY `idx_level` (`level`),
  KEY `idx_is_open` (`is_open`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小程序地区配置表';

-- 初始化连云港市的地区数据（示例）
INSERT INTO `miniapp_region_config` (`id`, `region_code`, `region_name`, `parent_code`, `level`, `is_open`, `sort`, `tenant_id`) VALUES
-- 江苏省
(1, '320000', '江苏省', '00', 1, 1, 1, '000000'),
-- 连云港市
(2, '320700', '连云港市', '320000', 2, 1, 1, '000000'),
-- 连云港市各区县
(3, '320703', '连云区', '320700', 3, 1, 1, '000000'),
(4, '320706', '海州区', '320700', 3, 1, 2, '000000'),
(5, '320707', '赣榆区', '320700', 3, 1, 3, '000000'),
(6, '320722', '东海县', '320700', 3, 1, 4, '000000'),
(7, '320723', '灌云县', '320700', 3, 1, 5, '000000'),
(8, '320724', '灌南县', '320700', 3, 1, 6, '000000');

-- 为连云港市各区县添加一些乡镇示例数据
-- 连云区乡镇
INSERT INTO `miniapp_region_config` (`id`, `region_code`, `region_name`, `parent_code`, `level`, `is_open`, `sort`, `tenant_id`) VALUES
(101, '320703001', '海州湾街道', '320703', 4, 1, 1, '000000'),
(102, '320703002', '连岛街道', '320703', 4, 1, 2, '000000'),
(103, '320703003', '墟沟街道', '320703', 4, 1, 3, '000000'),
(104, '320703004', '板桥街道', '320703', 4, 1, 4, '000000'),
(105, '320703005', '中云街道', '320703', 4, 1, 5, '000000');

-- 海州区乡镇
INSERT INTO `miniapp_region_config` (`id`, `region_code`, `region_name`, `parent_code`, `level`, `is_open`, `sort`, `tenant_id`) VALUES
(201, '320706001', '海州街道', '320706', 4, 1, 1, '000000'),
(202, '320706002', '幸福路街道', '320706', 4, 1, 2, '000000'),
(203, '320706003', '朐阳街道', '320706', 4, 1, 3, '000000'),
(204, '320706004', '路南街道', '320706', 4, 1, 4, '000000'),
(205, '320706005', '新东街道', '320706', 4, 1, 5, '000000'),
(206, '320706006', '洪门街道', '320706', 4, 1, 6, '000000'),
(207, '320706007', '新南街道', '320706', 4, 1, 7, '000000'),
(208, '320706008', '南城街道', '320706', 4, 1, 8, '000000'),
(209, '320706009', '浦南镇', '320706', 4, 1, 9, '000000'),
(210, '320706010', '锦屏镇', '320706', 4, 1, 10, '000000');

-- 灌南县乡镇
INSERT INTO `miniapp_region_config` (`id`, `region_code`, `region_name`, `parent_code`, `level`, `is_open`, `sort`, `tenant_id`) VALUES
(301, '320724001', '新安镇', '320724', 4, 1, 1, '000000'),
(302, '320724002', '堆沟港镇', '320724', 4, 1, 2, '000000'),
(303, '320724003', '长茂镇', '320724', 4, 1, 3, '000000'),
(304, '320724004', '北陈集镇', '320724', 4, 1, 4, '000000'),
(305, '320724005', '孟兴庄镇', '320724', 4, 1, 5, '000000'),
(306, '320724006', '汤沟镇', '320724', 4, 1, 6, '000000'),
(307, '320724007', '三口镇', '320724', 4, 1, 7, '000000'),
(308, '320724008', '田楼镇', '320724', 4, 1, 8, '000000'),
(309, '320724009', '张店镇', '320724', 4, 1, 9, '000000'),
(310, '320724010', '李集镇', '320724', 4, 1, 10, '000000'),
(311, '320724011', '百禄镇', '320724', 4, 1, 11, '000000'),
(312, '320724012', '花园乡', '320724', 4, 1, 12, '000000');
