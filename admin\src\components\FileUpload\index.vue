<template>
  <div class="file-upload-component">
    <!-- 文件上传区域 -->
    <el-upload
      ref="uploadRef"
      :action="uploadAction"
      :headers="uploadHeaders"
      :data="uploadData"
      :file-list="fileList"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :on-remove="handleFileRemove"
      :before-upload="beforeUpload"
      :on-exceed="handleExceed"
      :multiple="multiple"
      :limit="limit"
      :accept="accept"
      :drag="drag"
      :list-type="listType"
      :show-file-list="showFileList"
      :disabled="disabled"
      :auto-upload="autoUpload"
      :http-request="customUpload"
      class="file-upload"
    >
      <template #default>
        <div v-if="drag">
          <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
        </div>
        <div v-else>
          <el-button type="primary" :disabled="disabled">
            <el-icon><Upload /></el-icon>
            {{ buttonText }}
          </el-button>
        </div>
      </template>
      <template #tip>
        <div class="el-upload__tip">
          {{ tipText }}
        </div>
      </template>
    </el-upload>

    <!-- 文件列表展示 -->
    <div v-if="showFileList && fileList.length > 0" class="file-list">
      <div v-for="(file, index) in fileList" :key="index" class="file-item">
        <div class="file-info">
          <el-image
            v-if="isImage(file)"
            :src="file.url || file.response?.data?.accessUrl"
            :preview-src-list="[file.url || file.response?.data?.accessUrl]"
            fit="cover"
            class="file-preview">
            <template #error>
              <div class="image-error">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
          <div v-else class="file-icon">
            <el-icon v-if="isDocument(file)"><Document /></el-icon>
            <el-icon v-else-if="isVideo(file)"><VideoPlay /></el-icon>
            <el-icon v-else-if="isAudio(file)"><Headset /></el-icon>
            <el-icon v-else><Files /></el-icon>
          </div>
          <div class="file-details">
            <div class="file-name">{{ file.name }}</div>
            <div class="file-meta">
              <span>{{ formatFileSize(file.size) }}</span>
              <span v-if="file.status === 'success'" class="success">上传成功</span>
              <span v-else-if="file.status === 'uploading'" class="uploading">上传中...</span>
              <span v-else-if="file.status === 'fail'" class="error">上传失败</span>
            </div>
          </div>
        </div>
        <div class="file-actions">
          <el-button 
            v-if="file.status === 'success'"
            type="primary" 
             
            @click="handlePreview(file)">
            预览
          </el-button>
          <el-button 
            type="danger" 
             
            @click="handleRemove(file, index)">
            删除
          </el-button>
        </div>
      </div>
    </div>

    <!-- 上传进度 -->
    <el-progress 
      v-if="uploadProgress > 0 && uploadProgress < 100"
      :percentage="uploadProgress"
      :status="uploadStatus"
      class="upload-progress" />
  </div>
</template>

<script>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { FileUploadAPI } from '@/api/system/fileUpload'
import { UploadFilled, Upload, Picture, Document, VideoPlay, Headset, Files } from '@element-plus/icons-vue'

export default {
  name: 'FileUpload',
  components: {
    UploadFilled,
    Upload,
    Picture,
    Document,
    VideoPlay,
    Headset,
    Files
  },
  props: {
    // 上传配置
    multiple: {
      type: Boolean,
      default: false
    },
    limit: {
      type: Number,
      default: 10
    },
    accept: {
      type: String,
      default: 'image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain,text/csv'
    },
    drag: {
      type: Boolean,
      default: false
    },
    listType: {
      type: String,
      default: 'text'
    },
    showFileList: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    autoUpload: {
      type: Boolean,
      default: true
    },
    
    // 业务配置
    uploadSource: {
      type: String,
      default: 'admin'
    },
    businessType: {
      type: String,
      default: ''
    },
    businessId: {
      type: [String, Number],
      default: ''
    },
    
    // 显示配置
    buttonText: {
      type: String,
      default: '选择文件'
    },
    tipText: {
      type: String,
      default: '支持jpg/png/gif/pdf/doc/docx/txt/csv格式，单个文件不超过10MB'
    },
    
    // 值绑定
    modelValue: {
      type: [Array, String],
      default: () => []
    }
  },
  
  emits: ['update:modelValue', 'success', 'error', 'remove', 'exceed'],
  
  setup(props, { emit }) {
    const uploadRef = ref()
    const fileList = ref([])
    const uploadProgress = ref(0)
    const uploadStatus = ref('')
    
    // 计算属性
    const uploadAction = computed(() => {
      return props.multiple 
        ? '/blade-system/file-upload/upload-batch'
        : '/blade-system/file-upload/upload'
    })
    
    const uploadHeaders = computed(() => {
      const token = localStorage.getItem('blade-auth')
      return {
        'Authorization': `Bearer ${token}`
      }
    })
    
    const uploadData = computed(() => {
      return {
        uploadSource: props.uploadSource,
        businessType: props.businessType,
        businessId: props.businessId
      }
    })
    
    // 监听modelValue变化
    watch(() => props.modelValue, (newVal) => {
      if (newVal && newVal.length > 0) {
        // 将modelValue转换为fileList格式
        const files = Array.isArray(newVal) ? newVal : [newVal]
        fileList.value = files.map(file => {
          if (typeof file === 'string') {
            return {
              name: file.split('/').pop(),
              url: file,
              status: 'success'
            }
          }
          return file
        })
      } else {
        fileList.value = []
      }
    }, { immediate: true })
    
    // 监听fileList变化，更新modelValue
    watch(fileList, (newVal) => {
      const urls = newVal
        .filter(file => file.status === 'success')
        .map(file => file.url || file.response?.data?.accessUrl)
      
      if (props.multiple) {
        emit('update:modelValue', urls)
      } else {
        emit('update:modelValue', urls[0] || '')
      }
    }, { deep: true })
    
    // 上传前验证
    const beforeUpload = (file) => {
      // 检查文件大小（10MB）
      const isLt10M = file.size / 1024 / 1024 < 10
      if (!isLt10M) {
        ElMessage.error('文件大小不能超过10MB!')
        return false
      }
      
      // 检查文件类型
      const allowedTypes = props.accept.split(',')
      const isValidType = allowedTypes.some(type => {
        if (type.includes('*')) {
          return file.type.startsWith(type.replace('*', ''))
        }
        return file.type === type
      })
      
      if (!isValidType) {
        ElMessage.error('不支持的文件类型!')
        return false
      }
      
      return true
    }
    
    // 自定义上传
    const customUpload = async (options) => {
      try {
        uploadProgress.value = 0
        uploadStatus.value = 'active'
        
        const formData = new FormData()
        formData.append('file', options.file)
        formData.append('uploadSource', props.uploadSource)
        if (props.businessType) {
          formData.append('businessType', props.businessType)
        }
        if (props.businessId) {
          formData.append('businessId', props.businessId)
        }
        
        // 模拟上传进度
        const progressInterval = setInterval(() => {
          if (uploadProgress.value < 90) {
            uploadProgress.value += 10
          }
        }, 100)
        
        const response = await FileUploadAPI.uploadFile(
          options.file,
          props.uploadSource,
          props.businessType,
          props.businessId
        )
        
        clearInterval(progressInterval)
        uploadProgress.value = 100
        uploadStatus.value = 'success'
        
        // 更新文件状态
        const fileIndex = fileList.value.findIndex(f => f.uid === options.file.uid)
        if (fileIndex > -1) {
          fileList.value[fileIndex].status = 'success'
          fileList.value[fileIndex].response = response
        }
        
        ElMessage.success('上传成功')
        emit('success', response.data)
        
        setTimeout(() => {
          uploadProgress.value = 0
        }, 1000)
        
      } catch (error) {
        uploadProgress.value = 0
        uploadStatus.value = 'exception'
        
        const fileIndex = fileList.value.findIndex(f => f.uid === options.file.uid)
        if (fileIndex > -1) {
          fileList.value[fileIndex].status = 'fail'
        }
        
        ElMessage.error('上传失败: ' + error.message)
        emit('error', error)
      }
    }
    
    // 上传成功
    const handleUploadSuccess = (response, file, fileList) => {
      if (response.code === 200) {
        file.status = 'success'
        ElMessage.success('上传成功')
        emit('success', response.data)
      } else {
        file.status = 'fail'
        ElMessage.error(response.msg || '上传失败')
        emit('error', response)
      }
    }
    
    // 上传失败
    const handleUploadError = (error, file, fileList) => {
      file.status = 'fail'
      ElMessage.error('上传失败')
      emit('error', error)
    }
    
    // 文件移除
    const handleFileRemove = (file, fileList) => {
      emit('remove', file)
    }
    
    // 超出限制
    const handleExceed = (files, fileList) => {
      ElMessage.warning(`最多只能上传 ${props.limit} 个文件`)
      emit('exceed', files, fileList)
    }
    
    // 删除文件
    const handleRemove = (file, index) => {
      fileList.value.splice(index, 1)
      emit('remove', file)
    }
    
    // 预览文件
    const handlePreview = (file) => {
      const url = file.url || file.response?.data?.accessUrl
      if (isImage(file)) {
        // 图片预览
        window.open(url)
      } else {
        // 其他文件下载
        const link = document.createElement('a')
        link.href = url
        link.download = file.name
        link.click()
      }
    }
    
    // 文件类型判断
    const isImage = (file) => {
      const url = file.url || file.response?.data?.accessUrl
      const name = file.name || ''
      return file.type?.startsWith('image/') || 
             /\.(jpg|jpeg|png|gif|webp)$/i.test(name) ||
             /\.(jpg|jpeg|png|gif|webp)$/i.test(url)
    }
    
    const isDocument = (file) => {
      const name = file.name || ''
      return file.type?.includes('pdf') || 
             file.type?.includes('word') || 
             file.type?.includes('excel') ||
             /\.(pdf|doc|docx|xls|xlsx|ppt|pptx)$/i.test(name)
    }
    
    const isVideo = (file) => {
      const name = file.name || ''
      return file.type?.startsWith('video/') || 
             /\.(mp4|avi|mov|wmv|flv|mkv)$/i.test(name)
    }
    
    const isAudio = (file) => {
      const name = file.name || ''
      return file.type?.startsWith('audio/') || 
             /\.(mp3|wav|flac|aac)$/i.test(name)
    }
    
    // 格式化文件大小
    const formatFileSize = (bytes) => {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    }
    
    // 公开方法
    const submit = () => {
      uploadRef.value?.submit()
    }
    
    const clearFiles = () => {
      fileList.value = []
    }
    
    return {
      uploadRef,
      fileList,
      uploadProgress,
      uploadStatus,
      uploadAction,
      uploadHeaders,
      uploadData,
      beforeUpload,
      customUpload,
      handleUploadSuccess,
      handleUploadError,
      handleFileRemove,
      handleExceed,
      handleRemove,
      handlePreview,
      isImage,
      isDocument,
      isVideo,
      isAudio,
      formatFileSize,
      submit,
      clearFiles
    }
  }
}
</script>

<style lang="scss" scoped>
.file-upload-component {
  .file-upload {
    width: 100%;
  }
  
  .file-list {
    margin-top: 16px;
    
    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      margin-bottom: 8px;
      
      .file-info {
        display: flex;
        align-items: center;
        flex: 1;
        
        .file-preview {
          width: 40px;
          height: 40px;
          border-radius: 4px;
          margin-right: 12px;
        }
        
        .file-icon {
          width: 40px;
          height: 40px;
          background: #f5f5f5;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 12px;
          color: #999;
        }
        
        .file-details {
          flex: 1;
          
          .file-name {
            font-weight: 500;
            margin-bottom: 4px;
            word-break: break-all;
          }
          
          .file-meta {
            font-size: 12px;
            color: #999;
            
            span {
              margin-right: 8px;
              
              &.success {
                color: #67c23a;
              }
              
              &.uploading {
                color: #409eff;
              }
              
              &.error {
                color: #f56c6c;
              }
            }
          }
        }
      }
      
      .file-actions {
        display: flex;
        gap: 8px;
      }
    }
  }
  
  .upload-progress {
    margin-top: 16px;
  }
  
  .image-error {
    width: 40px;
    height: 40px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }
}
</style> 