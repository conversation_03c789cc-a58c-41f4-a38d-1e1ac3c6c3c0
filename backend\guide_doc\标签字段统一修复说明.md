# 标签字段统一修复说明

## 修复概述

本次修复将标签相关的字段统一为 `tagName`，解决前后端字段不一致的问题。

## 修复内容

### 1. 后端修复

#### 1.1 实体类修复
- **文件**: `backend/src/main/java/org/springblade/business/post/entity/Tag.java`
- **修改**: 将 `name` 字段改为 `tagName`，并添加 `@TableField("tag_name")` 注解
- **原因**: 数据库表中字段名为 `tag_name`，需要正确映射

#### 1.2 控制器修复
- **文件**: `backend/src/main/java/org/springblade/business/post/controller/TagController.java`
- **修改**: 
  - `getTagList` 方法参数从 `name` 改为 `tagName`
  - `createTag` 方法参数从 `name` 改为 `tagName`
  - 更新查询逻辑使用 `tagName` 字段

#### 1.3 服务层修复
- **文件**: `backend/src/main/java/org/springblade/business/post/service/ITagService.java`
- **修改**: `createTag` 方法参数从 `name` 改为 `tagName`

- **文件**: `backend/src/main/java/org/springblade/business/post/service/impl/TagServiceImpl.java`
- **修改**: 
  - `createTag` 方法参数从 `name` 改为 `tagName`
  - 查询条件使用 `Tag::getTagName`
  - 设置标签名称使用 `setTagName`

#### 1.4 Mapper修复
- **文件**: `backend/src/main/java/org/springblade/business/post/mapper/TagMapper.xml`
- **修改**: 
  - 更新 `resultMap` 中的字段映射
  - 添加 `tagName` 字段的查询条件支持
  - 优化查询SQL，支持按 `tagName` 搜索

### 2. 前端修复

#### 2.1 管理后台API
- **文件**: `admin/src/api/ad/tag.js`
- **修改**: 
  - 添加 `createTag` 方法
  - 修复 `getDetail` 方法的URL格式
  - 确保所有API使用正确的字段名

#### 2.2 管理后台组件
- **文件**: `admin/src/views/ad/post/tag.vue`
- **状态**: 无需修改，已经正确使用 `tagName` 字段

#### 2.3 小程序修复
- **文件**: `weapp/pages/publish/publish.js`
- **修改**: 将 `tag.name` 改为 `tag.tagName`

- **文件**: `weapp/components/my-post-item/my-post-item.wxml`
- **修改**: 将 `tag.name` 改为 `tag.tagName`

### 3. 数据库修复

#### 3.1 字段映射
- 数据库字段: `tag_name`
- Java实体字段: `tagName`
- 前端字段: `tagName`

#### 3.2 迁移脚本
- **文件**: `backend/doc/sql/tag_name_field_update.sql`
- **内容**: 包含数据验证、索引创建等SQL脚本

## 修复验证

### 1. 后端验证
```bash
# 启动后端服务
cd backend
mvn spring-boot:run

# 测试标签相关接口
curl -X GET "http://localhost:80/blade-ad/tag/admin/list?tagName=测试"
curl -X POST "http://localhost:80/blade-ad/tag/admin/create?tagName=新标签&categoryId=1"
```

### 2. 前端验证
```bash
# 启动管理后台
cd admin
npm run dev

# 访问标签管理页面，验证字段显示和操作
```

### 3. 小程序验证
```bash
# 在微信开发者工具中打开小程序
# 测试发布页面的标签选择功能
# 验证标签显示是否正确
```

## 注意事项

### 1. 数据兼容性
- 如果数据库中还有使用 `name` 字段的数据，需要执行迁移脚本
- 建议在测试环境先验证修复效果

### 2. 缓存清理
- 如果使用了Redis缓存，需要清理相关的标签缓存
- 重启应用服务确保配置生效

### 3. 接口兼容性
- 确保所有调用标签接口的地方都已更新
- 检查第三方集成是否受影响

## 回滚方案

如果修复出现问题，可以按以下步骤回滚：

### 1. 代码回滚
```bash
# 回滚到修复前的代码版本
git revert <commit-hash>
```

### 2. 数据库回滚
```sql
-- 如果需要回滚数据库字段
ALTER TABLE urb_tag CHANGE tag_name name VARCHAR(50);
```

### 3. 重启服务
```bash
# 重启后端服务
# 重启前端服务
# 清理缓存
```

## 总结

本次修复解决了标签字段不一致的问题，确保了前后端数据的一致性。修复涉及后端实体、服务、控制器、Mapper，以及前端API和组件。建议在测试环境充分验证后再部署到生产环境。 