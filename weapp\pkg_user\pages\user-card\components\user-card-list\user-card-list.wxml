<scroll-view class="card-list-scroll" scroll-x>
  <view class="card-list-row">
    <block wx:for="{{cardList}}" wx:key="id">
      <view class="biz-card" 
            catchtap="onCardTap" 
            data-index="{{index}}">
        <view class="card-flip {{flippedIndex === index ? 'flipped' : ''}}">
          <!-- 正面 -->
          <view class="card-front card-face">
            <view class="card-top">
              <image wx:if="{{item.logo}}" class="logo" src="{{item.logo}}" mode="aspectFit"/>
              <view class="company-info">
                <text class="company">{{item.company}}</text>
              </view>
            </view>
            <view class="divider"></view>
            <view class="card-bottom">
              <view class="left-info">
                <text class="name">{{item.name}}</text>
                <text class="title">{{item.title}}</text>
                <image wx:if="{{item.qrCode}}" class="qrcode" src="{{item.qrCode}}" mode="aspectFit"/>
              </view>
              <view class="right-info">
                <view wx:if="{{item.address}}" class="info-row">
                  <text class="iconfont">&#xe601;</text>
                  <text class="info-text">{{item.address}}</text>
                </view>
                <view wx:if="{{item.mobile}}" class="info-row">
                  <text class="iconfont">&#xe600;</text>
                  <text class="info-text">{{item.mobile}}</text>
                </view>
                <view wx:if="{{item.phone}}" class="info-row">
                  <text class="iconfont">&#xe602;</text>
                  <text class="info-text">{{item.phone}}</text>
                </view>
                <view wx:if="{{item.website}}" class="info-row">
                  <text class="iconfont">&#xe603;</text>
                  <text class="info-text">{{item.website}}</text>
                </view>
                <view wx:if="{{item.email}}" class="info-row">
                  <text class="iconfont">&#xe604;</text>
                  <text class="info-text">{{item.email}}</text>
                </view>
              </view>
            </view>
          </view>
          <!-- 背面 -->
          <view class="card-back card-face">
            <view class="back-content">
              <text class="back-title">备注/简介</text>
              <text class="back-desc">{{item.remark || item.description || '暂无更多信息'}}</text>
              <image wx:if="{{item.qrCode}}" class="qrcode" src="{{item.qrCode}}" mode="aspectFit"/>
            </view>
          </view>
        </view>
      </view>
    </block>
  </view>
</scroll-view>