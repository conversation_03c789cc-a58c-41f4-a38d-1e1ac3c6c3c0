<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.post.mapper.UrbMessageTemplateMapper">

    <!-- 根据typeCode查找模板 -->
    <select id="selectByTypeCode" resultType="org.springblade.business.post.entity.UrbMessageTemplate">
        SELECT * FROM urb_message_template WHERE type_code = #{typeCode} LIMIT 1
    </select>

    <!-- 其余insert、updateById等由MyBatis-Plus自动实现 -->

</mapper>
