<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
              :before-close="beforeClose"
               :search="search"
               :page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.product_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template #specification-form>
        <div v-if="formType === 'view'">
          <div v-for="(vals, key) in parseSpec(form.specification)" :key="key">
            <b>{{ key }}：</b>{{ vals.join('，') }}
          </div>
        </div>
        <div v-else>
          <div v-for="(spec, idx) in specList" :key="idx" class="spec-row" style="margin-bottom: 10px;">
            <el-input v-model="spec.name" placeholder="规格名" style="width: 120px; margin-right: 8px;" />
            <el-tag
              v-for="(val, vIdx) in spec.values"
              :key="vIdx"
              closable
              @close="removeSpecValue(idx, vIdx)"
              style="margin-right: 4px;"
            >{{ val }}</el-tag>
            <el-input
              v-model="spec.newValue"
              placeholder="规格值"
              style="width: 100px; margin-right: 4px;"
              @keyup.enter.native="addSpecValue(idx)"
            />
            <el-button type="primary" size="mini" @click="addSpecValue(idx)">添加</el-button>
            <el-button type="danger" size="mini" @click="removeSpec(idx)">删除规格</el-button>
          </div>
          <el-button type="success" size="mini" @click="addSpec">添加规格</el-button>
        </div>
      </template>
      
      <template #productDesc-form>
        <div v-if="formType === 'view'">
          <div class="rich-text-view" v-html="form.productDesc"></div>
        </div>
        <div>
          <div :style="{display: formType === 'view' ? 'none' : 'block'}" style="border: 1px solid #ccc">
            <Toolbar
              style="border-bottom: 1px solid #ccc"
              :editor="editorRef"
              :defaultConfig="toolbarConfig"
              :mode="mode"
            />
            <Editor
              style="height: 500px; overflow-y: hidden;"
              v-model="valueHtml"
              :defaultConfig="editorConfig"
              :mode="mode"
              @onCreated="handleCreated"
            />
          </div>
        </div>
      </template>
      
     
      <template #categoryId="{ row }">
        <span>{{ getCategoryName(row.categoryId) }}</span>
      </template>
      <template #specification="{ row }">
        <div v-if="row.specification">
          <div v-for="(vals, key) in parseSpec(row.specification)" :key="key">
            <b>{{ key }}：</b>{{ vals.join('，') }}
          </div>
        </div>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/biz/product";
  import {getAllCategories} from "@/api/biz/category";
  import {uploadFormData} from "@/api/system/file";
  import {mapGetters} from "vuex";
  import { getCurrentInstance } from 'vue';
  import { $ImagePreview } from '@smallwei/avue';
  import '@wangeditor/editor/dist/css/style.css';
  import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
  import { onBeforeUnmount, ref, shallowRef, onMounted } from 'vue';
  import { getToken } from '@/utils/auth';

  export default {
    components: { Editor, Toolbar },
    setup() {
      const { appContext } = getCurrentInstance();
      
      // 编辑器实例，必须用 shallowRef
      const editorRef = shallowRef()
      
      // 内容 HTML
      const valueHtml = ref('<p>请输入商品描述...</p>')
      
      const toolbarConfig = {}
      const editorConfig = { 
        placeholder: '请输入商品描述...',
        autoFocus: false,
        MENU_CONF: {
          uploadImage: {
            server: '/api/blade-system/file/upload',
            fieldName: 'file',
            maxFileSize: 10 * 1024 * 1024, // 10M
            maxNumberOfFiles: 10,
            allowedFileTypes: ['image/*'],
            meta: {
              'blade-auth':'bearer '+ getToken()
            },
            metaWithUrl: true,
            withCredentials: false,
            timeout: 5 * 1000,
            onBeforeUpload: (file) => {
              return file
            },
            onProgress: (progress) => {
              console.log('progress', progress)
            },
            onSuccess: (file, res) => {
              console.log('success', file, res)
            },
            onFailed: (file, res) => {
              console.log('failed', file, res)  
              // 在setup中无法直接访问this.$message，错误处理移到methods中
            },
            onError: (file, err, res) => {
              console.log('error', file, err, res)
              // 在setup中无法直接访问this.$message，错误处理移到methods中
            },
            customInsert: (res, insertFn) => {  
              if (res.success || res.code === 200) {
                const url = res.data.filePath || res.data.url
                insertFn(url, res.data.fileName || 'image', url)
              } else {
                console.error('图片上传失败:', res)
              }
            }
          } 
        }
      }
      
      // 组件销毁时，也及时销毁编辑器
      onBeforeUnmount(() => {
        const editor = editorRef.value
        if (editor == null) return
        editor.destroy()
      })
      
      const handleCreated = (editor) => {
        editorRef.value = editor // 记录 editor 实例，重要！
        
        // 监听编辑器内容变化，同步到表单数据
        editor.on('change', () => {
          valueHtml.value = editor.getHtml()
        })
        
        // 延迟设置内容，确保编辑器完全初始化
        // setTimeout(() => {
        //   try {
        //     if (valueHtml.value && valueHtml.value !== '<p>请输入商品描述...</p>') {
        //       // 先清空编辑器，再设置内容，避免状态冲突
        //       editor.clear()
        //       editor.setHtml(valueHtml.value)
        //     }
        //   } catch (error) {
        //     console.warn('设置编辑器内容失败:', error)
        //     // 如果设置失败，使用默认内容
        //     editor.setHtml('<p>请输入商品描述...</p>')
        //   }
        // }, 200)
      }
      
      return { 
        appContext,
        editorRef,
        valueHtml,
        mode: 'default', //默认模式
        toolbarConfig,
        editorConfig,
        handleCreated
      };
    },
    data() {
      return {
        form: {
          newProduct: 0, // 默认否
        },
        formType: 'add',
        editor: null,
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        specList: [],
        categoryOptions: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
     
              hide: true,
              label: "商品图片",
              prop: "images",
              type: "upload",
              span: 24,
              listType: "picture-card",
               action: "/blade-system/file/upload",
              propsHttp: {
                url: "filePath",
                name: "fileName",
                res: "data"
              },
              multiple: true,
              limit: 5,
              tip: "最多上传5张图片，支持jpg/png/gif格式"
            },
          
            {
              label: "商品名称",
              prop: "productName",
              search: true,
              rules: [{
                required: true,
                message: "请输入商品名称",
                trigger: "blur"
              }]
            },
            {
              label: "是否新品",
              prop: "newProduct",
              type: "radio",
              dicData: [
                { label: "是", value: 1 },
                { label: "否", value: 0 }
              ],
              search: true,
              rules: [{
                required: true,
                message: "请选择是否新品",
                trigger: "change"
              }]
            },
             {
              label: "分类",
              prop: "categoryId",
              search: true,
              type: "select",
              slot: true,
              dicData: [],
              props: {
                label: 'categoryName',
                value: 'id'
              },
              rules: [{
                required: true,
                message: "请选择分类",
                trigger: "change"
              }]
            },
            {
              label: "商品描述",
              prop: "productDesc",
              span: 24,
              hide: true,
              rules: [{
                required: true,
                message: "请输入商品描述",
                trigger: "blur"
              }]
            },
            {
              label: "价格",
              prop: "price",
              rules: [{
                required: true,
                message: "请输入价格",
                trigger: "blur"
              }]
            },
             
            {
              label: "库存",
              prop: "stock",
              rules: [{
                required: true,
                message: "请输入库存",
                trigger: "blur"
              }]
            },
           
            // {
            //   label: "品牌",
            //   prop: "brand",
            //   search: true,
            //   rules: [{
            //     required: true,
            //     message: "请输入品牌",
            //     trigger: "blur"
            //   }]
            // },
            // {
            //   label: "尺寸",
            //   prop: "dimensions",
            //   rules: [{
            //     required: true,
            //     message: "请输入尺寸",
            //     trigger: "blur"
            //   }]
            // },
            
            // {
            //   label: "颜色",
            //   prop: "color",
            //   search: true,
            //   rules: [{
            //     required: true,
            //     message: "请输入颜色",
            //     trigger: "blur"
            //   }]
            // },
            {
              label: "排序",
              prop: "sortOrder",
              rules: [{
                required: true,
                message: "请输入排序",
                trigger: "blur"
              }]
            }, 
            {
              label: "规格参数",
              prop: "specification",
              slot: true,
              span: 24
            },
          ]
        },
        data: [],
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.product_add, false),
          viewBtn: this.validData(this.permission.product_view, false),
          delBtn: this.validData(this.permission.product_delete, false),
          editBtn: this.validData(this.permission.product_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      // 富文本编辑器相关方法
      onEditorCreated(editor) {
        this.editor = Object.seal(editor)
        
        // 如果表单中已有内容，设置到编辑器中
        if (this.form.productDesc) {
          this.$nextTick(() => {
            editor.setHtml(this.form.productDesc)
          })
        }
        
        // 设置编辑器内容变化监听
        editor.on('change', () => {
          // 更新表单数据
          this.form.productDesc = editor.getHtml()
          // 触发Avue表单验证
          this.$nextTick(() => {
            this.$refs.crud.validateField('productDesc')
          })
        })
      },
      
      // 验证富文本内容
      validateRichText() {
        const content = this.valueHtml || this.form.productDesc || '';
        if (!content || content.trim() === '') {
          return false
        }
        // 移除HTML标签后检查是否有实际内容
        const textContent = content.replace(/<[^>]*>/g, '').trim()
        return textContent.length > 0
      },
      
      // 刷新编辑器内容
      refreshEditorContent() {
        if (this.editorRef && this.editorRef.value && this.valueHtml) {
          this.$nextTick(() => {
            try {
              const editor = this.editorRef.value;
              // 先清空编辑器，再设置内容，避免状态冲突
              editor.clear();
              editor.setHtml(this.valueHtml);
            } catch (error) {
              console.warn('刷新编辑器内容失败:', error);
              // 如果刷新失败，重新创建编辑器
              this.recreateEditor();
            }
          });
        }
      },
      
      // 重新创建编辑器
      recreateEditor() {
        if (this.editorRef && this.editorRef.value) {
          try {
            this.editorRef.value.destroy();
          } catch (error) {
            console.warn('销毁编辑器失败:', error);
          }
        }
        this.editorRef = null;
        
        // 强制重新渲染编辑器组件
        this.$nextTick(() => {
          this.editorRef = this.$refs.editor;
        });
      },
      
      // 清理HTML内容，避免wangEditor解析错误
      cleanHtmlContent(content) {
        if (!content || typeof content !== 'string') {
          return '<p>请输入商品描述...</p>';
        }
        
        let cleanContent = content.trim();
        
        // 移除可能导致问题的特殊字符和格式
        cleanContent = cleanContent
          .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // 移除控制字符
          .replace(/\s+/g, ' ') // 合并多个空格
          .trim();
        
        // 确保内容有基本的HTML结构
        if (!cleanContent.startsWith('<')) {
          cleanContent = `<p>${cleanContent}</p>`;
        }
        
        // 如果内容为空，返回默认内容
        if (!cleanContent || cleanContent === '<p></p>') {
          return '<p>请输入商品描述...</p>';
        }
        
        return cleanContent;
      },
      
      // 规格参数动态表单
      addSpec() {
        this.specList.push({ name: '', values: [], newValue: '' });
      },
      removeSpec(idx) {
        this.specList.splice(idx, 1);
      },
      addSpecValue(idx) {
        const val = this.specList[idx].newValue && this.specList[idx].newValue.trim();
        if (val) {
          this.specList[idx].values.push(val);
          this.specList[idx].newValue = '';
        }
      },
      removeSpecValue(idx, vIdx) {
        this.specList[idx].values.splice(vIdx, 1);
      },
      // 规格参数保存为对象格式
      rowSave(row, done, loading) {
        // 验证富文本内容
        if (!this.validateRichText()) {
          this.$message.error("请输入商品描述内容");
          loading();
          return;
        }
        
        // 规格参数转为 { color: "黄色,绿色", ... }
        const specObj = {};
        this.specList.forEach(item => {
          if (item.name && item.values.length) {
            specObj[item.name] = item.values.join(',');
          }
        });
        row.specification = JSON.stringify(specObj);
        
        // 确保富文本内容正确保存
        if (this.editorRef && this.editorRef.value) {
          row.productDesc = this.editorRef.value.getHtml();
        } else {
          row.productDesc = this.valueHtml;
        }
        
        add({...row,images:this.form.images.join(',')}).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          console.error('保存失败:', error);
          loading();
          this.$message.error("操作失败，请重试");
        });
      },
      rowUpdate(row, index, done, loading) {
        // 验证富文本内容
        if (!this.validateRichText()) {
          this.$message.error("请输入商品描述内容");
          loading();
          return;
        }
        
        const specObj = {};
        this.specList.forEach(item => {
          if (item.name && item.values.length) {
            specObj[item.name] = item.values.join(',');
          }
        });
        row.specification = JSON.stringify(specObj);
        
        // 确保富文本内容正确保存
        if (this.editorRef && this.editorRef.value) {
          row.productDesc = this.editorRef.value.getHtml();
        } else {
          row.productDesc = this.valueHtml;
        }
        
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          console.error('更新失败:', error);
          loading();
          this.$message.error("操作失败，请重试");
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          })
          .catch(error => {
            console.error('删除失败:', error);
            this.$message.error("删除失败，请重试");
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          })
          .catch(error => {
            console.error('批量删除失败:', error);
            this.$message.error("删除失败，请重试");
          });
      },
      beforeOpen(done, type) {
        this.formType = type;
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
            // 规格参数回显为自定义结构
            if (typeof this.form.specification === 'string' && this.form.specification) {
              try {
                const specObj = this.parseSpec(this.form.specification);
                this.specList = Object.keys(specObj).map(key => ({
                  name: key,
                  values: specObj[key],
                  newValue: ''
                }));
              } catch (e) {
                this.specList = [];
              }
            } else {
              this.specList = [];
            }
            
            // 设置富文本编辑器内容
            this.valueHtml = this.cleanHtmlContent(this.form.productDesc);
            
            this.$nextTick(() => {
              // 触发Avue表单验证
              this.$refs.crud.validateField('productDesc')
            });
          }).catch(error => {
            console.error('获取详情失败:', error);
            this.$message.error("获取详情失败");
          });
        } else {
          this.specList = [];
          this.form.specification = [];
          this.form.productDesc = '';
          this.valueHtml = '<p>请输入商品描述...</p>';
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        }).catch(error => {
          console.error('加载数据失败:', error);
          this.loading = false;
          this.$message.error("加载数据失败，请重试");
        });
      },
      // 加载分类数据
      loadCategories() {
        getAllCategories().then(res => {
          const data = res.data.data;
          this.categoryOptions = data.records || [];
          // 更新option中的dicData
          this.option.column.find(col => col.prop === 'categoryId').dicData = this.categoryOptions;
        }).catch(error => {
          console.error('加载分类数据失败:', error);
          this.$message.error("加载分类数据失败");
        });
      },
      // 根据分类ID获取分类名称
      getCategoryName(categoryId) {
        if (!categoryId) return '';
        const category = this.categoryOptions.find(item => item.id === categoryId);
        return category ? category.categoryName : categoryId;
      },
      beforeClose(done) {
        if (this.editorRef && this.editorRef.value) {
          this.editorRef.value.destroy();
        }
        done();
      },
      // 规格参数友好解析
      parseSpec(spec) {
        if (!spec) return {};
        let obj = {};
        try {
          if (typeof spec === 'string') obj = JSON.parse(spec);
          else obj = spec;
        } catch (e) {
          return {};
        }
        if(typeof obj === 'string') {
          obj = this.parseSpec(obj);
        }
        Object.keys(obj).forEach(k => {
          if (typeof obj[k] === 'string') obj[k] = obj[k].split(',');
        });
        return obj;
      }
    },
    mounted() {
      this.loadCategories();
    },
    watch: {
      valueHtml: {
        handler(newVal) {
          this.form.productDesc = newVal;
        },
        deep: true
      },
      formType: {
        handler(newType) {
          // 当从查看模式切换到编辑模式时，重新设置编辑器内容
          if (newType === 'edit') {
            // 延迟执行，确保DOM完全更新
            setTimeout(() => {
              this.refreshEditorContent();
            }, 300);
          }
        }
      }
    },
    beforeDestroy() {
      if (this.editorRef && this.editorRef.value) {
        this.editorRef.value.destroy();
      }
    }
  };
</script>

<style>
.image-preview-multi {
  display: flex;
  flex-wrap: nowrap;
  gap: 10px;
  align-items: flex-start;
  margin-top: 8px;
}

.image-preview-multi img {
  border: 1px solid #dcdfe6;
  transition: border-color 0.2s;
}

.image-preview-multi img:hover {
  border-color: #409eff;
}
.product-image-upload {
  width: 80px;
  height: 80px;
}
.el-upload-list--picture-card{
  flex-wrap: nowrap;
}

.rich-editor-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  height: 300px;
  background: #fff;
}

.rich-editor-container .w-e-toolbar {
  border-bottom: 1px solid #dcdfe6;
  background: #fafafa;
}

.rich-editor-container .w-e-text-container {
  border: none;
}

.rich-editor-container .w-e-text {
  padding: 10px;
  min-height: 280px;
  line-height: 1.6;
}

.rich-text-view {
  padding: 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background: #fafafa;
  min-height: 100px;
  line-height: 1.6;
}

.rich-text-view img {
  max-width: 100%;
  height: auto;
  margin: 5px 0;
}

.rich-text-view table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
}

.rich-text-view table td,
.rich-text-view table th {
  border: 1px solid #dcdfe6;
  padding: 8px;
}

.rich-text-view p {
  margin: 5px 0;
}
</style>
