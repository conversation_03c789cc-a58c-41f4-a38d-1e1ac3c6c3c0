<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.post.mapper.RentHouseMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="rentHouseResultMap" type="org.springblade.business.post.entity.RentHouse">
        <result column="id" property="id"/>
        <result column="post_id" property="postId"/>
        <result column="house_name" property="houseName"/>
        <result column="rent_type" property="rentType"/>
        <result column="house_type" property="houseType"/>
        <result column="Bedroom_type" property="bedroomType"/>
        <result column="house_size" property="houseSize"/>
    </resultMap>


    <select id="selectRentHousePage" resultMap="rentHouseResultMap">
        select * from urb_rent_house where is_deleted = 0
    </select>

</mapper>
