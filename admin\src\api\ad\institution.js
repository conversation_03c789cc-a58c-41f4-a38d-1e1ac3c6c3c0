import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/institution/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getPage = (current, size, params) => {
  return request({
    url: '/blade-ad/institution/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-ad/institution/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/institution/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-ad/institution/save',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-ad/institution/update',
    method: 'post',
    data: row
  })
}

export const audit = (data) => {
  return request({
    url: '/blade-ad/institution/audit',
    method: 'post',
    data: data
  })
}

export const openOrClose = (ids) => {
  return request({
    url: '/blade-ad/institution/openOrClose',
    method: 'post',
    params: {
      ids,
    }
  })
}

