<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.config.mapper.FormConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="formConfigResultMap" type="org.springblade.business.config.entity.FormConfig">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="status" property="status"/>
        <result column="name" property="name"/>
        <result column="config_json" property="configJson"/>
    </resultMap>


    <select id="selectFormConfigPage" resultMap="formConfigResultMap">
        select * from urb_form_config where is_deleted = 0
    </select>

</mapper>
