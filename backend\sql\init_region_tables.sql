-- 初始化地区相关表的SQL脚本
-- 请在数据库中执行此脚本以创建必要的表结构

-- 1. 创建 blade_region 表（如果不存在）
CREATE TABLE IF NOT EXISTS `blade_region` (
  `code` varchar(12) NOT NULL COMMENT '区划编号',
  `parent_code` varchar(12) DEFAULT NULL COMMENT '父区划编号',
  `ancestors` varchar(255) DEFAULT NULL COMMENT '祖区划编号',
  `name` varchar(32) DEFAULT NULL COMMENT '区划名称',
  `province_code` varchar(12) DEFAULT NULL COMMENT '省级区划编号',
  `province_name` varchar(32) DEFAULT NULL COMMENT '省级名称',
  `city_code` varchar(12) DEFAULT NULL COMMENT '市级区划编号',
  `city_name` varchar(32) DEFAULT NULL COMMENT '市级名称',
  `district_code` varchar(12) DEFAULT NULL COMMENT '区级区划编号',
  `district_name` varchar(32) DEFAULT NULL COMMENT '区级名称',
  `town_code` varchar(12) DEFAULT NULL COMMENT '镇级区划编号',
  `town_name` varchar(32) DEFAULT NULL COMMENT '镇级名称',
  `village_code` varchar(12) DEFAULT NULL COMMENT '村级区划编号',
  `village_name` varchar(32) DEFAULT NULL COMMENT '村级名称',
  `level` int(2) DEFAULT NULL COMMENT '层级',
  `sort` int(2) DEFAULT NULL COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='行政区划表';

-- 2. 创建 miniapp_region_config 表（如果不存在）
CREATE TABLE IF NOT EXISTS `miniapp_region_config` (
  `id` bigint(64) NOT NULL COMMENT '主键',
  `region_code` varchar(20) NOT NULL COMMENT '地区编码',
  `region_name` varchar(100) NOT NULL COMMENT '地区名称',
  `parent_code` varchar(20) DEFAULT NULL COMMENT '父级地区编码',
  `level` int(2) NOT NULL COMMENT '地区层级 1-省 2-市 3-县 4-乡镇 5-村',
  `is_open` int(2) NOT NULL DEFAULT '0' COMMENT '是否开放 0-关闭 1-开放',
  `sort` int(11) DEFAULT '0' COMMENT '排序',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `tenant_id` varchar(12) DEFAULT '000000' COMMENT '租户ID',
  `create_user` bigint(64) DEFAULT NULL COMMENT '创建人',
  `create_dept` bigint(64) DEFAULT NULL COMMENT '创建部门',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_user` bigint(64) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `status` int(2) DEFAULT '1' COMMENT '状态',
  `is_deleted` int(2) DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_region_code` (`region_code`),
  KEY `idx_parent_code` (`parent_code`),
  KEY `idx_level` (`level`),
  KEY `idx_is_open` (`is_open`),
  KEY `idx_tenant_id` (`tenant_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='小程序地区配置表';

-- 3. 插入一些基础的地区数据到 blade_region 表（示例数据）
INSERT IGNORE INTO `blade_region` (`code`, `parent_code`, `ancestors`, `name`, `province_code`, `province_name`, `city_code`, `city_name`, `district_code`, `district_name`, `level`, `sort`) VALUES
-- 江苏省
('320000', '00', '00', '江苏省', '320000', '江苏省', NULL, NULL, NULL, NULL, 1, 1),
-- 连云港市
('320700', '320000', '00,320000', '连云港市', '320000', '江苏省', '320700', '连云港市', NULL, NULL, 2, 1),
-- 连云港市各区县
('320703', '320700', '00,320000,320700', '连云区', '320000', '江苏省', '320700', '连云港市', '320703', '连云区', 3, 1),
('320706', '320700', '00,320000,320700', '海州区', '320000', '江苏省', '320700', '连云港市', '320706', '海州区', 3, 2),
('320707', '320700', '00,320000,320700', '赣榆区', '320000', '江苏省', '320700', '连云港市', '320707', '赣榆区', 3, 3),
('320722', '320700', '00,320000,320700', '东海县', '320000', '江苏省', '320700', '连云港市', '320722', '东海县', 3, 4),
('320723', '320700', '00,320000,320700', '灌云县', '320000', '江苏省', '320700', '连云港市', '320723', '灌云县', 3, 5),
('320724', '320700', '00,320000,320700', '灌南县', '320000', '江苏省', '320700', '连云港市', '320724', '灌南县', 3, 6);

-- 4. 插入一些街道/乡镇数据（示例）
INSERT IGNORE INTO `blade_region` (`code`, `parent_code`, `ancestors`, `name`, `province_code`, `province_name`, `city_code`, `city_name`, `district_code`, `district_name`, `town_code`, `town_name`, `level`, `sort`) VALUES
-- 连云区街道
('320703001', '320703', '00,320000,320700,320703', '海州湾街道', '320000', '江苏省', '320700', '连云港市', '320703', '连云区', '320703001', '海州湾街道', 4, 1),
('320703002', '320703', '00,320000,320700,320703', '连岛街道', '320000', '江苏省', '320700', '连云港市', '320703', '连云区', '320703002', '连岛街道', 4, 2),
('320703003', '320703', '00,320000,320700,320703', '墟沟街道', '320000', '江苏省', '320700', '连云港市', '320703', '连云区', '320703003', '墟沟街道', 4, 3),
-- 海州区街道
('320706001', '320706', '00,320000,320700,320706', '海州街道', '320000', '江苏省', '320700', '连云港市', '320706', '海州区', '320706001', '海州街道', 4, 1),
('320706002', '320706', '00,320000,320700,320706', '幸福路街道', '320000', '江苏省', '320700', '连云港市', '320706', '海州区', '320706002', '幸福路街道', 4, 2),
('320706003', '320706', '00,320000,320700,320706', '朐阳街道', '320000', '江苏省', '320700', '连云港市', '320706', '海州区', '320706003', '朐阳街道', 4, 3);

-- 5. 检查表是否创建成功
SELECT 'blade_region表记录数:' as info, COUNT(*) as count FROM blade_region
UNION ALL
SELECT 'miniapp_region_config表记录数:' as info, COUNT(*) as count FROM miniapp_region_config;
