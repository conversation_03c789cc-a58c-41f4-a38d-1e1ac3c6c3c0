<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.BusinessCardMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="businessCardResultMap" type="org.springblade.business.user.vo.BusinessCardVO">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="company" property="company"/>
        <result column="job_title" property="jobTitle"/>
        <result column="business_profile" property="businessProfile"/>
        <result column="full_name" property="fullName"/>
        <result column="gender" property="gender"/>
        <result column="phone" property="phone"/>
        <result column="address" property="address"/>
        <result column="email" property="email"/>
        <result column="website" property="website"/>
        <result column="weixin" property="weixin"/>
        <result column="avatar" property="avatar"/>
        <result column="images" property="images"/>
        <result column="video" property="video"/>
        <result column="description" property="description"/>
        <result column="is_public" property="isPublic"/>
        <result column="nickname" property="app_nickname"/>
        <result column="mobile" property="app_mobile"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="view_count" property="viewCount"/>
    </resultMap>


    <select id="selectBusinessCardPage" resultMap="businessCardResultMap">
        select ubs.*, uu.nickname, uu.mobile, view_log.view_count as view_count from urb_business_card ubs
                 left join urb_user uu on ubs.create_user = uu.id
                left join (
                    select relevancy_id,count(1) as view_count
                    from urb_view_log
                    where relevancy_type = '1'
                    and is_deleted = 0
                    group by relevancy_id
        ) view_log on ubs.id = view_log.relevancy_id
                 where ubs.is_deleted = 0
                 <if test="businessCard.auditStatus!=null">
                     and ubs.audit_status = #{businessCard.auditStatus}
                 </if>
                 <if test="businessCard.company!=null">
                     and ubs.company like concat('%',#{businessCard.company},'%')
                 </if>
                 <if test="businessCard.fullName!=null">
                     and ubs.full_name like concat('%',#{businessCard.fullName},'%')
                 </if>
                  <if test="businessCard.jobTitle!=null">
                      and ubs.job_title like concat('%',#{businessCard.jobTitle},'%')
                  </if>
                 <if test="businessCard.id!=null">
                     and ubs.id = #{businessCard.id}admin
                 </if>
        order by ubs.create_time desc
    </select>
</mapper>
