/* 机构详情页面样式 */

.institution-detail-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 120rpx;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #e0e0e0;
  border-top: 4rpx solid #1976d2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 20rpx;
  color: #666;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-text {
  color: #666;
  font-size: 28rpx;
  margin-bottom: 40rpx;
}

.retry-btn {
  background-color: #1976d2;
  color: white;
  border-radius: 40rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 内容容器 */
.content-container {
  background-color: white;
}

/* 机构头部 */
.institution-header {
  display: flex;
  padding: 40rpx;
  background-color: white;
  border-bottom: 1rpx solid #e0e0e0;
}

.institution-logo {
  width: 120rpx;
  height: 120rpx;
  margin-right: 30rpx;
}

.logo-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.institution-info {
  flex: 1;
}

.institution-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.institution-rating {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.stars {
  display: flex;
  margin-right: 16rpx;
}

.star {
  color: #ddd;
  font-size: 24rpx;
}

.star.active {
  color: #ffc107;
}

.rating-text {
  font-size: 28rpx;
  color: #333;
  margin-right: 16rpx;
}

.review-count {
  font-size: 24rpx;
  color: #666;
}

.institution-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.favorite-btn {
  background-color: #f5f5f5;
  color: #666;
  border: 1rpx solid #e0e0e0;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
}

.favorite-btn.active {
  background-color: #e3f2fd;
  color: #1976d2;
  border-color: #1976d2;
}

/* Tab导航 */
.tab-nav {
  display: flex;
  background-color: white;
  border-bottom: 1rpx solid #e0e0e0;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 30rpx 0;
  font-size: 28rpx;
  color: #666;
  position: relative;
}

.tab-item.active {
  color: #1976d2;
  font-weight: bold;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: #1976d2;
  border-radius: 2rpx;
}

/* Tab内容 */
.tab-content {
  background-color: white;
  min-height: 400rpx;
}

/* 机构信息内容 */
.info-content {
  padding: 40rpx;
}

.info-section {
  margin-bottom: 40rpx;
}

.info-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.section-content {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.description {
  display: block;
}

.tags {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  background-color: #e3f2fd;
  color: #1976d2;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.image-gallery {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.gallery-image {
  width: 200rpx;
  height: 200rpx;
  border-radius: 8rpx;
}

/* 服务内容 */
.service-content {
  padding: 40rpx;
}

.service-item {
  display: flex;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.service-item:last-child {
  border-bottom: none;
}

.service-item.disabled {
  opacity: 0.5;
}

.service-info {
  flex: 1;
}

.service-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 12rpx;
}

.service-description {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 16rpx;
  line-height: 1.4;
}

.service-meta {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.service-price {
  font-size: 28rpx;
  color: #e91e63;
  font-weight: bold;
}

.service-duration {
  font-size: 24rpx;
  color: #999;
}

.book-btn {
  background-color: #1976d2;
  color: white;
  border-radius: 40rpx;
  padding: 16rpx 32rpx;
  font-size: 26rpx;
  min-width: 120rpx;
}

.book-btn[disabled] {
  background-color: #ccc;
  color: #999;
}

/* 评价内容 */
.review-content {
  padding: 40rpx;
}

.review-item {
  padding: 30rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.review-item:last-child {
  border-bottom: none;
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.user-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  margin-right: 20rpx;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 8rpx;
}

.review-rating {
  display: flex;
}

.review-time {
  font-size: 24rpx;
  color: #999;
}

.review-content-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.review-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
}

/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
  color: #999;
  font-size: 28rpx;
}

/* 底部操作栏 */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  background-color: white;
  border-top: 1rpx solid #e0e0e0;
  padding: 20rpx 40rpx;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));
  z-index: 100;
}

.action-btn {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  border: 1rpx solid #e0e0e0;
  border-radius: 12rpx;
  padding: 20rpx;
  margin: 0 10rpx;
  font-size: 24rpx;
  color: #666;
}

.action-btn.primary {
  background-color: #1976d2;
  color: white;
  border-color: #1976d2;
}

.btn-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.btn-text {
  font-size: 24rpx;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .institution-header {
    padding: 30rpx;
  }

  .institution-name {
    font-size: 32rpx;
  }

  .tab-content {
    min-height: 300rpx;
  }

  .info-content,
  .service-content,
  .review-content {
    padding: 30rpx;
  }
}