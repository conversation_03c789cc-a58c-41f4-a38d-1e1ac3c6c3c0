# 前端开发指导意见

## 开发环境配置

### 1. 环境要求
- **Node.js**: 16.0+
- **包管理器**: npm 8.0+ / yarn 1.22+ / pnpm 7.0+
- **IDE**: VS Code / WebStorm
- **浏览器**: Chrome 90+ / Firefox 88+ / Safari 14+

### 2. 开发工具配置

#### VS Code 推荐插件
```json
{
  "recommendations": [
    "Vue.volar",
    "Vue.vscode-typescript-vue-plugin",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "formulahendry.auto-rename-tag",
    "christian-kohler.path-intellisense"
  ]
}
```

#### VS Code 设置
```json
{
  "editor.formatOnSave": true,
  "editor.defaultFormatter": "esbenp.prettier-vscode",
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "emmet.includeLanguages": {
    "vue": "html"
  }
}
```

### 3. 项目初始化
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 预览生产版本
npm run serve
```

## 代码规范

### 1. 文件命名规范

#### 组件文件
```bash
# 页面组件使用kebab-case
post-list.vue
user-info.vue
category-management.vue

# 公共组件使用PascalCase
BasicContainer.vue
PostItem.vue
UserAvatar.vue

# 工具文件使用camelCase
util.js
auth.js
crypto.js
```

#### 目录命名
```bash
# 使用kebab-case
src/
├── views/
│   ├── ad/
│   │   ├── post/
│   │   └── user/
│   └── system/
├── components/
│   ├── basic-container/
│   └── error-page/
└── utils/
```

### 2. Vue组件规范

#### 组件结构
```vue
<template>
  <!-- 模板内容 -->
  <div class="component-name">
    <!-- 组件内容 -->
  </div>
</template>

<script>
// 导入顺序：第三方库 -> 内部模块 -> 相对路径
import { ref, reactive, computed, onMounted } from 'vue'
import { getList, add, update, remove } from '@/api/ad/post'
import { mapGetters } from 'vuex'

export default {
  name: 'ComponentName',
  components: {},
  props: {
    // 属性定义
  },
  emits: [
    // 事件定义
  ],
  setup(props, { emit }) {
    // 响应式数据
    const data = ref([])
    const loading = ref(false)
    
    // 响应式对象
    const form = reactive({
      title: '',
      content: ''
    })
    
    // 计算属性
    const total = computed(() => data.value.length)
    
    // 生命周期
    onMounted(() => {
      loadData()
    })
    
    // 方法
    const loadData = async () => {
      loading.value = true
      try {
        const res = await getList()
        data.value = res.data
      } catch (error) {
        console.error('加载数据失败:', error)
      } finally {
        loading.value = false
      }
    }
    
    const handleSubmit = () => {
      // 处理提交逻辑
    }
    
    return {
      data,
      loading,
      form,
      total,
      loadData,
      handleSubmit
    }
  }
}
</script>

<style lang="scss" scoped>
.component-name {
  // 样式定义
}
</style>
```

#### Props定义规范
```javascript
props: {
  // 基础类型
  title: {
    type: String,
    required: true,
    default: ''
  },
  count: {
    type: Number,
    default: 0
  },
  visible: {
    type: Boolean,
    default: false
  },
  // 对象类型
  config: {
    type: Object,
    default: () => ({})
  },
  // 数组类型
  list: {
    type: Array,
    default: () => []
  },
  // 自定义验证
  status: {
    type: String,
    validator: (value) => ['active', 'inactive'].includes(value)
  }
}
```

#### 事件定义规范
```javascript
emits: [
  'update',
  'delete',
  'submit',
  'cancel'
]

// 事件触发
const handleUpdate = () => {
  emit('update', { id: 1, data: form })
}
```

### 3. API接口规范

#### 接口文件结构
```javascript
// api/ad/post.js
import request from '@/axios'

// 获取列表
export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/post/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

// 获取详情
export const getDetail = (id) => {
  return request({
    url: '/blade-ad/post/detail',
    method: 'get',
    params: { id }
  })
}

// 新增/更新
export const submit = (row) => {
  return request({
    url: '/blade-ad/post/submit',
    method: 'post',
    data: row
  })
}

// 删除
export const remove = (ids) => {
  return request({
    url: '/blade-ad/post/remove',
    method: 'post',
    params: { ids }
  })
}

// 批量操作
export const batchRemove = (ids) => {
  return request({
    url: '/blade-ad/post/batch-remove',
    method: 'post',
    data: { ids }
  })
}
```

#### 接口使用规范
```javascript
// 在组件中使用API
const loadData = async () => {
  loading.value = true
  try {
    const res = await getList(page.current, page.size, searchForm)
    data.value = res.data.records
    page.total = res.data.total
  } catch (error) {
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

const handleSubmit = async (form) => {
  try {
    if (form.id) {
      await update(form)
      ElMessage.success('更新成功')
    } else {
      await add(form)
      ElMessage.success('添加成功')
    }
    loadData()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}
```

### 4. 路由配置规范

#### 路由结构
```javascript
// router/views/index.js
export default [
  {
    path: '/ad',
    component: Layout,
    redirect: '/ad/post',
    meta: {
      title: '广告管理',
      icon: 'ad',
      permission: 'ad'
    },
    children: [
      {
        path: 'post',
        name: 'Post',
        component: () => import('@/views/ad/post/post.vue'),
        meta: {
          title: '帖子管理',
          permission: 'ad_post',
          keepAlive: true,
          breadcrumb: true
        }
      },
      {
        path: 'category',
        name: 'Category',
        component: () => import('@/views/ad/post/category.vue'),
        meta: {
          title: '分类管理',
          permission: 'ad_category',
          keepAlive: true
        }
      }
    ]
  }
]
```

#### 路由元信息
```javascript
meta: {
  title: '页面标题',           // 页面标题
  icon: 'icon-name',          // 菜单图标
  permission: 'permission',   // 权限标识
  keepAlive: true,           // 是否缓存
  breadcrumb: true,          // 是否显示面包屑
  hidden: false,             // 是否隐藏菜单
  affix: false,              // 是否固定标签
  noCache: false,            // 是否不缓存
  activeMenu: '/path',       // 激活菜单路径
  roles: ['admin'],          // 角色权限
  alwaysShow: false          // 是否总是显示
}
```

### 5. 状态管理规范

#### Store模块结构
```javascript
// store/modules/user.js
const state = {
  token: getToken(),
  userInfo: {},
  roles: [],
  permissions: []
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USER_INFO: (state, userInfo) => {
    state.userInfo = userInfo
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_PERMISSIONS: (state, permissions) => {
    state.permissions = permissions
  }
}

const actions = {
  // 登录
  login({ commit }, userInfo) {
    return new Promise((resolve, reject) => {
      login(userInfo).then(response => {
        const { data } = response
        commit('SET_TOKEN', data.access_token)
        setToken(data.access_token)
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  },
  
  // 获取用户信息
  getInfo({ commit, state }) {
    return new Promise((resolve, reject) => {
      getInfo(state.token).then(response => {
        const { data } = response
        commit('SET_USER_INFO', data)
        commit('SET_ROLES', data.roles)
        commit('SET_PERMISSIONS', data.permissions)
        resolve(data)
      }).catch(error => {
        reject(error)
      })
    })
  },
  
  // 登出
  logout({ commit }) {
    return new Promise((resolve, reject) => {
      logout().then(() => {
        commit('SET_TOKEN', '')
        commit('SET_USER_INFO', {})
        commit('SET_ROLES', [])
        commit('SET_PERMISSIONS', [])
        removeToken()
        resolve()
      }).catch(error => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
```

### 6. 样式规范

#### SCSS使用规范
```scss
// 变量定义
$primary-color: #409EFF;
$success-color: #67C23A;
$warning-color: #E6A23C;
$danger-color: #F56C6C;
$info-color: #909399;

$border-radius: 4px;
$box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);

// 混入定义
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 组件样式
.post-list {
  padding: 20px;
  
  &__header {
    @include flex-center;
    margin-bottom: 20px;
    
    &-title {
      font-size: 18px;
      font-weight: bold;
      color: $primary-color;
    }
  }
  
  &__content {
    background: #fff;
    border-radius: $border-radius;
    box-shadow: $box-shadow;
    
    &-item {
      padding: 15px;
      border-bottom: 1px solid #eee;
      
      &:last-child {
        border-bottom: none;
      }
      
      &-title {
        @include text-ellipsis;
        font-size: 16px;
        margin-bottom: 8px;
      }
      
      &-desc {
        color: #666;
        font-size: 14px;
      }
    }
  }
}
```

#### 响应式设计
```scss
// 断点定义
$breakpoints: (
  xs: 480px,
  sm: 768px,
  md: 992px,
  lg: 1200px,
  xl: 1920px
);

@mixin respond-to($breakpoint) {
  @if map-has-key($breakpoints, $breakpoint) {
    @media (min-width: map-get($breakpoints, $breakpoint)) {
      @content;
    }
  }
}

// 使用示例
.container {
  width: 100%;
  padding: 0 15px;
  
  @include respond-to(sm) {
    max-width: 540px;
    margin: 0 auto;
  }
  
  @include respond-to(md) {
    max-width: 720px;
  }
  
  @include respond-to(lg) {
    max-width: 960px;
  }
  
  @include respond-to(xl) {
    max-width: 1140px;
  }
}
```

## 组件开发最佳实践

### 1. Avue表格组件使用

#### 基础表格配置
```vue
<template>
  <avue-crud
    :option="option"
    :data="data"
    :loading="loading"
    :page="page"
    @search-change="searchChange"
    @search-reset="searchReset"
    @current-change="currentChange"
    @size-change="sizeChange"
    @row-save="rowSave"
    @row-update="rowUpdate"
    @row-del="rowDel">
  </avue-crud>
</template>

<script>
export default {
  data() {
    return {
      option: {
        height: 'auto',
        calcHeight: 210,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        column: [
          {
            label: '标题',
            prop: 'title',
            search: true,
            rules: [{
              required: true,
              message: '请输入标题',
              trigger: 'blur'
            }]
          },
          {
            label: '内容',
            prop: 'content',
            type: 'textarea',
            span: 24,
            hide: true
          },
          {
            label: '状态',
            prop: 'status',
            type: 'select',
            dicData: [
              { label: '启用', value: 1 },
              { label: '禁用', value: 0 }
            ],
            search: true
          },
          {
            label: '创建时间',
            prop: 'createTime',
            type: 'datetime',
            format: 'YYYY-MM-DD HH:mm:ss',
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            search: true,
            searchSpan: 12
          }
        ]
      }
    }
  }
}
</script>
```

#### 高级表格功能
```javascript
// 自定义列渲染
{
  label: '操作',
  prop: 'menu',
  fixed: 'right',
  width: 200,
  slot: true,
  search: false
}

// 表格插槽
<template #menu="{ row, index }">
  <el-button type="primary"  @click="handleEdit(row)">
    编辑
  </el-button>
  <el-button type="danger"  @click="handleDelete(row)">
    删除
  </el-button>
</template>

// 表单验证
rules: [
  {
    required: true,
    message: '请输入标题',
    trigger: 'blur'
  },
  {
    min: 2,
    max: 50,
    message: '标题长度在2到50个字符',
    trigger: 'blur'
  }
]
```

### 2. 表单组件开发

#### 表单验证
```vue
<template>
  <el-form
    ref="formRef"
    :model="form"
    :rules="rules"
    label-width="100px">
    <el-form-item label="标题" prop="title">
      <el-input v-model="form.title" placeholder="请输入标题" />
    </el-form-item>
    <el-form-item label="内容" prop="content">
      <el-input
        v-model="form.content"
        type="textarea"
        :rows="4"
        placeholder="请输入内容" />
    </el-form-item>
    <el-form-item>
      <el-button type="primary" @click="handleSubmit">提交</el-button>
      <el-button @click="handleReset">重置</el-button>
    </el-form-item>
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      form: {
        title: '',
        content: ''
      },
      rules: {
        title: [
          { required: true, message: '请输入标题', trigger: 'blur' },
          { min: 2, max: 50, message: '标题长度在2到50个字符', trigger: 'blur' }
        ],
        content: [
          { required: true, message: '请输入内容', trigger: 'blur' },
          { min: 10, max: 500, message: '内容长度在10到500个字符', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    handleSubmit() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // 提交表单
          this.submitForm()
        }
      })
    },
    handleReset() {
      this.$refs.formRef.resetFields()
    }
  }
}
</script>
```

### 3. 文件上传组件

#### 图片上传
```vue
<template>
  <el-upload
    :action="uploadUrl"
    :headers="headers"
    :data="uploadData"
    :file-list="fileList"
    :on-success="handleSuccess"
    :on-error="handleError"
    :before-upload="beforeUpload"
    list-type="picture-card"
    :limit="6"
    accept="image/*">
    <el-icon><Plus /></el-icon>
  </el-upload>
</template>

<script>
export default {
  data() {
    return {
      uploadUrl: '/api/upload',
      headers: {
        Authorization: `Bearer ${this.$store.state.user.token}`
      },
      uploadData: {
        type: 'image'
      },
      fileList: []
    }
  },
  methods: {
    beforeUpload(file) {
      const isImage = file.type.startsWith('image/')
      const isLt2M = file.size / 1024 / 1024 < 2
      
      if (!isImage) {
        ElMessage.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        ElMessage.error('图片大小不能超过2MB!')
        return false
      }
      return true
    },
    handleSuccess(response, file, fileList) {
      ElMessage.success('上传成功')
      this.fileList = fileList
    },
    handleError() {
      ElMessage.error('上传失败')
    }
  }
}
</script>
```

## 性能优化建议

### 1. 组件懒加载
```javascript
// 路由懒加载
const routes = [
  {
    path: '/post',
    component: () => import('@/views/ad/post/post.vue')
  }
]

// 组件懒加载
const AsyncComponent = defineAsyncComponent(() => import('./AsyncComponent.vue'))
```

### 2. 虚拟滚动
```vue
<template>
  <el-table
    :data="tableData"
    height="400"
    :virtual-scrolling="{ enabled: true }">
    <el-table-column prop="title" label="标题" />
    <el-table-column prop="content" label="内容" />
  </el-table>
</template>
```

### 3. 图片懒加载
```vue
<template>
  <el-image
    lazy
    :src="imageUrl"
    :preview-src-list="[imageUrl]">
  </el-image>
</template>
```

### 4. 防抖节流
```javascript
// 防抖
const debounce = (func, wait) => {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

// 节流
const throttle = (func, limit) => {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}

// 使用示例
const handleSearch = debounce(() => {
  loadData()
}, 300)

const handleScroll = throttle(() => {
  // 滚动处理
}, 100)
```

## 错误处理

### 1. 全局错误处理
```javascript
// main.js
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err)
  console.error('错误信息:', info)
  
  // 发送错误日志
  logError(err, info)
  
  // 显示用户友好的错误信息
  ElMessage.error('系统出现错误，请稍后重试')
}

// 异步错误处理
window.addEventListener('unhandledrejection', event => {
  console.error('未处理的Promise拒绝:', event.reason)
  event.preventDefault()
})
```

### 2. HTTP错误处理
```javascript
// axios.js
axios.interceptors.response.use(
  response => {
    const { code, msg } = response.data
    
    if (code !== 200) {
      // 业务错误处理
      if (code === 401) {
        // 未授权，跳转登录
        store.dispatch('user/logout')
        router.push('/login')
      } else {
        ElMessage.error(msg || '请求失败')
      }
      return Promise.reject(new Error(msg))
    }
    
    return response
  },
  error => {
    // 网络错误处理
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 400:
          ElMessage.error('请求参数错误')
          break
        case 401:
          ElMessage.error('未授权，请重新登录')
          store.dispatch('user/logout')
          router.push('/login')
          break
        case 403:
          ElMessage.error('拒绝访问')
          break
        case 404:
          ElMessage.error('请求的资源不存在')
          break
        case 500:
          ElMessage.error('服务器内部错误')
          break
        default:
          ElMessage.error('网络错误')
      }
    } else {
      ElMessage.error('网络连接失败')
    }
    
    return Promise.reject(error)
  }
)
```

### 3. 组件错误边界
```vue
<template>
  <div v-if="error" class="error-boundary">
    <h3>组件加载失败</h3>
    <p>{{ error.message }}</p>
    <el-button @click="handleRetry">重试</el-button>
  </div>
  <div v-else>
    <slot />
  </div>
</template>

<script>
export default {
  name: 'ErrorBoundary',
  data() {
    return {
      error: null
    }
  },
  errorCaptured(err, vm, info) {
    this.error = err
    return false // 阻止错误继续向上传播
  },
  methods: {
    handleRetry() {
      this.error = null
      this.$forceUpdate()
    }
  }
}
</script>
```

## 安全考虑

### 1. XSS防护
```javascript
// 输入验证
const validateInput = (input) => {
  return input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
}

// 输出编码
const escapeHtml = (text) => {
  const div = document.createElement('div')
  div.textContent = text
  return div.innerHTML
}
```

### 2. CSRF防护
```javascript
// 添加CSRF Token
axios.defaults.headers.common['X-CSRF-TOKEN'] = getCsrfToken()
```

### 3. 敏感信息保护
```javascript
// 不在前端存储敏感信息
// 使用加密存储
import CryptoJS from 'crypto-js'

const encryptData = (data, key) => {
  return CryptoJS.AES.encrypt(JSON.stringify(data), key).toString()
}

const decryptData = (encryptedData, key) => {
  const bytes = CryptoJS.AES.decrypt(encryptedData, key)
  return JSON.parse(bytes.toString(CryptoJS.enc.Utf8))
}
```

## 测试规范

### 1. 单元测试
```javascript
// tests/unit/PostList.spec.js
import { mount } from '@vue/test-utils'
import PostList from '@/views/ad/post/post.vue'

describe('PostList', () => {
  it('should render correctly', () => {
    const wrapper = mount(PostList)
    expect(wrapper.exists()).toBe(true)
  })
  
  it('should load data on mount', async () => {
    const wrapper = mount(PostList)
    await wrapper.vm.$nextTick()
    expect(wrapper.vm.loading).toBe(false)
  })
  
  it('should handle search', async () => {
    const wrapper = mount(PostList)
    const searchInput = wrapper.find('[data-test="search-input"]')
    await searchInput.setValue('test')
    expect(wrapper.vm.search.title).toBe('test')
  })
})
```

### 2. 集成测试
```javascript
// tests/integration/api.spec.js
import { getList } from '@/api/ad/post'

describe('Post API', () => {
  it('should fetch post list', async () => {
    const response = await getList(1, 10)
    expect(response.data).toBeDefined()
    expect(Array.isArray(response.data.records)).toBe(true)
  })
})
```

## 部署配置

### 1. 环境变量配置
```bash
# .env.development
VITE_APP_BASE=/
VITE_APP_API_BASE_URL=http://localhost:80
VITE_APP_TITLE=易贴易找管理系统(开发环境)

# .env.production
VITE_APP_BASE=/
VITE_APP_API_BASE_URL=https://api.example.com
VITE_APP_TITLE=易贴易找管理系统
```

### 2. 构建优化
```javascript
// vite.config.mjs
export default defineConfig({
  build: {
    target: 'es2015',
    outDir: 'dist',
    assetsDir: 'assets',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['vue', 'vue-router', 'vuex'],
          element: ['element-plus'],
          avue: ['@smallwei/avue']
        }
      }
    }
  }
})
```

### 3. 缓存策略
```javascript
// 静态资源缓存
const staticCache = {
  name: 'static-cache',
  urls: [
    '/static/js/vendor.js',
    '/static/css/vendor.css'
  ]
}

// 动态缓存
const dynamicCache = {
  name: 'dynamic-cache',
  urls: [
    '/api/ad/post/list'
  ]
}
```

## 开发流程

### 1. 功能开发流程
1. **需求分析**: 明确功能需求和交互要求
2. **组件设计**: 设计组件结构和接口
3. **API对接**: 定义API接口和数据结构
4. **组件开发**: 实现组件功能和样式
5. **测试验证**: 单元测试和集成测试
6. **代码审查**: 团队代码审查
7. **部署上线**: 生产环境部署

### 2. 代码提交规范
```bash
# 提交格式
feat: 新增帖子管理功能
fix: 修复表格分页显示问题
docs: 更新API文档
style: 调整按钮样式
refactor: 重构用户管理组件
test: 添加单元测试
chore: 更新依赖包

# 提交示例
git add .
git commit -m "feat: 新增帖子管理功能

- 添加帖子列表页面
- 实现帖子增删改查功能
- 添加帖子审核功能
- 优化表格显示效果"
```

### 3. 版本发布流程
1. **功能开发**: 完成功能开发和测试
2. **版本号更新**: 更新package.json版本号
3. **构建测试**: 本地构建和测试
4. **代码合并**: 合并到主分支
5. **自动化构建**: CI/CD自动构建
6. **部署测试**: 测试环境部署验证
7. **生产部署**: 生产环境部署
8. **版本标签**: 打版本标签

## 总结

本开发指导意见涵盖了前端开发的各个方面：

1. **开发环境**: 完整的开发环境配置
2. **代码规范**: 统一的代码风格和结构
3. **组件开发**: 组件开发的最佳实践
4. **性能优化**: 提升应用性能的方法
5. **错误处理**: 完善的错误处理机制
6. **安全考虑**: 前端安全防护措施
7. **测试规范**: 测试策略和实现
8. **部署配置**: 生产环境部署配置
9. **开发流程**: 规范的开发流程

遵循这些规范可以确保代码质量、提高开发效率、降低维护成本，为项目的长期发展奠定良好基础。 