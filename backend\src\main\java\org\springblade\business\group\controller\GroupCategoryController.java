/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.group.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.group.entity.GroupCategory;
import org.springblade.business.group.vo.GroupCategoryVO;
import org.springblade.business.group.wrapper.GroupCategoryWrapper;
import org.springblade.business.group.service.IGroupCategoryService;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.List;

/**
 * 群分类表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-25
 */
@RestController
@AllArgsConstructor
@RequestMapping("/balde-ad/groupcategory")
@io.swagger.v3.oas.annotations.tags.Tag(name = "群分类表", description = "群分类表接口")
public class GroupCategoryController extends BladeController {

	private IGroupCategoryService groupCategoryService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入groupCategory")
	public R<GroupCategoryVO> detail(GroupCategory groupCategory) {
		GroupCategory detail = groupCategoryService.getOne(Condition.getQueryWrapper(groupCategory));
		return R.data(GroupCategoryWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 群分类表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入groupCategory")
	public R<List<GroupCategoryVO>> list(GroupCategory groupCategory, Query query) {
		//根据排序字段正序,创建时间倒叙
		query.setAscs("sort_order");
		query.setDescs("create_time");
		List<GroupCategory> pages = groupCategoryService.list(Condition.getQueryWrapper(groupCategory));
		return R.data(GroupCategoryWrapper.build().listVO(pages));
	}


	/**
	 * 自定义分页 群分类表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入groupCategory")
	public R<IPage<GroupCategoryVO>> page(GroupCategoryVO groupCategory, Query query) {
		IPage<GroupCategoryVO> pages = groupCategoryService.selectGroupCategoryPage(Condition.getPage(query), groupCategory);
		return R.data(pages);
	}

	/**
	 * 新增 群分类表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入groupCategory")
	public R save(@Valid @RequestBody GroupCategory groupCategory) {
		return R.status(groupCategoryService.save(groupCategory));
	}

	/**
	 * 修改 群分类表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入groupCategory")
	public R update(@Valid @RequestBody GroupCategory groupCategory) {
		return R.status(groupCategoryService.updateById(groupCategory));
	}

	/**
	 * 新增或修改 群分类表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入groupCategory")
	public R submit(@Valid @RequestBody GroupCategory groupCategory) {
		return R.status(groupCategoryService.saveOrUpdate(groupCategory));
	}


	/**
	 * 删除 群分类表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(groupCategoryService.deleteLogic(Func.toLongList(ids)));
	}


}
