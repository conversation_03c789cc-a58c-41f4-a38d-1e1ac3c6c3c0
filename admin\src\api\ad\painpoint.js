import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/painpoint/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getPage = (current, size, params) => { 
  return request({
    url: '/blade-ad/painpoint/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-ad/painpoint/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/painpoint/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-ad/painpoint/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-ad/painpoint/submit',
    method: 'post',
    data: row
  })
}

// 审核反馈
export const auditPainpoint = (auditData) => {
  return request({
    url: '/blade-ad/painpoint/audit',
    method: 'post',
    data: auditData
  })
}

// 批量审核
export const batchAudit = (auditData) => {
  return request({
    url: '/blade-ad/painpoint/batch-audit',
    method: 'post',
    params: auditData
  })
}

