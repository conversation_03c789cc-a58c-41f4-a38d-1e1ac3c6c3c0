<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.modules.system.mapper.RegionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="regionResultMap" type="org.springblade.modules.system.entity.Region">
        <id column="code" property="code"/>
        <result column="parent_code" property="parentCode"/>
        <result column="ancestors" property="ancestors"/>
        <result column="name" property="name"/>
        <result column="province_code" property="provinceCode"/>
        <result column="province_name" property="provinceName"/>
        <result column="city_code" property="cityCode"/>
        <result column="city_name" property="cityName"/>
        <result column="district_code" property="districtCode"/>
        <result column="district_name" property="districtName"/>
        <result column="town_code" property="townCode"/>
        <result column="town_name" property="townName"/>
        <result column="village_code" property="villageCode"/>
        <result column="village_name" property="villageName"/>
        <result column="level" property="level"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="regionVOResultMap" type="org.springblade.modules.system.vo.RegionVO">
        <id column="code" property="code"/>
        <result column="parent_code" property="parentCode"/>
        <result column="ancestors" property="ancestors"/>
        <result column="name" property="name"/>
        <result column="province_code" property="provinceCode"/>
        <result column="province_name" property="provinceName"/>
        <result column="city_code" property="cityCode"/>
        <result column="city_name" property="cityName"/>
        <result column="district_code" property="districtCode"/>
        <result column="district_name" property="districtName"/>
        <result column="town_code" property="townCode"/>
        <result column="town_name" property="townName"/>
        <result column="village_code" property="villageCode"/>
        <result column="village_name" property="villageName"/>
        <result column="level" property="level"/>
        <result column="sort" property="sort"/>
        <result column="remark" property="remark"/>
        <result column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="org.springblade.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
        <result column="has_children" property="hasChildren"/>
    </resultMap>

    <select id="lazyList" resultMap="regionVOResultMap">
        SELECT
            region.*,
            ( SELECT CASE WHEN count( 1 ) > 0 THEN 1 ELSE 0 END FROM blade_region WHERE parent_code = region.code ) AS "has_children"
        FROM
            blade_region region
        <where>
            <if test="param1!=null">
                and region.parent_code = #{param1}
            </if>
            <if test="param2.code!=null and param2.code!=''">
                and region.code like concat(concat('%', #{param2.code}),'%')
            </if>
            <if test="param2.name!=null and param2.name!=''">
                and region.name like concat(concat('%', #{param2.name}),'%')
            </if>
        </where>
    </select>

    <select id="lazyTree" resultMap="treeNodeResultMap">
        SELECT
            region.code AS "id",
            region.parent_code AS "parent_id",
            region.name AS "title",
            region.code AS "value",
            region.code AS "key",
            ( SELECT CASE WHEN count( 1 ) > 0 THEN 1 ELSE 0 END FROM blade_region WHERE parent_code = region.code ) AS "has_children"
        FROM
            blade_region region
        <where>
            <if test="param1!=null">
                and region.parent_code = #{param1}
            </if>
            <if test="param2.code!=null and param2.code!=''">
                and region.code like concat(concat('%', #{param2.code}),'%')
            </if>
            <if test="param2.name!=null and param2.name!=''">
                and region.name like concat(concat('%', #{param2.name}),'%')
            </if>
        </where>
    </select>

</mapper>
