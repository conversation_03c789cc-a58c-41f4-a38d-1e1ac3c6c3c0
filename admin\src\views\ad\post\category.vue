<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               :search="search"
               :page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   
                   icon="el-icon-delete"
                   plain
                   v-if="permission.category_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   
                   icon="el-icon-plus"
                   plain
                   v-if="permission.category_add"
                   @click="handleAddTag">管理标签
        </el-button>
      </template>
      <template #status="{ row }">
        <el-tag :type="row.status === 1 ? 'success' : 'danger'">
          {{ row.status === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>
      <template #enabled="{ row }">
        <el-tag :type="row.enabled === 1 ? 'success' : 'danger'">
          {{ row.enabled === 1 ? '启用' : '禁用' }}
        </el-tag>
      </template>
      <template #enableAudit="{ row }">
        <el-tag :type="row.enableAudit === 1 ? 'warning' : 'info'">
          {{ row.enableAudit === 1 ? '启用审核' : '无需审核' }}
        </el-tag>
      </template>
      <template #parentId="{ row }">
        <span v-if="row.parentId === 0">顶级分类</span>
        <span v-else>{{ getParentName(row.parentId) }}</span>
      </template>
      <template #allowTags="{ row }">
        <el-tag v-for="tag in row.allowTags" :key="tag" type="info" style="margin-right: 5px;">
          {{ tag }}
        </el-tag>
      </template>
      <template #tags="{ row }">
        <el-tag v-for="tag in row.tags" :key="tag.id" :color="tag.color" style="margin-right: 5px;">
          {{ tag.tagName }}
        </el-tag>
      </template>
    </avue-crud>

    <!-- 标签管理弹窗 -->
    <el-dialog 
      title="标签管理" 
      v-model="tagDialogVisible" 
      width="900px"
      :close-on-click-modal="false"
      :before-close="handleTagDialogClose">
      <div class="tag-management">
        <!-- 标签类型切换Tab -->
        <el-tabs v-model="currentTagType" @tab-click="handleTagTypeChange">
          <el-tab-pane label="分类标签" :name="1"></el-tab-pane>
          <el-tab-pane label="反馈标签" :name="2"></el-tab-pane>
          <el-tab-pane label="举报标签" :name="3"></el-tab-pane>
        </el-tabs>
        <!-- 当前分类信息 -->
        <div class="category-info">
          <el-alert
            :title="`当前分类：${currentCategoryName}`"
            type="info"
            :closable="false"
            show-icon>
          </el-alert>
        </div>
        <!-- 标签管理主界面 -->
        <div class="tag-management-content">
          <!-- 左侧：当前分类标签 -->
          <div class="tag-section current-tags-section">
            <div class="section-header">
              <h4>当前分类标签 ({{ currentCategoryTags.length }})</h4>
              <el-button 
                type="primary" 
                @click="refreshCategoryTags">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
            <div class="current-tags">
              <el-tag 
                v-for="tag in currentCategoryTags" 
                :key="tag.id" 
                :color="tag.color"
                closable
                @close="removeTag(tag.id)"
                style="margin: 5px; cursor: pointer;"
                @click="editTag(tag)">
                {{ tag.tagName || tag.label }}
                <span class="tag-count" v-if="tag.useCount">({{ tag.useCount }})</span>
              </el-tag>
              <div v-if="currentCategoryTags.length === 0" class="empty-tags">
                <el-empty description="暂无标签" :image-size="60"></el-empty>
              </div>
            </div>
          </div>
          <!-- 右侧：标签操作 -->
          <div class="tag-section operations-section">
            <!-- 创建新标签 -->
            <div class="operation-group">
              <h4>创建新标签</h4>
              <div class="add-tag-form">
                <el-form :model="newTagForm" :rules="tagRules" ref="tagFormRef" label-width="80px">
                  <el-form-item label="标签名称" prop="tagName">
                    <el-input 
                      v-model="newTagForm.tagName" 
                      placeholder="请输入标签名称"
                      maxlength="20"
                      show-word-limit>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="标签颜色" prop="color">
                    <el-color-picker 
                      v-model="newTagForm.color"
                      show-alpha>
                    </el-color-picker>
                  </el-form-item>
                  <el-form-item label="描述说明" prop="description">
                    <el-input 
                      v-model="newTagForm.description" 
                      type="textarea"
                      :rows="3"
                      placeholder="请输入标签描述"
                      maxlength="100"
                      show-word-limit>
                    </el-input>
                  </el-form-item>
                  <el-form-item label="排序序号" prop="sortOrder">
                    <el-input-number 
                      v-model="newTagForm.sortOrder" 
                      :min="0"
                      :max="999"
                      placeholder="排序序号">
                    </el-input-number>
                  </el-form-item>
                  <el-form-item>
                    <el-button 
                      type="primary" 
                      @click="createTag"
                      :loading="createTagLoading"
                      :disabled="!newTagForm.tagName.trim()">
                      创建标签
                    </el-button>
                    <el-button @click="resetTagForm">重置</el-button>
                  </el-form-item>
                </el-form>
              </div>
            </div>
            <!-- 选择已有标签 -->
            <div class="operation-group">
              <div class="section-header">
                <h4>选择已有标签 ({{ availableTags.length }})</h4>
                <el-button 
                  type="success" 
                  size="small"
                  @click="refreshAvailableTags">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
              <div class="available-tags">
                <el-tag 
                  v-for="tag in availableTags" 
                  :key="tag.id" 
                  :color="tag.color"
                  @click="addTag(tag.id)"
                  style="margin: 5px; cursor: pointer;"
                  :class="{ 'tag-disabled': isTagAdded(tag.id) }">
                  {{ tag.tagName || tag.label }}
                  <span class="tag-count" v-if="tag.useCount">({{ tag.useCount }})</span>
                </el-tag>
                <div v-if="availableTags.length === 0" class="empty-tags">
                  <el-empty description="暂无可用标签" :image-size="60"></el-empty>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="tagDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="saveTagChanges">保存更改</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 标签编辑弹窗 -->
    <el-dialog 
      title="编辑标签" 
      v-model="editTagDialogVisible" 
      width="500px"
      :close-on-click-modal="false">
      <el-form :model="editTagForm" :rules="tagRules" ref="editTagFormRef" label-width="80px">
        <el-form-item label="标签名称" prop="tagName">
          <el-input 
            v-model="editTagForm.tagName" 
            placeholder="请输入标签名称"
            maxlength="20"
            show-word-limit>
          </el-input>
        </el-form-item>
        <el-form-item label="标签颜色" prop="color">
          <el-color-picker 
            v-model="editTagForm.color"
            show-alpha>
          </el-color-picker>
        </el-form-item>
        <el-form-item label="描述说明" prop="description">
          <el-input 
            v-model="editTagForm.description" 
            type="textarea"
            :rows="3"
            placeholder="请输入标签描述"
            maxlength="100"
            show-word-limit>
          </el-input>
        </el-form-item>
        <el-form-item label="排序序号" prop="sortOrder">
          <el-input-number 
            v-model="editTagForm.sortOrder" 
            :min="0"
            :max="999"
            placeholder="排序序号">
          </el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="editTagForm.status" placeholder="请选择状态">
            <el-option label="启用" :value="1"></el-option>
            <el-option label="禁用" :value="0"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="editTagDialogVisible = false">取消</el-button>
          <el-button 
            type="primary" 
            @click="updateTag"
            :loading="updateTagLoading">
            保存
          </el-button>
        </div>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/category";
  import {mapGetters} from "vuex";
  import { Picture, Upload, Delete, Refresh } from '@element-plus/icons-vue'

  import request from '@/axios'

  // 替换为对象方式导入
  import * as TagApi from "@/api/ad/tag";

  export default {
    components: {
      Picture,
      Upload,
      Delete,
      Refresh
    },
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        categoryList: [], // 用于父级分类选择
        tagDialogVisible: false,
        currentCategoryId: null,
        currentTagType: 1, // 1=分类标签 2=反馈标签 3=举报标签
        currentCategoryTags: [],
        availableTags: [],
        newTagForm: {
          tagName: '',
          color: '#1890ff',
          description: '',
          sortOrder: 0,
          type: 1
        },
        createTagLoading: false,
        editTagDialogVisible: false,
        editTagForm: {
          id: '',
          tagName: '',
          color: '#1890ff',
          description: '',
          sortOrder: 0,
          status: 1,
          type: 1
        },
        updateTagLoading: false,
        tagRules: {
          tagName: [
            { required: true, message: '请输入标签名称', trigger: 'blur' },
            { min: 1, max: 20, message: '标签名称长度在1到20个字符', trigger: 'blur' }
          ],
          color: [
            { required: true, message: '请选择标签颜色', trigger: 'change' }
          ],
          description: [
            { required: false, message: '请输入标签描述', trigger: 'blur' },
            { max: 100, message: '标签描述长度不能超过100个字符', trigger: 'blur' }
          ],
          sortOrder: [
            { required: true, message: '请输入排序序号', trigger: 'blur' },
            { type: 'number', min: 0, max: 999, message: '排序序号必须在0-999之间', trigger: 'blur' }
          ],
          status: [
            { required: true, message: '请选择状态', trigger: 'change' }
          ]
        },
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "ID",
              prop: "id",
            },
            {
              label: "分类名称",
              prop: "name",
              search: true,
              rules: [{
                required: true,
                message: "请输入分类名称",
                trigger: "blur"
              }]
            },
            {
              label: "上级分类",
              prop: "parentId",
              type: "select",
              dicData: [],
              slot: true,
            },
            {
              label: "分类图标",
              prop: "icon",
              type: 'upload',
              action: '/blade-system/file-upload/upload',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('blade-auth')}`
              },
              data: {
                uploadSource: 'admin',
                businessType: 'category-icon'
              },
              accept: 'image/*',
              limit: 1,
              tip: '支持jpg/png/gif格式，建议尺寸32x32px，文件大小不超过2MB',
              propsHttp: {
                url: 'accessUrl',
                name: 'fileName',
                res: 'data'
              },
              rules: [{
                required: false,
                message: "请上传分类图标",
                trigger: "change"
              }]
            },
            {
              label: "分类描述",
              prop: "description",
              type: "textarea",
              span: 24,
              hide: true
            },
            {
              label: "提示信息",
              prop: "tip",
              type: "textarea",
              span: 24,
              hide: true
            },
            {
              label: "最大图片数",
              prop: "maxImages",
              type: "number",
              rules: [{
                required: true,
                message: "请输入最大图片数",
                trigger: "blur"
              }]
            },
            {
              label: "排序序号",
              prop: "sort",
              type: "number",
              rules: [{
                required: true,
                message: "请输入排序序号",
                trigger: "blur"
              }]
            },
            {
              label: "是否启用",
              prop: "enabled",
              type: "select",
              dicData: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ],
              search: true,
              slot: true
            },
            {
              label: "启用审核",
              prop: "enableAudit",
              type: "select",
              dicData: [
                { label: '启用审核', value: 1 },
                { label: '无需审核', value: 0 }
              ],
              search: true,
              slot: true
            },
            {
              label: "状态",
              prop: "status",
              type: "select",
              dicData: [
                { label: '启用', value: 1 },
                { label: '禁用', value: 0 }
              ],
              search: true,
              slot: true
            },
            // {
            //   label: "标签",
            //   prop: "tags",
            //   slot: true,
            //   hide: true
            // },
            {
              label: "创建时间",
              prop: "createTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              search: true,
              searchSpan: 12
            },
            {
              label: "更新时间",
              prop: "updateTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              hide: true
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.category_add, false),
          viewBtn: this.validData(this.permission.category_view, false),
          delBtn: this.validData(this.permission.category_delete, false),
          editBtn: this.validData(this.permission.category_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
      currentCategoryName() {
        const category = this.categoryList.find(cat => cat.id === this.currentCategoryId);
        return category ? category.name : '未命名分类';
      }
    },
    mounted() {
      this.loadCategoryList();
    },
    methods: {
      loadCategoryList() {
        // 加载所有分类用于父级选择
        getList(1, 1000, {}).then(res => {
          const categories = res.data.data.records;
          this.categoryList = categories;
          // 更新父级分类选项
          this.option.column.find(col => col.prop === 'parentId').dicData = [
            { label: '顶级分类', value: 0 },
            ...categories.map(cat => ({ label: cat.name, value: cat.id }))
          ];
        });
      },
      getParentName(parentId) {
        const parent = this.categoryList.find(cat => cat.id === parentId);
        return parent ? parent.name : '未知分类';
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.loadCategoryList(); // 重新加载分类列表
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      handleImageError(){
        this.$message.error("图片上传失败");
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.loadCategoryList(); // 重新加载分类列表
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.loadCategoryList(); // 重新加载分类列表
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.loadCategoryList(); // 重新加载分类列表
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleRefresh() {
        this.onLoad(this.page);
        this.loadCategoryList();
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },
      handleAddTag() {
        if (this.selectionList.length !== 1) {
          this.$message.warning("请选择一个分类进行标签管理");
          return;
        }
        this.currentCategoryId = this.selectionList[0].id;
        this.currentTagType = 1;
        this.loadCategoryTags();
        this.loadAvailableTags();
        this.tagDialogVisible = true;
      },
      handleTagTypeChange(tab) {
        // 只做类型赋值，数据加载交给 watch
        this.currentTagType = Number(tab.name);
      },
      loadCategoryTags() {
        if (!this.currentCategoryId) return;
        if (!this.currentTagType) {
          this.$message.error('标签类型不能为空');
          return;
        }
        TagApi.getTagsByCategoryAndType(this.currentCategoryId, this.currentTagType).then(res => {
          if (res.data.code === 200) {
            this.currentCategoryTags = res.data.data || [];
          } else {
            this.$message.error(res.data.msg || '加载分类标签失败');
          }
        }).catch(error => {
          this.$message.error('加载分类标签失败：' + (error.message || '网络错误'));
        });
      },
      loadAvailableTags() {
        if (!this.currentTagType) {
          this.$message.error('标签类型不能为空');
          return;
        }
        TagApi.getTagList({ current: 1, size: 1000, type: this.currentTagType }).then(res => {
          if (res.data.code === 200) {
            this.availableTags = res.data.data.records || [];
          } else {
            this.$message.error(res.data.msg || '加载可用标签失败');
          }
        }).catch(error => {
          this.$message.error('加载可用标签失败：' + (error.message || '网络错误'));
        });
      },
      addTag(tagId) {
        if (this.isTagAdded(tagId)) {
          this.$message.warning('该标签已添加到此分类');
          return;
        }
        TagApi.bindTag({
          categoryId: this.currentCategoryId,
          tagId,
          type: Number(this.currentTagType)
        }).then(res => {
          if (res.data.code === 200) {
            this.$message.success('标签添加成功');
            this.loadCategoryTags();
          } else {
            this.$message.error(res.data.msg || '标签添加失败');
          }
        }).catch(error => {
          this.$message.error('标签添加失败：' + (error.message || '网络错误'));
        });
      },
      removeTag(tagId) {
        this.$confirm('确定要从此分类中移除该标签吗？', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          TagApi.removeTagFromCategory({
            categoryId: this.currentCategoryId,
            tagId,
            type: Number(this.currentTagType)
          }).then(res => {
            if (res.data.code === 200) {
              this.$message.success('标签移除成功');
              this.loadCategoryTags();
            } else {
              this.$message.error(res.data.msg || '标签移除失败');
            }
          }).catch(error => {
            this.$message.error('标签移除失败：' + (error.message || '网络错误'));
          });
        });
      },
      createTag() {
        this.$refs.tagFormRef.validate((valid) => {
          if (valid) {
            this.createTagLoading = true;
            TagApi.createTag({
              tagName: this.newTagForm.tagName,
              color: this.newTagForm.color,
              description: this.newTagForm.description,
              sortOrder: this.newTagForm.sortOrder,
              type: Number(this.currentTagType),
              categoryId: this.currentCategoryId
            }).then(res => {
              if (res.data.code === 200) {
                this.$message.success('标签创建成功');
                this.resetTagForm();
                this.loadCategoryTags();
                this.loadAvailableTags();
              } else {
                this.$message.error(res.data.msg || '标签创建失败');
              }
            }).catch(error => {
              this.$message.error('标签创建失败：' + (error.message || '网络错误'));
            }).finally(() => {
              this.createTagLoading = false;
            });
          }
        });
      },
      resetTagForm() {
        this.newTagForm = {
          tagName: '',
          color: '#1890ff',
          description: '',
          sortOrder: 0,
          type: Number(this.currentTagType)
        };
        this.$refs.tagFormRef && this.$refs.tagFormRef.resetFields();
      },
      editTag(tag) {
        this.editTagForm = {
          id: tag.id,
          tagName: tag.tagName || tag.label,
          color: tag.color,
          description: tag.description || '',
          sortOrder: tag.sortOrder || tag.sort_order || 0,
          status: tag.status || 1,
          type: Number(this.currentTagType)
        };
        this.editTagDialogVisible = true;
      },
      updateTag() {
        this.$refs.editTagFormRef.validate((valid) => {
          if (valid) {
            this.updateTagLoading = true;
            TagApi.submit(this.editTagForm).then(res => {
              if (res.data.code === 200) {
                this.$message.success('标签更新成功');
                this.editTagDialogVisible = false;
                this.loadCategoryTags();
                this.loadAvailableTags();
              } else {
                this.$message.error(res.data.msg || '标签更新失败');
              }
            }).catch(error => {
              this.$message.error('标签更新失败：' + (error.message || '网络错误'));
            }).finally(() => {
              this.updateTagLoading = false;
            });
          }
        });
      },
      handleTagDialogClose() {
        this.tagDialogVisible = false;
        this.currentCategoryId = null;
        this.currentCategoryTags = [];
        this.availableTags = [];
        this.resetTagForm();
        this.editTagDialogVisible = false;
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
            // 确保图标字段正确初始化
            if (this.form.icon && typeof this.form.icon === 'string') {
              // 图标字段已经是URL格式，无需处理
            }
          });
        } else {
          // 新增模式，清空图标字段
          this.form.icon = null;
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      refreshCategoryTags() {
        this.loadCategoryTags();
        this.$message.success('分类标签已刷新');
      },
      refreshAvailableTags() {
        this.loadAvailableTags();
        this.$message.success('可用标签已刷新');
      },
      saveTagChanges() {
        this.loadCategoryTags();
        this.tagDialogVisible = false;
        this.$message.success('标签管理已保存');
      },
      isTagAdded(tagId) {
        return this.currentCategoryTags.some(tag => tag.id === tagId);
      }
    },
    watch: {
      currentTagType(val) {
        if (val) {
          this.loadCategoryTags();
          this.loadAvailableTags();
        }
      }
    }
  };
</script>

<style lang="scss" scoped>
.tag-management {
  padding: 20px 0;
}

.category-info {
  margin-bottom: 20px;
}

.tag-management-content {
  display: flex;
  gap: 20px;
}

.current-tags-section {
  flex: 1;
}

.operations-section {
  flex: 1;
}

.tag-section {
  margin-bottom: 30px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.section-header h4 {
  margin: 0;
  color: #333;
  font-weight: 500;
}

.operation-group {
  margin-bottom: 25px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.operation-group h4 {
  margin-bottom: 15px;
  color: #333;
  font-weight: 500;
}

.current-tags, .available-tags {
  min-height: 120px;
  padding: 15px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fff;
  max-height: 300px;
  overflow-y: auto;
}

.add-tag-form {
  .el-form-item {
    margin-bottom: 15px;
  }
}

.empty-tags {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  color: #909399;
}

.tag-count {
  font-size: 12px;
  opacity: 0.8;
  margin-left: 4px;
}

.tag-disabled {
  opacity: 0.5;
  cursor: not-allowed !important;
}

.tag-disabled:hover {
  opacity: 0.5;
}

.text-gray-400 {
  color: #c0c4cc;
}

.image-cell {
  display: flex;
  align-items: center;

  .menu-image {
    width: 32px;
    height: 32px;
    border-radius: 4px;
    border: 1px solid #e4e7ed;
  }

  .image-error {
    width: 32px;
    height: 32px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #999;
  }

  .image-placeholder {
    width: 32px;
    height: 32px;
    background: #f5f5f5;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;
    font-size: 10px;
    .el-icon {
      font-size: 12px;
      margin-bottom: 2px;
    }
  }
}

.icon-upload-container {
  padding: 20px 0;
}

.icon-upload-form {
  display: flex;
  align-items: center;
}

.icon-upload-controls {
  margin-left: 10px;
}

.icon-error-large {
  width: 60px;
  height: 60px;
  background: #f5f5f5;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #999;
}

.upload-dialog-content {
  .upload-tips {
    margin-bottom: 20px;
  }
  
  .upload-area {
    margin-bottom: 20px;
  }
  
  .upload-preview {
    h4 {
      margin-bottom: 12px;
      color: #606266;
    }
    
    .preview-list {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 12px;
      
      .preview-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border: 1px solid #e4e7ed;
        border-radius: 4px;
        background: #fafafa;
        
        .preview-image {
          margin-right: 12px;
          
          .menu-preview-image {
            width: 32px;
            height: 32px;
            border-radius: 4px;
            border: 1px solid #e4e7ed;
          }
        }
        
        .preview-info {
          flex: 1;
          
          .preview-name {
            font-weight: 500;
            margin-bottom: 4px;
            word-break: break-all;
          }
          
          .preview-status {
            margin-top: 4px;
          }
        }
      }
    }
  }
}

.dialog-footer {
  text-align: right;
}
</style> 