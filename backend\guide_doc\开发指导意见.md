# 后端开发指导意见

## 开发环境配置

### 1. 环境要求
- **JDK**: 17+
- **IDE**: IntelliJ IDEA 2023.1+ 或 Eclipse 2023-03+
- **Maven**: 3.6+
- **MySQL**: 8.0+
- **Redis**: 6.0+ (可选)

### 2. IDE配置
```xml
<!-- 推荐IDEA设置 -->
- 编码: UTF-8
- 换行符: LF (Unix)
- 缩进: 4个空格
- 自动导入: 开启
- 代码格式化: 使用阿里巴巴Java开发手册
```

### 3. Maven配置
```xml
<!-- settings.xml 配置阿里云镜像 -->
<mirrors>
    <mirror>
        <id>aliyun-repos</id>
        <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
        <mirrorOf>central</mirrorOf>
    </mirror>
</mirrors>
```

## 代码规范

### 1. 命名规范

#### 1.1 类命名
```java
// ✅ 正确示例
public class UserService {}
public class PostController {}
public class CategoryMapper {}

// ❌ 错误示例
public class userService {}
public class post_controller {}
```

#### 1.2 方法命名
```java
// ✅ 正确示例
public void getUserById() {}
public boolean savePost() {}
public List<Category> getCategoryList() {}

// ❌ 错误示例
public void get_user_by_id() {}
public boolean SavePost() {}
```

#### 1.3 变量命名
```java
// ✅ 正确示例
private String userName;
private List<Post> postList;
private static final String API_BASE_URL = "/api";

// ❌ 错误示例
private String user_name;
private List<Post> PostList;
private static final String apiBaseUrl = "/api";
```

#### 1.4 数据库命名
```sql
-- ✅ 正确示例
CREATE TABLE `urb_post` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `title` varchar(255) DEFAULT NULL,
  `create_time` datetime(6) DEFAULT NULL,
  `is_deleted` int DEFAULT '0'
);

-- ❌ 错误示例
CREATE TABLE `UrbPost` (
  `ID` bigint NOT NULL AUTO_INCREMENT,
  `Title` varchar(255) DEFAULT NULL,
  `createTime` datetime(6) DEFAULT NULL
);
```

### 2. 注释规范

#### 2.1 类注释
```java
/**
 * 帖子管理服务类
 * 
 * <AUTHOR>
 * @since 2024-01-01
 * @version 1.0.0
 */
@Service
public class PostService {
    // 类实现
}
```

#### 2.2 方法注释
```java
/**
 * 根据ID获取帖子详情
 * 
 * @param id 帖子ID
 * @return 帖子详情，如果不存在返回null
 * @throws IllegalArgumentException 当ID为null或负数时抛出
 */
public Post getPostById(Long id) {
    // 方法实现
}
```

#### 2.3 API接口注释
```java
/**
 * 创建帖子
 */
@PostMapping("/save")
@ApiOperationSupport(order = 1)
@Operation(summary = "创建帖子", description = "创建新的帖子信息")
public R<PostVO> save(@Valid @RequestBody Post post) {
    // 接口实现
}
```

### 3. 代码结构规范

#### 3.1 包结构
```
org.springblade.business.post/
├── controller/          # 控制器层
├── service/            # 服务层
│   └── impl/          # 服务实现
├── mapper/             # 数据访问层
├── entity/             # 实体类
├── vo/                 # 视图对象
├── dto/                # 数据传输对象
└── wrapper/            # 包装器
```

#### 3.2 分层职责
```java
// Controller层 - 只负责参数验证和结果返回
@RestController
public class PostController {
    
    @PostMapping("/save")
    public R<PostVO> save(@Valid @RequestBody PostDTO postDTO) {
        // 参数验证
        // 调用Service
        // 返回结果
    }
}

// Service层 - 业务逻辑处理
@Service
public class PostService {
    
    public PostVO savePost(PostDTO postDTO) {
        // 业务逻辑处理
        // 数据转换
        // 调用Mapper
    }
}

// Mapper层 - 数据访问
@Mapper
public interface PostMapper extends BaseMapper<Post> {
    
    List<Post> selectByCondition(@Param("condition") PostCondition condition);
}
```

## 数据库设计规范

### 1. 表设计原则

#### 1.1 基础字段
```sql
-- 所有业务表必须包含以下基础字段
CREATE TABLE `table_name` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  -- 业务字段
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='表说明';
```

#### 1.2 索引设计
```sql
-- 主键索引
PRIMARY KEY (`id`)

-- 唯一索引
UNIQUE KEY `uk_field_name` (`field_name`)

-- 普通索引
KEY `idx_field_name` (`field_name`)

-- 复合索引
KEY `idx_field1_field2` (`field1`, `field2`)

-- 外键索引
KEY `fk_parent_id` (`parent_id`)
```

### 2. 字段设计规范

#### 2.1 数据类型选择
```sql
-- 主键ID
`id` bigint NOT NULL AUTO_INCREMENT

-- 字符串类型
`name` varchar(50) DEFAULT NULL        -- 短字符串
`content` text                         -- 长文本
`description` varchar(500) DEFAULT NULL -- 中等长度字符串

-- 数值类型
`count` int DEFAULT '0'                -- 整数
`price` decimal(10,2) DEFAULT '0.00'   -- 金额
`status` tinyint DEFAULT '1'           -- 状态值

-- 时间类型
`create_time` datetime(6) DEFAULT NULL -- 创建时间
`update_time` datetime(6) DEFAULT NULL -- 更新时间
`expire_time` timestamp NULL           -- 过期时间

-- JSON类型
`config` json DEFAULT NULL             -- 配置信息
`tags` json DEFAULT NULL               -- 标签数组
```

#### 2.2 字段约束
```sql
-- 非空约束
`name` varchar(50) NOT NULL COMMENT '名称'

-- 默认值
`status` int DEFAULT '1' COMMENT '状态'

-- 检查约束
`age` int DEFAULT '0' CHECK (`age` >= 0 AND `age` <= 150)

-- 注释
`field_name` varchar(50) DEFAULT NULL COMMENT '字段说明'
```

## API设计规范

### 1. RESTful API设计

#### 1.1 URL设计
```java
// ✅ 正确示例
GET    /api/posts              // 获取帖子列表
GET    /api/posts/{id}         // 获取单个帖子
POST   /api/posts              // 创建帖子
PUT    /api/posts/{id}         // 更新帖子
DELETE /api/posts/{id}         // 删除帖子

// ❌ 错误示例
GET    /api/getPosts
POST   /api/createPost
PUT    /api/updatePost
DELETE /api/deletePost
```

#### 1.2 请求参数
```java
// 查询参数
GET /api/posts?page=1&size=10&title=关键词&status=1

// 路径参数
GET /api/posts/{id}

// 请求体
POST /api/posts
{
  "title": "帖子标题",
  "content": "帖子内容",
  "tags": ["标签1", "标签2"]
}
```

#### 1.3 响应格式
```java
// 成功响应
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "title": "帖子标题",
    "content": "帖子内容"
  },
  "timestamp": 1640995200000
}

// 错误响应
{
  "code": 400,
  "msg": "参数错误",
  "data": null,
  "timestamp": 1640995200000
}

// 分页响应
{
  "code": 200,
  "msg": "查询成功",
  "data": {
    "records": [...],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  },
  "timestamp": 1640995200000
}
```

### 2. 参数验证

#### 2.1 实体类验证
```java
@Data
public class PostDTO {
    
    @NotBlank(message = "标题不能为空")
    @Length(max = 100, message = "标题长度不能超过100字符")
    private String title;
    
    @NotBlank(message = "内容不能为空")
    @Length(max = 2000, message = "内容长度不能超过2000字符")
    private String content;
    
    @Size(max = 6, message = "图片数量不能超过6张")
    private List<String> images;
    
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String contactPhone;
}
```

#### 2.2 控制器验证
```java
@PostMapping("/save")
public R<PostVO> save(@Valid @RequestBody PostDTO postDTO) {
    // 参数会自动验证，验证失败会抛出异常
    return R.data(postService.savePost(postDTO));
}
```

### 3. 异常处理

#### 3.1 全局异常处理
```java
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public R<Void> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return R.fail(400, message);
    }
    
    @ExceptionHandler(BusinessException.class)
    public R<Void> handleBusinessException(BusinessException e) {
        return R.fail(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(Exception.class)
    public R<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return R.fail(500, "系统异常，请稍后重试");
    }
}
```

#### 3.2 自定义异常
```java
public class BusinessException extends RuntimeException {
    
    private final int code;
    
    public BusinessException(int code, String message) {
        super(message);
        this.code = code;
    }
    
    public int getCode() {
        return code;
    }
}
```

## 性能优化建议

### 1. 数据库优化

#### 1.1 查询优化
```java
// ✅ 正确示例 - 使用分页查询
public IPage<Post> getPostList(PostQuery query) {
    Page<Post> page = new Page<>(query.getCurrent(), query.getSize());
    return postMapper.selectPage(page, 
        new LambdaQueryWrapper<Post>()
            .like(StringUtils.isNotBlank(query.getTitle()), Post::getTitle, query.getTitle())
            .eq(query.getStatus() != null, Post::getStatus, query.getStatus())
            .orderByDesc(Post::getCreateTime)
    );
}

// ❌ 错误示例 - 全表查询
public List<Post> getAllPosts() {
    return postMapper.selectList(null); // 可能返回大量数据
}
```

#### 1.2 索引优化
```sql
-- 为常用查询字段创建索引
CREATE INDEX idx_post_title ON urb_post(title);
CREATE INDEX idx_post_status ON urb_post(status);
CREATE INDEX idx_post_create_time ON urb_post(create_time);

-- 复合索引
CREATE INDEX idx_post_status_time ON urb_post(status, create_time);
```

#### 1.3 连接池配置
```yaml
spring:
  datasource:
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
```

### 2. 缓存策略

#### 2.1 Redis缓存
```java
@Service
public class CategoryService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public List<Category> getCategoryList() {
        String key = "category:list";
        
        // 先从缓存获取
        List<Category> categories = (List<Category>) redisTemplate.opsForValue().get(key);
        if (categories != null) {
            return categories;
        }
        
        // 缓存未命中，从数据库查询
        categories = categoryMapper.selectList(null);
        
        // 存入缓存，设置过期时间
        redisTemplate.opsForValue().set(key, categories, 30, TimeUnit.MINUTES);
        
        return categories;
    }
}
```

#### 2.2 本地缓存
```java
@Component
public class LocalCache {
    
    private final Map<String, Object> cache = new ConcurrentHashMap<>();
    
    public void put(String key, Object value, long expireSeconds) {
        cache.put(key, new CacheItem(value, System.currentTimeMillis() + expireSeconds * 1000));
    }
    
    public Object get(String key) {
        CacheItem item = (CacheItem) cache.get(key);
        if (item != null && item.getExpireTime() > System.currentTimeMillis()) {
            return item.getValue();
        }
        cache.remove(key);
        return null;
    }
}
```

### 3. 异步处理

#### 3.1 异步方法
```java
@Service
public class PostService {
    
    @Async
    public void processPostAsync(Post post) {
        // 异步处理耗时操作
        // 如：发送通知、更新统计等
    }
    
    @Async
    public CompletableFuture<String> processPostWithResult(Post post) {
        // 异步处理并返回结果
        String result = doProcess(post);
        return CompletableFuture.completedFuture(result);
    }
}
```

#### 3.2 异步配置
```java
@Configuration
@EnableAsync
public class AsyncConfig implements AsyncConfigurer {
    
    @Override
    public Executor getAsyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(500);
        executor.setThreadNamePrefix("Async-");
        executor.initialize();
        return executor;
    }
    
    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new SimpleAsyncUncaughtExceptionHandler();
    }
}
```

## 安全规范

### 1. 认证授权

#### 1.1 JWT Token
```java
@Component
public class JwtTokenProvider {
    
    @Value("${blade.auth.sign-key}")
    private String signKey;
    
    public String generateToken(String userId) {
        return Jwts.builder()
            .setSubject(userId)
            .setIssuedAt(new Date())
            .setExpiration(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000))
            .signWith(SignatureAlgorithm.HS512, signKey)
            .compact();
    }
    
    public String getUserIdFromToken(String token) {
        return Jwts.parser()
            .setSigningKey(signKey)
            .parseClaimsJws(token)
            .getBody()
            .getSubject();
    }
}
```

#### 1.2 权限验证
```java
@RestController
public class PostController {
    
    @PostMapping("/save")
    @PreAuthorize("hasRole('USER')")
    public R<PostVO> save(@Valid @RequestBody PostDTO postDTO) {
        // 只有USER角色的用户才能访问
        return R.data(postService.savePost(postDTO));
    }
    
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('ADMIN') or @postService.isOwner(#id)")
    public R<Void> delete(@PathVariable Long id) {
        // 只有ADMIN角色或帖子所有者才能删除
        postService.deleteById(id);
        return R.success();
    }
}
```

### 2. 数据安全

#### 2.1 SQL注入防护
```java
// ✅ 正确示例 - 使用MyBatis Plus
public List<Post> getPostsByTitle(String title) {
    return postMapper.selectList(
        new LambdaQueryWrapper<Post>()
            .like(Post::getTitle, title)
    );
}

// ❌ 错误示例 - 直接拼接SQL
public List<Post> getPostsByTitle(String title) {
    String sql = "SELECT * FROM urb_post WHERE title LIKE '%" + title + "%'";
    // 容易受到SQL注入攻击
}
```

#### 2.2 XSS防护
```java
@Component
public class XssFilter implements Filter {
    
    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain) {
        XssHttpServletRequestWrapper xssRequest = new XssHttpServletRequestWrapper(
            (HttpServletRequest) request);
        chain.doFilter(xssRequest, response);
    }
}

public class XssHttpServletRequestWrapper extends HttpServletRequestWrapper {
    
    public XssHttpServletRequestWrapper(HttpServletRequest request) {
        super(request);
    }
    
    @Override
    public String getParameter(String parameter) {
        String value = super.getParameter(parameter);
        return cleanXSS(value);
    }
    
    private String cleanXSS(String value) {
        if (value == null) {
            return null;
        }
        // 过滤XSS攻击字符
        return value.replaceAll("<script[^>]*>.*?</script>", "")
                   .replaceAll("<[^>]*>", "");
    }
}
```

### 3. 接口安全

#### 3.1 频率限制
```java
@RestController
public class PostController {
    
    @PostMapping("/save")
    @RateLimit(value = 10, time = 60) // 60秒内最多10次请求
    public R<PostVO> save(@Valid @RequestBody PostDTO postDTO) {
        return R.data(postService.savePost(postDTO));
    }
}

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RateLimit {
    int value() default 10;
    int time() default 60;
}
```

#### 3.2 参数验证
```java
@RestController
public class PostController {
    
    @PostMapping("/save")
    public R<PostVO> save(@Valid @RequestBody PostDTO postDTO) {
        // 自动进行参数验证
        return R.data(postService.savePost(postDTO));
    }
    
    @GetMapping("/list")
    public R<IPage<PostVO>> list(
        @Min(value = 1, message = "页码必须大于0") @RequestParam(defaultValue = "1") Integer current,
        @Min(value = 1, message = "每页大小必须大于0") @Max(value = 100, message = "每页大小不能超过100") @RequestParam(defaultValue = "10") Integer size
    ) {
        return R.data(postService.getPostList(current, size));
    }
}
```

## 测试规范

### 1. 单元测试

#### 1.1 Service层测试
```java
@ExtendWith(MockitoExtension.class)
class PostServiceTest {
    
    @Mock
    private PostMapper postMapper;
    
    @InjectMocks
    private PostServiceImpl postService;
    
    @Test
    void testSavePost() {
        // Given
        PostDTO postDTO = new PostDTO();
        postDTO.setTitle("测试标题");
        postDTO.setContent("测试内容");
        
        Post post = new Post();
        post.setId(1L);
        post.setTitle("测试标题");
        
        when(postMapper.insert(any(Post.class))).thenReturn(1);
        when(postMapper.selectById(1L)).thenReturn(post);
        
        // When
        PostVO result = postService.savePost(postDTO);
        
        // Then
        assertNotNull(result);
        assertEquals("测试标题", result.getTitle());
        verify(postMapper).insert(any(Post.class));
    }
}
```

#### 1.2 Controller层测试
```java
@WebMvcTest(PostController.class)
class PostControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @MockBean
    private PostService postService;
    
    @Test
    void testSavePost() throws Exception {
        // Given
        PostDTO postDTO = new PostDTO();
        postDTO.setTitle("测试标题");
        postDTO.setContent("测试内容");
        
        PostVO postVO = new PostVO();
        postVO.setId(1L);
        postVO.setTitle("测试标题");
        
        when(postService.savePost(any(PostDTO.class))).thenReturn(postVO);
        
        // When & Then
        mockMvc.perform(post("/api/posts")
                .contentType(MediaType.APPLICATION_JSON)
                .content(new ObjectMapper().writeValueAsString(postDTO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data.title").value("测试标题"));
    }
}
```

### 2. 集成测试

#### 2.1 数据库集成测试
```java
@SpringBootTest
@Transactional
@Rollback
class PostServiceIntegrationTest {
    
    @Autowired
    private PostService postService;
    
    @Autowired
    private PostMapper postMapper;
    
    @Test
    void testSaveAndQueryPost() {
        // Given
        PostDTO postDTO = new PostDTO();
        postDTO.setTitle("集成测试标题");
        postDTO.setContent("集成测试内容");
        
        // When
        PostVO savedPost = postService.savePost(postDTO);
        PostVO queriedPost = postService.getPostById(savedPost.getId());
        
        // Then
        assertNotNull(queriedPost);
        assertEquals("集成测试标题", queriedPost.getTitle());
    }
}
```

## 部署规范

### 1. 环境配置

#### 1.1 配置文件分离
```yaml
# application.yml - 公共配置
spring:
  profiles:
    active: dev

# application-dev.yml - 开发环境
spring:
  datasource:
    url: *****************************************

# application-prod.yml - 生产环境
spring:
  datasource:
    url: ****************************************
```

#### 1.2 敏感信息配置
```yaml
# 使用环境变量
spring:
  datasource:
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    
# 或使用配置中心
spring:
  cloud:
    config:
      uri: ${CONFIG_SERVER_URI:http://localhost:8888}
```

### 2. 日志配置

#### 2.1 Logback配置
```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 控制台输出 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 文件输出 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/application.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>logs/application.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>100MB</maxFileSize>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
        </encoder>
    </appender>
    
    <!-- 日志级别 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>
</configuration>
```

### 3. 监控配置

#### 3.1 健康检查
```java
@Component
public class DatabaseHealthIndicator implements HealthIndicator {
    
    @Autowired
    private DataSource dataSource;
    
    @Override
    public Health health() {
        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(1000)) {
                return Health.up().build();
            } else {
                return Health.down().withDetail("database", "Connection is invalid").build();
            }
        } catch (SQLException e) {
            return Health.down().withDetail("database", e.getMessage()).build();
        }
    }
}
```

#### 3.2 性能监控
```java
@Aspect
@Component
public class PerformanceMonitorAspect {
    
    private static final Logger log = LoggerFactory.getLogger(PerformanceMonitorAspect.class);
    
    @Around("@annotation(org.springframework.web.bind.annotation.RequestMapping)")
    public Object monitorPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        
        try {
            return joinPoint.proceed();
        } finally {
            long endTime = System.currentTimeMillis();
            long duration = endTime - startTime;
            
            if (duration > 1000) { // 超过1秒记录警告
                log.warn("Method {} took {}ms to execute", 
                    joinPoint.getSignature().getName(), duration);
            }
        }
    }
}
```

## 最佳实践总结

### 1. 开发流程
1. **需求分析** → 明确功能需求和技术要求
2. **数据库设计** → 设计表结构和关系
3. **接口设计** → 定义API接口和参数
4. **编码实现** → 按照规范编写代码
5. **单元测试** → 编写测试用例
6. **集成测试** → 验证功能完整性
7. **代码审查** → 团队代码审查
8. **部署上线** → 生产环境部署

### 2. 代码质量
- 遵循SOLID原则
- 保持代码简洁可读
- 及时重构优化
- 编写完整的测试用例
- 定期进行代码审查

### 3. 性能优化
- 合理使用索引
- 实现缓存策略
- 使用异步处理
- 监控系统性能
- 定期性能调优

### 4. 安全防护
- 实现完善的认证授权
- 防止SQL注入和XSS攻击
- 实现接口频率限制
- 加密敏感数据
- 记录安全日志

### 5. 运维监控
- 配置完善的日志系统
- 实现健康检查
- 监控系统性能
- 设置告警机制
- 制定应急预案 