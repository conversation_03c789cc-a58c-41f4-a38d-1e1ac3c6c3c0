<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.user.mapper.CategoryTagMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="org.springblade.business.post.entity.CategoryTag">
        <id column="id" property="id"/>
        <result column="category_id" property="categoryId"/>
        <result column="tag_id" property="tagId"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>

    <!-- 分类标签关联VO映射结果 -->
    <resultMap id="CategoryTagVOResultMap" type="org.springblade.business.post.vo.CategoryTagVO" extends="BaseResultMap">
        <result column="category_name" property="categoryName"/>
        <result column="tag_name" property="tagName"/>
        <result column="tag_color" property="tagColor"/>
        <result column="tag_use_count" property="tagUseCount"/>
        <result column="tag_is_system" property="tagIsSystem"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_user_name" property="updateUserName"/>
    </resultMap>

    <!-- 标签映射结果 -->
    <resultMap id="TagResultMap" type="org.springblade.business.post.entity.Tag">
        <id column="id" property="id"/>
        <result column="tag_name" property="tagName"/>
        <result column="category_id" property="categoryId"/>
        <result column="color" property="color"/>
        <result column="sort" property="sort"/>
        <result column="enabled" property="enabled"/>
        <result column="use_count" property="useCount"/>
        <result column="is_system" property="isSystem"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, category_id, tag_id, sort_order
    </sql>

    <!-- 分类标签关联VO查询结果列 -->
    <sql id="CategoryTagVO_Column_List">
        ct.id, ct.category_id, ct.tag_id, ct.sort_order,
        c.name as category_name,
        t.tag_name, t.color as tag_color, t.use_count as tag_use_count, t.is_system as tag_is_system,
        cu.name as create_user_name,
        uu.name as update_user_name
    </sql>

    <!-- 标签查询结果列 -->
    <sql id="Tag_Column_List">
        t.id, t.tag_name, t.category_id, t.color, t.sort, t.enabled, t.use_count, t.is_system,
        t.create_time, t.update_time, t.sort_order,t.status
    </sql>

    <!-- 分页查询分类标签关联 -->
    <select id="selectCategoryTagPage" resultMap="CategoryTagVOResultMap">
        SELECT
        <include refid="CategoryTagVO_Column_List"/>
        FROM urb_category_tag ct
        LEFT JOIN urb_category c ON ct.category_id = c.id
        LEFT JOIN urb_tag t ON ct.tag_id = t.id
        LEFT JOIN blade_user cu ON ct.create_user = cu.id
        LEFT JOIN blade_user uu ON ct.update_user = uu.id
        <where>
            <if test="categoryTag.categoryId != null">
                AND ct.category_id = #{categoryTag.categoryId}
            </if>
            <if test="categoryTag.tagId != null">
                AND ct.tag_id = #{categoryTag.tagId}
            </if>
            <if test="categoryTag.categoryName != null and categoryTag.categoryName != ''">
                AND c.name LIKE CONCAT('%', #{categoryTag.categoryName}, '%')
            </if>
            <if test="categoryTag.tagName != null and categoryTag.tagName != ''">
                AND t.tag_name LIKE CONCAT('%', #{categoryTag.tagName}, '%')
            </if>
        </where>
        ORDER BY ct.sort_order ASC
    </select>

    <!-- 根据分类ID查询标签信息（包含标签详情） -->
    <select id="selectTagsByCategoryId" resultMap="TagResultMap">
        SELECT
        <include refid="Tag_Column_List"/>
        FROM urb_tag t
        INNER JOIN urb_category_tag ct ON t.id = ct.tag_id
        WHERE ct.category_id = #{categoryId}
        AND t.enabled = 1
        ORDER BY t.sort_order ASC, t.sort ASC, t.use_count DESC, t.id ASC
    </select>

    <!-- 根据分类ID查询标签数量 -->
    <select id="selectTagCountByCategoryId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM urb_category_tag ct
        INNER JOIN urb_tag t ON ct.tag_id = t.id
        WHERE ct.category_id = #{categoryId}
        AND t.enabled = 1
    </select>

    <!-- 检查标签是否属于分类 -->
    <select id="checkTagBelongToCategory" resultType="java.lang.Boolean">
        SELECT COUNT(*) > 0
        FROM urb_category_tag
        WHERE category_id = #{categoryId}
        AND tag_id = #{tagId}
    </select>

    <!-- 获取分类下的所有标签ID -->
    <select id="selectTagIdsByCategoryId" resultType="java.lang.Long">
        SELECT tag_id
        FROM urb_category_tag
        WHERE category_id = #{categoryId}
        ORDER BY sort_order ASC
    </select>


    <!-- 获取热门标签（按使用次数排序） -->
    <select id="selectHotTags" resultMap="TagResultMap">
        SELECT
        <include refid="Tag_Column_List"/>
        FROM urb_tag t
        INNER JOIN urb_category_tag ct ON t.id = ct.tag_id
        WHERE t.enabled = 1
        ORDER BY t.use_count DESC, ct.sort_order ASC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 根据分类ID获取标签统计信息 -->
    <select id="selectTagStatsByCategory" resultMap="CategoryTagVOResultMap">
        SELECT
        ct.category_id,
        c.name as category_name,
        COUNT(ct.tag_id) as tag_count,
        SUM(t.use_count) as total_use_count,
        MAX(t.use_count) as max_use_count
        FROM urb_category_tag ct
        LEFT JOIN urb_category c ON ct.category_id = c.id
        LEFT JOIN urb_tag t ON ct.tag_id = t.id
        WHERE ct.category_id = #{categoryId}
        AND t.enabled = 1
        GROUP BY ct.category_id, c.name
    </select>

    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO urb_category_tag (category_id, tag_id, sort_order)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.categoryId}, #{item.tagId}, #{item.sortOrder}, NOW(), NOW())
        </foreach>
    </insert>

    <!-- 根据分类ID删除所有关联 -->
    <delete id="deleteByCategoryId" parameterType="java.lang.Long">
        DELETE FROM urb_category_tag WHERE category_id = #{categoryId}
    </delete>

    <!-- 根据标签ID删除所有关联 -->
    <delete id="deleteByTagId" parameterType="java.lang.Long">
        DELETE FROM urb_category_tag WHERE tag_id = #{tagId}
    </delete>

    <!-- 更新标签排序 -->
    <update id="updateTagSort">
        UPDATE urb_category_tag
        SET sort_order = #{sortOrder},
            update_time = NOW()
        WHERE category_id = #{categoryId}
        AND tag_id = #{tagId}
    </update>

    <!-- 批量更新标签排序 -->
    <update id="batchUpdateTagSort" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE urb_category_tag
            SET sort_order = #{item.sortOrder},
                update_time = NOW()
            WHERE category_id = #{item.categoryId}
            AND tag_id = #{item.tagId}
        </foreach>
    </update>

</mapper>
