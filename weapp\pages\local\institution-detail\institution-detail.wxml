<!--机构详情页面-->
<view class="institution-detail-container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-text">{{error}}</text>
    <button class="retry-btn" bindtap="onRetry">重试</button>
  </view>

  <!-- 机构详情内容 -->
  <view wx:else class="content-container">
    <!-- 机构头部信息 -->
    <view class="institution-header">
      <view class="institution-logo">
        <image
          src="{{institution.logo || '/images/default-institution.png'}}"
          mode="aspectFill"
          class="logo-image"
        />
      </view>

      <view class="institution-info">
        <view class="institution-name">{{institution.name}}</view>
        <view class="institution-rating">
          <view class="stars">
            <text wx:for="{{[1,2,3,4,5]}}" wx:key="*this"
                  class="star {{item <= institution.rating ? 'active' : ''}}">★</text>
          </view>
          <text class="rating-text">{{institution.rating}}</text>
          <text class="review-count">({{institution.reviewCount}}条评价)</text>
        </view>
        <view class="institution-address">{{institution.address}}</view>
      </view>

      <view class="action-buttons">
        <button
          class="favorite-btn {{institution.isFavorite ? 'active' : ''}}"
          bindtap="onToggleFavorite"
          disabled="{{favoriteLoading}}"
        >
          {{institution.isFavorite ? '已收藏' : '收藏'}}
        </button>
      </view>
    </view>

    <!-- Tab导航 -->
    <view class="tab-nav">
      <view
        class="tab-item {{currentTab === 'info' ? 'active' : ''}}"
        data-tab="info"
        bindtap="onTabChange"
      >
        机构信息
      </view>
      <view
        class="tab-item {{currentTab === 'service' ? 'active' : ''}}"
        data-tab="service"
        bindtap="onTabChange"
      >
        服务项目
      </view>
      <view
        class="tab-item {{currentTab === 'review' ? 'active' : ''}}"
        data-tab="review"
        bindtap="onTabChange"
      >
        用户评价
      </view>
    </view>

    <!-- Tab内容 -->
    <view class="tab-content">
      <!-- 机构信息 -->
      <view wx:if="{{currentTab === 'info'}}" class="info-content">
        <view class="info-section">
          <view class="section-title">机构介绍</view>
          <view class="section-content">
            <text class="description">{{institution.description || '暂无介绍'}}</text>
          </view>
        </view>

        <view class="info-section">
          <view class="section-title">营业时间</view>
          <view class="section-content">
            <text>{{institution.businessHours || '营业时间未知'}}</text>
          </view>
        </view>

        <view class="info-section" wx:if="{{institution.tags && institution.tags.length > 0}}">
          <view class="section-title">标签</view>
          <view class="section-content">
            <view class="tags">
              <text wx:for="{{institution.tags}}" wx:key="*this" class="tag">{{item}}</text>
            </view>
          </view>
        </view>

        <view class="info-section" wx:if="{{institution.images && institution.images.length > 0}}">
          <view class="section-title">机构图片</view>
          <view class="section-content">
            <view class="image-gallery">
              <image
                wx:for="{{institution.images}}"
                wx:key="*this"
                src="{{item}}"
                mode="aspectFill"
                class="gallery-image"
                bindtap="onPreviewImage"
                data-url="{{item}}"
                data-urls="{{institution.images}}"
              />
            </view>
          </view>
        </view>
      </view>

      <!-- 服务项目 -->
      <view wx:elif="{{currentTab === 'service'}}" class="service-content">
        <view wx:if="{{serviceList.length === 0}}" class="empty-state">
          <text>暂无服务项目</text>
        </view>
        <view wx:else>
          <view
            wx:for="{{serviceList}}"
            wx:key="id"
            class="service-item {{!item.isAvailable ? 'disabled' : ''}}"
          >
            <view class="service-info">
              <view class="service-name">{{item.name}}</view>
              <view class="service-description">{{item.description}}</view>
              <view class="service-meta">
                <text class="service-price">¥{{item.price}}</text>
                <text class="service-duration">{{item.duration}}</text>
              </view>
            </view>
            <button
              class="book-btn"
              data-service="{{item}}"
              bindtap="onBookService"
              disabled="{{!item.isAvailable || bookingLoading}}"
            >
              {{item.isAvailable ? '预约' : '暂停服务'}}
            </button>
          </view>
        </view>
      </view>

      <!-- 用户评价 -->
      <view wx:elif="{{currentTab === 'review'}}" class="review-content">
        <view wx:if="{{reviewList.length === 0}}" class="empty-state">
          <text>暂无用户评价</text>
        </view>
        <view wx:else>
          <view wx:for="{{reviewList}}" wx:key="id" class="review-item">
            <view class="review-header">
              <image src="{{item.userAvatar || '/images/default-avatar.png'}}" class="user-avatar" />
              <view class="user-info">
                <view class="user-name">{{item.userName}}</view>
                <view class="review-rating">
                  <text wx:for="{{[1,2,3,4,5]}}" wx:key="*this"
                        class="star {{item <= item.rating ? 'active' : ''}}">★</text>
                </view>
              </view>
              <view class="review-time">{{item.createTime}}</view>
            </view>
            <view class="review-content-text">{{item.content}}</view>
            <view wx:if="{{item.images && item.images.length > 0}}" class="review-images">
              <image
                wx:for="{{item.images}}"
                wx:key="*this"
                src="{{item}}"
                mode="aspectFill"
                class="review-image"
              />
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 底部操作栏 -->
  <view wx:if="{{!loading && !error}}" class="bottom-actions">
    <button class="action-btn" bindtap="onCallPhone" wx:if="{{institution.phone}}">
      <text class="btn-icon">📞</text>
      <text class="btn-text">电话</text>
    </button>
    <button class="action-btn" bindtap="onViewLocation" wx:if="{{institution.latitude && institution.longitude}}">
      <text class="btn-icon">📍</text>
      <text class="btn-text">位置</text>
    </button>
    <button class="action-btn primary" open-type="share">
      <text class="btn-icon">📤</text>
      <text class="btn-text">分享</text>
    </button>
  </view>
</view>