<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.points.mapper.PointsRecordMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="pointsRecordResultMap" type="org.springblade.business.points.entity.PointsRecord">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="user_id" property="userId"/>
        <result column="points" property="points"/>
        <result column="before_points" property="beforePoints"/>
        <result column="after_points" property="afterPoints"/>
        <result column="type" property="type"/>
        <result column="type_name" property="typeName"/>
        <result column="business_id" property="businessId"/>
        <result column="business_type" property="businessType"/>
        <result column="description" property="description"/>
        <result column="remark" property="remark"/>
        <result column="operate_time" property="operateTime"/>
        <result column="operator" property="operator"/>
    </resultMap>


    <select id="selectPointsRecordPage" resultMap="pointsRecordResultMap">
        select * from urb_points_record where is_deleted = 0
    </select>

</mapper>
