# 分类管理文件上传功能说明

## 功能概述

分类管理模块已集成文件上传功能，支持为分类上传自定义图标。该功能基于 `FileUploadComponent` 组件实现，提供了完整的文件上传、预览和管理功能。

## 功能特性

### 1. 图标上传
- **支持格式**: JPG、PNG、GIF等图片格式
- **文件大小**: 最大2MB
- **建议尺寸**: 32x32px（最佳显示效果）
- **上传来源**: 管理后台（admin）
- **业务类型**: category-icon

### 2. 上传方式
- **工具栏上传**: 通过"上传图标"按钮批量上传
- **表单上传**: 在新增/编辑分类时直接上传
- **拖拽上传**: 支持拖拽文件到上传区域

### 3. 预览功能
- **缩略图预览**: 在列表中显示32x32px缩略图
- **大图预览**: 点击图标可查看原图
- **错误处理**: 图标加载失败时显示占位符

## 使用方法

### 1. 工具栏上传图标
1. 在分类列表中选择一个或多个分类
2. 点击工具栏中的"上传图标"按钮
3. 在弹出的对话框中选择图标文件
4. 点击"确定"完成上传

### 2. 表单中上传图标
1. 点击"新增"或"编辑"按钮
2. 在"分类图标"字段中点击"选择图标"
3. 选择图标文件并上传
4. 可以预览已上传的图标
5. 点击"删除图标"可移除已上传的图标

## 技术实现

### 1. 组件依赖
```javascript
import FileUploadComponent from '@/components/FileUpload/index.vue'
import { Picture, Upload, Delete } from '@element-plus/icons-vue'
```

### 2. API接口
```javascript
// 文件上传接口
POST /blade-system/file-upload/upload
Content-Type: multipart/form-data

// 参数
{
  file: File,                    // 上传的文件
  uploadSource: 'admin',         // 上传来源
  businessType: 'category-icon', // 业务类型
  businessId: 'category_id'      // 业务ID（可选）
}
```

## 配置说明

### 1. 上传配置
```javascript
upload: {
  action: '/blade-system/file-upload/upload',
  headers: {
    'Authorization': `Bearer ${localStorage.getItem('blade-auth')}`
  },
  data: {
    uploadSource: 'admin',
    businessType: 'category-icon'
  },
  accept: 'image/*',
  limit: 1,
  tip: '支持jpg/png/gif格式，建议尺寸32x32px，文件大小不超过2MB'
}
```

## 最佳实践

### 1. 图标设计
- 使用PNG格式，支持透明背景
- 建议尺寸32x32px，确保清晰度
- 保持简洁的设计风格

### 2. 性能优化
- 图标文件大小控制在100KB以内
- 使用CDN加速图标加载

## 注意事项

1. **文件安全**: 上传的文件会进行安全检查
2. **存储管理**: 定期清理未使用的图标文件
3. **权限控制**: 确保只有授权用户才能上传图标

## 扩展功能

### 1. 图标管理
- 图标库管理
- 图标分类管理
- 图标使用统计

### 2. 高级功能
- 图标裁剪和编辑
- 图标模板管理
- 批量图标处理

## 故障排除

### 1. 上传失败
- 检查网络连接
- 验证文件格式和大小
- 确认用户权限
- 查看服务器日志

### 2. 显示异常
- 检查图标URL是否有效
- 验证CDN配置
- 确认浏览器兼容性

### 3. 性能问题
- 优化图标文件大小
- 检查CDN配置
- 监控服务器负载 