<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   
                   icon="el-icon-delete"
                   plain
                   v-if="permission.painpoint_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   
                   icon="el-icon-check"
                   plain
                   @click="handleBatchAudit">批量审核
        </el-button>
      </template>
      <template #content="{ row }">
        <el-button type="text" @click="viewContent(row)">
          {{ row.content ? (row.content.length > 50 ? row.content.substring(0, 50) + '...' : row.content) : '查看内容' }}
        </el-button>
      </template>
      <template #image="{ row }">
        <el-image 
          v-if="row.image && getImageList(row.image).length > 0"
          :src="getImageList(row.image)[0]"
          style="width: 50px; height: 50px;"
          :preview-src-list="getImageList(row.image)">
        </el-image>
        <span v-else style="color: #999;">无图片</span>
      </template>
      <template #auditStatus="{ row }">
        <el-tag :type="getAuditStatusType(row.auditStatus)">
          {{ getAuditStatusText(row.auditStatus) }}
        </el-tag>
      </template>
      <template #menu="{ row }">
        <el-button v-if="row.auditStatus === '0'" 
                   type="success" 
                   size="small"
                   @click="handleAudit(row, '1')">
          通过
        </el-button>
        <el-button v-if="row.auditStatus === '0'" 
                   type="danger" 
                   size="small"
                   @click="handleAudit(row, '2')">
          拒绝
        </el-button>
      </template>
    </avue-crud>

    <!-- 反馈详情对话框 -->
    <el-dialog v-model="contentDialogVisible" title="反馈详情" width="600px">
      <div v-if="selectedPainpoint">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="用户ID">{{ selectedPainpoint.createUser }}</el-descriptions-item>
          <el-descriptions-item label="用户昵称">{{ selectedPainpoint.nickname }}</el-descriptions-item>
          <el-descriptions-item label="反馈时间">{{ selectedPainpoint.createTime }}</el-descriptions-item>
          <el-descriptions-item label="联系方式">{{ selectedPainpoint.contactInfo }}</el-descriptions-item>
          <el-descriptions-item label="审核状态">
            <el-tag :type="getAuditStatusType(selectedPainpoint.auditStatus)">
              {{ getAuditStatusText(selectedPainpoint.auditStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="审核结果" v-if="selectedPainpoint.auditResult">
            {{ selectedPainpoint.auditResult }}
          </el-descriptions-item>
        </el-descriptions>
        <el-divider />
        <div>
          <h4>反馈内容：</h4>
          <p style="white-space: pre-wrap;">{{ selectedPainpoint.content }}</p>
        </div>
        <div v-if="selectedPainpoint.image && getImageList(selectedPainpoint.image).length > 0">
          <h4>图片：</h4>
          <el-image 
            v-for="(img, index) in getImageList(selectedPainpoint.image)" 
            :key="index"
            :src="img"
            style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px;"
            :preview-src-list="getImageList(selectedPainpoint.image)">
          </el-image>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog v-model="auditDialogVisible" title="反馈审核" width="500px">
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input v-model="auditForm.auditResult" 
                    type="textarea" 
                    :rows="4" 
                    placeholder="请输入审核备注（可选）">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit">确定</el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getPage, getDetail, add, update, remove, auditPainpoint} from "@/api/ad/painpoint";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        contentDialogVisible: false,
        selectedPainpoint: null,
        auditDialogVisible: false,
        auditForm: {
          id: null,
          auditStatus: '1',
          auditResult: ''
        },
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          selection: true,
          editBtn: false,
          menu: true,
          column: [
            {
              label: "用户ID",
              prop: "createUser",
              width: 200,
              search: true,
            },
            {
              label: "用户昵称",
              prop: "nickname",
              width: 100,
              search: true,
            },
            {
              label: "反馈内容",
              prop: "content",
              type: "textarea",
              overHidden: true,
              span: 24,
              slot: true,
              rules: [{
                required: true,
                message: "请输入反馈内容",
                trigger: "blur"
              }]
            },
            {
              label: "图片",
              prop: "image",
              slot: true,
              search: false,
              rules: [{
                required: true,
                message: "请输入图片路径或链接",
                trigger: "blur"
              }]
            },
            {
              label: "审核状态",
              prop: "auditStatus",
              type: "select",
              dicData: [
                { label: '待审核', value: '0' },
                { label: '已通过', value: '1' },
                { label: '已拒绝', value: '2' }
              ],
              search: true,
              slot: true
            },
            {
              label: "反馈时间",
              prop: "createTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              search: true,
              rules: [{
                required: true,
                message: "请输入反馈时间",
                trigger: "blur"
              }]
            },
            {
              label: "联系方式",
              prop: "contactInfo",
              search: true,
              rules: [{
                required: true,
                message: "请输入联系方式",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.painpoint_add, false),
          viewBtn: this.validData(this.permission.painpoint_view, false),
          delBtn: this.validData(this.permission.painpoint_delete, false),
          editBtn: this.validData(this.permission.painpoint_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      getImageList(image) {
        if (!image) return [];
        if (Array.isArray(image)) return image;
        return image.split(',').filter(img => img.trim() !== '');
      },
      getAuditStatusType(status) {
        const statusMap = {
          '0': 'warning',
          '1': 'success',
          '2': 'danger'
        };
        return statusMap[status] || 'info';
      },
      getAuditStatusText(status) {
        const statusMap = {
          '0': '待审核',
          '1': '已通过',
          '2': '已拒绝'
        };
        return statusMap[status] || '未知';
      },
      viewContent(row) {
        this.selectedPainpoint = row;
        this.contentDialogVisible = true;
      },
      handleAudit(row, status) {
        this.auditForm.id = row.id;
        this.auditForm.auditStatus = status;
        this.auditForm.auditResult = '';
        this.auditDialogVisible = true;
      },
      handleBatchAudit() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.auditForm.id = this.ids;
        this.auditForm.auditStatus = '1';
        this.auditForm.auditResult = '';
        this.auditDialogVisible = true;
      },
      submitAudit() {
        // 判断是否为批量审核
        if (this.auditForm.id && this.auditForm.id.includes(',')) {
          // 批量审核 - painpoint没有批量接口，使用单个接口多次调用
          const ids = this.auditForm.id.split(',');
          const promises = ids.map(id => {
            return auditPainpoint({
              id: id,
              auditStatus: this.auditForm.auditStatus,
              auditResult: this.auditForm.auditResult
            });
          });
          
          Promise.all(promises).then(() => {
            this.auditDialogVisible = false;
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "批量审核操作成功!"
            });
          }).catch(error => {
            this.$message.error("批量审核操作失败");
          });
        } else {
          // 单个审核
          auditPainpoint(this.auditForm).then(() => {
            this.auditDialogVisible = false;
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "审核操作成功!"
            });
          }).catch(error => {
            this.$message.error("审核操作失败");
          });
        }
      },
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
