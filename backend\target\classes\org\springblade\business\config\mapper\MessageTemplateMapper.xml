<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.config.mapper.MessageTemplateMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="messageTemplateResultMap" type="org.springblade.business.config.entity.MessageTemplate">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="type_code" property="typeCode"/>
        <result column="type_name" property="typeName"/>
        <result column="title_template" property="titleTemplate"/>
        <result column="content_template" property="contentTemplate"/>
    </resultMap>


    <select id="selectMessageTemplatePage" resultMap="messageTemplateResultMap">
        select * from urb_message_template where is_deleted = 0
    </select>

</mapper>
