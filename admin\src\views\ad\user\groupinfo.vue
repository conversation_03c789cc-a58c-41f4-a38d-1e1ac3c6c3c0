<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.groupinfo_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/ad/groupinfo";
  import {mapGetters} from "vuex";
  import { getLazyTree } from "@/api/base/region";
  import { getList as getCategoryList } from '@/api/ad/groupcategory'

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "群名称",
              prop: "groupName",
              rules: [{
                required: true,
                message: "请输入群名称",
                trigger: "blur"
              }]
            },
            {
              label: "群类别",
              prop: "groupType",
              type: "select",
              dicData: [], // 动态加载
              props: {
                label: 'categoryName',
                value: 'id'
              },
              search: true,
              rules: [{
                required: true,
                message: "请选择群类别",
                trigger: "change"
              }]
            },
            {
              label: "群图片",
              prop: "groupImage",
              type: 'upload',
              action: '/blade-system/file-upload/upload',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('blade-auth')}`
              },
              data: {
                uploadSource: 'admin',
                businessType: 'group-image'
              },
              accept: 'image/*',
              limit: 1,
              tip: '支持jpg/png/gif格式，建议尺寸200x200px，文件大小不超过2MB',
              propsHttp: {
                url: 'accessUrl',
                name: 'fileName',
                res: 'data'
              },
            },
            {
              label: "微信链接",
              prop: "groupWeixin",
              type: 'upload',
              action: '/blade-system/file-upload/upload',
              headers: {
                'Authorization': `Bearer ${localStorage.getItem('blade-auth')}`
              },
              data: {
                uploadSource: 'admin',
                businessType: 'group-weixin'
              },
              accept: 'image/*',
              limit: 1,
              tip: '请上传微信群二维码图片，支持jpg/png/gif格式，建议尺寸200x200px，文件大小不超过2MB',
              propsHttp: {
                url: 'accessUrl',
                name: 'fileName',
                res: 'data'
              },
              rules: [{
                required: true,
                message: "请输入微信链接",
                trigger: "blur"
              }]
            },
            {
              label: "群描述信息",
              prop: "groupDesc",
              rules: [{
                required: true,
                message: "请输入群描述信息",
                trigger: "blur"
              }]
            },
            {
              label: "地区编号",
              prop: "regionCode",
              type: "cascader",
              dicData: [],
              props: {
                label: "title",
                value: "id",
                children: "children",
              },
              checkStrictly: true,
              showAllLevels: false,
              expandTrigger: 'click',
              emitPath: true,
              clearable: true,
              filterable: true,
              placeholder: "请选择地区",
              rules: [
                {
                  required: true,
                  message: "请选择地区",
                  trigger: "blur"
                }
              ]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.groupinfo_add, false),
          viewBtn: this.validData(this.permission.groupinfo_view, false),
          delBtn: this.validData(this.permission.groupinfo_delete, false),
          editBtn: this.validData(this.permission.groupinfo_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    mounted() {
      // 加载群分类下拉选项
      getCategoryList({}).then(res => {
        const categories = res.data.data || [];
        const col = this.option.column.find(c => c.prop === 'groupType');
        if (col) col.dicData = categories;
      });
    },
    methods: {
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getLazyTree().then(res => {
          this.option.column[5].dicData = res.data.data;
        });
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
