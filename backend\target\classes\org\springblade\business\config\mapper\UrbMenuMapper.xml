<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.config.mapper.UrbMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="urbMenuResultMap" type="org.springblade.business.config.entity.UrbMenu">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="name" property="name"/>
        <result column="image" property="image"/>
        <result column="sort_weight" property="sortWeight"/>
        <result column="color" property="color"/>
        <result column="url" property="url"/>
        <result column="category" property="category"/>
    </resultMap>


    <select id="selectUrbMenuPage" resultMap="urbMenuResultMap">
        select * from urb_menu
        where is_deleted = 0
        <if test="urbMenu != null and urbMenu.category != null">
            and category = #{urbMenu.category}
        </if>
        order by sort_weight asc
    </select>

</mapper>
