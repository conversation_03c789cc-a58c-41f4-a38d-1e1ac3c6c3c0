import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/feedback/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const getPage = (current, size, params) => {
  return request({
    url: '/blade-ad/feedback/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-ad/feedback/detail',
    method: 'get',
    params: {
      id
    }
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/feedback/remove',
    method: 'post',
    params: {
      ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-ad/feedback/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-ad/feedback/submit',
    method: 'post',
    data: row
  })
}

// 新增反馈标签
export const addFeedbackTag = (tag) => {
  return request({
    url: '/blade-ad/tag',
    method: 'post',
    data: tag
  })
}

// 根据分类Id查询反馈标签
export const getFeedbackTagsByCategory = (categoryId) => {
  return request({
    url: '/blade-ad/feedback/getTagsByCategory',
    method: 'get',
    params: {
      categoryId
    }
  })
}

// 获取热度top5反馈标签
export const getHotFeedbackTags = () => {
  return request({
    url: '/blade-ad/feedback/getHotTags',
    method: 'get'
  })
}

// 移除反馈标签
export const removeFeedbackTag = (categoryId, tagId) => {
  return request({
    url: '/blade-ad/feedback/removeTag',
    method: 'delete',
    params: {
      categoryId,
      tagId
    }
  })
}

// 获取所有反馈标签
export const getAllFeedbackTags = (current = 1, size = 1000, params = {}) => {
  return request({
    url: '/blade-ad/tag/page',
    method: 'get',
    params: {
      ...params,
      current,
      size
    }
  })
}

// 审核反馈
export const auditFeedback = (auditData) => {
  return request({
    url: '/blade-ad/feedback/audit',
    method: 'post',
    data: auditData
  })
}

// 批量审核反馈
export const batchAuditFeedback = (auditData) => {
  return request({
    url: '/blade-ad/feedback/batch-audit',
    method: 'post',
    data: auditData
  })
}

// 获取反馈审核统计
export const getFeedbackAuditStats = () => {
  return request({
    url: '/blade-ad/feedback/audit-stats',
    method: 'get'
  })
}

