<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.cooperation.mapper.CooperationLeadsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="cooperationLeadsResultMap" type="org.springblade.business.cooperation.entity.CooperationLeads">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="name" property="name"/>
        <result column="phone" property="phone"/>
        <result column="wechat" property="wechat"/>
        <result column="cooperation_type" property="cooperationType"/>
        <result column="cooperation_details" property="cooperationDetails"/>
        <result column="remarks" property="remarks"/>
    </resultMap>


    <select id="selectCooperationLeadsPage" resultMap="cooperationLeadsResultMap">
        select * from urb_cooperation_leads where is_deleted = 0
    </select>

</mapper>
