-- 文件分享表
CREATE TABLE `file_share` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `share_key` varchar(6) NOT NULL COMMENT '分享密钥（6位字符）',
  `share_type` varchar(10) NOT NULL COMMENT '分享类型（FILE-文件，TEXT-文本）',
  `file_name` varchar(255) DEFAULT NULL COMMENT '文件名称',
  `file_path` varchar(500) DEFAULT NULL COMMENT '文件路径',
  `file_size` bigint DEFAULT NULL COMMENT '文件大小（字节）',
  `file_type` varchar(100) DEFAULT NULL COMMENT '文件类型',
  `text_content` text COMMENT '文本内容',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `download_count` int DEFAULT '0' COMMENT '下载次数',
  `status` int DEFAULT '0' COMMENT '状态（0-正常，1-已过期，2-已删除）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建人',
  `update_user` bigint DEFAULT NULL COMMENT '更新人',
  `is_deleted` int DEFAULT '0' COMMENT '是否已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_share_key` (`share_key`),
  KEY `idx_share_type` (`share_type`),
  KEY `idx_expire_time` (`expire_time`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文件分享表';

-- 插入测试数据
INSERT INTO `file_share` (`share_key`, `share_type`, `text_content`, `expire_time`, `download_count`, `status`)
VALUES ('ABC123', 'TEXT', '这是一个测试文本内容，用于验证文件分享功能。', DATE_ADD(NOW(), INTERVAL 1 DAY), 0, 0);
