import request from '@/axios';

// 分页查询地区配置
export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/miniapp-region-config/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  });
};

// 配置地区开放状态
export const configOpen = (regionCode, isOpen) => {
  return request({
    url: '/blade-ad/miniapp-region-config/config-open',
    method: 'post',
    params: {
      regionCode,
      isOpen
    }
  });
};

// 根据层级查询地区配置
export const getListByLevel = (level) => {
  return request({
    url: '/blade-ad/miniapp-region-config/list-by-level',
    method: 'get',
    params: {
      level
    }
  });
};

// 根据父级编码查询子级地区配置
export const getListByParent = (parentCode) => {
  return request({
    url: '/blade-ad/miniapp-region-config/list-by-parent',
    method: 'get',
    params: {
      parentCode
    }
  });
};

// 批量配置地区开放状态
export const batchConfigOpen = (regionCodes, isOpen) => {
  return request({
    url: '/blade-ad/miniapp-region-config/batch-config-open',
    method: 'post',
    data: {
      regionCodes,
      isOpen
    }
  });
};

// 同步地区数据
export const syncData = () => {
  return request({
    url: '/blade-ad/miniapp-region-config/sync-data',
    method: 'post'
  });
};

// 可扩展：获取开放地区、按父级查等
// export const getOpenRegion = () => { ... }
// export const getByParent = (parentCode) => { ... } 
