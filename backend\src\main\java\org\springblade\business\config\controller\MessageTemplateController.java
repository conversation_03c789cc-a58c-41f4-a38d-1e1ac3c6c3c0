/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.business.config.entity.MessageTemplate;
import org.springblade.business.config.service.IMessageTemplateService;
import org.springblade.business.config.vo.MessageTemplateVO;
import org.springblade.business.config.wrapper.MessageTemplateWrapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springblade.business.post.dto.MessageTemplateCreateDTO;
import org.springblade.business.post.dto.MessageTemplateUpdateDTO;
import org.springblade.miniapp.service.WeChatMessageService;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;

import java.util.List;

/**
 * 消息模板表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/messagetemplate")
@io.swagger.v3.oas.annotations.tags.Tag(name = "消息模板表", description = "消息模板表接口")
public class MessageTemplateController extends BladeController {

	private IMessageTemplateService messageTemplateService;
	private WeChatMessageService messageService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")

	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入messageTemplate")
	public R<MessageTemplateVO> detail(MessageTemplate messageTemplate) {
		MessageTemplate detail = messageTemplateService.getOne(Condition.getQueryWrapper(messageTemplate));
		return R.data(MessageTemplateWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 消息模板表
	 */
	@GetMapping("/list")

	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入messageTemplate")
	public R<IPage<MessageTemplateVO>> list(MessageTemplate messageTemplate, Query query) {
		IPage<MessageTemplate> pages = messageTemplateService.page(Condition.getPage(query), Condition.getQueryWrapper(messageTemplate));
		return R.data(MessageTemplateWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 消息模板表
	 */
	@GetMapping("/page")

	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入messageTemplate")
	public R<IPage<MessageTemplateVO>> page(MessageTemplateVO messageTemplate, Query query) {
		IPage<MessageTemplateVO> pages = messageTemplateService.selectMessageTemplatePage(Condition.getPage(query), messageTemplate);
		return R.data(pages);
	}

	/**
	 * 新增 消息模板表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入messageTemplate")
	public R save(@Valid @RequestBody MessageTemplate messageTemplate) {
		return R.status(messageTemplateService.save(messageTemplate));
	}

	/**
	 * 修改 消息模板表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入messageTemplate")
	public R update(@Valid @RequestBody MessageTemplate messageTemplate) {
		return R.status(messageTemplateService.updateById(messageTemplate));
	}

	/**
	 * 新增或修改 消息模板表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入messageTemplate")
	public R submit(@Valid @RequestBody MessageTemplate messageTemplate) {
		return R.status(messageTemplateService.saveOrUpdate(messageTemplate));
	}


	/**
	 * 删除 消息模板表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(messageTemplateService.deleteLogic(Func.toLongList(ids)));
	}

	//------------------自己写的-----------------
	/**
	 * 获取所有消息模板,管理员可用
	 */

	@GetMapping("/templates")
	@Operation(summary = "获取所有消息模板", description = "返回所有可用的消息模板列表")
	public R<List<org.springblade.miniapp.vo.MessageTemplateVO>> getTemplates() {
		return R.data(messageService.getAllTemplates());
	}

	/**
	 * 更新消息模板,管理员可用
	 */
	@PutMapping("/templates")
	@Operation(summary = "更新消息模板", description = "根据ID更新消息模板内容和状态")
	public R<?> updateTemplate(
		@Parameter(description = "模板更新DTO", required = true) @RequestBody MessageTemplateUpdateDTO updateDTO) {
		messageService.updateTemplate(updateDTO);
		return R.success("更新成功");
	}

	/**
	 * 新增消息模板，管理员
	 */
	@PostMapping("/templates")
	@Operation(summary = "新增消息模板", description = "创建一个新的消息模板")
	public R<?> addTemplate(@Parameter(description = "新增消息模板DTO", required = true) @RequestBody MessageTemplateCreateDTO createDTO) {
		messageService.addTemplate(createDTO);
		return R.success("新增成功");
	}


}
