/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springblade.core.mp.base.BaseEntity;

import java.io.Serial;

/**
 * 小程序地区配置实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("miniapp_region_config")
@Schema(description = "小程序地区配置对象")
public class MiniappRegionConfig extends BaseEntity {

	@Serial
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(value = "id", type = IdType.ASSIGN_ID)
	@JsonSerialize(using = ToStringSerializer.class)
	@Schema(description = "主键")
	private Long id;

	/**
	 * 地区编码
	 */
	@Schema(description = "地区编码")
	private String regionCode;

	/**
	 * 地区名称
	 */
	@Schema(description = "地区名称")
	private String regionName;

	/**
	 * 父级地区编码
	 */
	@Schema(description = "父级地区编码")
	private String parentCode;

	/**
	 * 地区层级 1-省 2-市 3-县 4-乡镇 5-村
	 */
	@Schema(description = "地区层级")
	private Integer level;

	/**
	 * 是否开放 0-关闭 1-开放
	 */
	@Schema(description = "是否开放")
	private Integer isOpen;

	/**
	 * 排序
	 */
	@Schema(description = "排序")
	private Integer sort;

	/**
	 * 备注
	 */
	@Schema(description = "备注")
	private String remark;

	/**
	 * 租户ID
	 */
	@Schema(description = "租户ID")
	private String tenantId;

}
