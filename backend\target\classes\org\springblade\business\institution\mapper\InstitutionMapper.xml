<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.institution.mapper.InstitutionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="institutionResultMap" type="org.springblade.business.institution.vo.InstitutionVO">
        <result column="id" property="id"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user" property="createUser"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="status" property="status"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="name" property="name"/>
        <result column="logo" property="logo"/>
        <result column="type_id" property="typeId"/>
        <result column="years_in_business" property="yearsInBusiness"/>
        <result column="description" property="description"/>
        <result column="contact_person" property="contactPerson"/>
        <result column="phone" property="phone"/>
        <result column="backup_phone" property="backupPhone"/>
        <result column="email" property="email"/>
        <result column="wechat" property="wechat"/>
        <result column="qq" property="qq"/>
        <result column="license_no" property="licenseNo"/>
        <result column="license_image" property="licenseImage"/>
        <result column="legal_person" property="legalPerson"/>
        <result column="industry_license" property="industryLicense"/>
        <result column="tax_certificate" property="taxCertificate"/>
        <result column="province" property="province"/>
        <result column="city" property="city"/>
        <result column="district" property="district"/>
        <result column="detail_address" property="detailAddress"/>
        <result column="longitude" property="longitude"/>
        <result column="latitude" property="latitude"/>
        <result column="is_store" property="isStore"/>
        <result column="service_radius" property="serviceRadius"/>
        <result column="has_delivery" property="hasDelivery"/>
        <result column="payment_methods" property="paymentMethods"/>
        <result column="special_services" property="specialServices"/>
        <result column="username" property="username"/>
        <result column="password" property="password"/>
        <result column="salt" property="salt"/>
        <result column="last_login_time" property="lastLoginTime"/>
        <result column="last_login_ip" property="lastLoginIp"/>
        <result column="is_locked" property="isLocked"/>
        <result column="business_hours" property="businessHours"/>
        <result column="images" property="images"/>
        <result column="audit_status" property="auditStatus"/>
        <result column="audit_remark" property="auditRemark"/>
        <result column="apply_user_id" property="applyUserId"/>
        <result column="apply_time" property="applyTime"/>
        <result column="last_audit_time" property="lastAuditTime"/>
        <result column="last_audit_user_id" property="lastAuditUserId"/>
        <result column="top" property="top"/>
    </resultMap>


    <select id="selectInstitutionPage" resultMap="institutionResultMap">
        select * from urb_institution
        <where>
            is_deleted = 0
            <if test="institution.typeId != null">
                and type_id = #{institution.typeId}
            </if>
            <if test="institution.name != null and institution.name != ''">
                and name like concat('%',#{institution.name},'%')
            </if>
            <if test="institution.description != null and institution.description != ''">
                and description like concat('%',#{institution.description},'%')
            </if>
        </where>
        ORDER BY
        CASE WHEN top = '1' THEN '0' ELSE '1' END,
        create_time DESC
    </select>
    <select id="pageByUserId" resultType="org.springblade.business.institution.vo.InstitutionVO">
            select * from urb_institution where is_deleted = 0
            and id in (select institution_id from urb_user_institution where user_id=#{userId})
    </select>
    <select id="getInstutionById" resultType="org.springblade.business.institution.entity.Institution">
            select * from urb_institution where id = #{id}
            and is_deleted = 0
            ORDER BY
                CASE WHEN top = '1' THEN '0' ELSE '1' END,
                create_time DESC

    </select>

</mapper>
