# 签到积分功能说明文档

## 1. 功能概述

签到积分功能是小程序的核心用户激励系统，通过每日签到、连续签到奖励、积分商城等方式，提高用户活跃度和留存率。

### 1.1 主要功能
- **每日签到**：用户每日可签到一次，获得基础积分
- **连续签到奖励**：连续签到可获得额外奖励积分
- **积分管理**：用户积分余额、积分明细、积分等级
- **积分商城**：用户可使用积分兑换商品
- **签到记录**：查看历史签到记录和统计信息

### 1.2 技术架构
- **前端**：微信小程序
- **后端**：Spring Boot + MyBatis Plus
- **数据库**：MySQL 8.0
- **缓存**：Redis（可选）

## 2. 数据库设计

### 2.1 核心表结构

#### 2.1.1 用户积分表 (urb_user_points)
```sql
CREATE TABLE `urb_user_points` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户OpenID',
  `points` int DEFAULT '0' COMMENT '当前积分余额',
  `total_earned` int DEFAULT '0' COMMENT '累计获得积分',
  `total_spent` int DEFAULT '0' COMMENT '累计消费积分',
  `level` int DEFAULT '1' COMMENT '等级',
  `level_name` varchar(50) DEFAULT '新手' COMMENT '等级名称',
  `continuous_days` int DEFAULT '0' COMMENT '连续签到天数',
  `total_signin_days` int DEFAULT '0' COMMENT '总签到天数',
  -- 其他字段...
);
```

#### 2.1.2 签到记录表 (urb_signin_record)
```sql
CREATE TABLE `urb_signin_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户OpenID',
  `signin_date` date NOT NULL COMMENT '签到日期',
  `points` int DEFAULT '0' COMMENT '获得积分',
  `continuous_reward` int DEFAULT '0' COMMENT '连续签到奖励积分',
  `continuous_days` int DEFAULT '0' COMMENT '连续签到天数',
  -- 其他字段...
);
```

#### 2.1.3 积分记录表 (urb_points_record)
```sql
CREATE TABLE `urb_points_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户OpenID',
  `points` int NOT NULL COMMENT '积分变动数量',
  `type` varchar(20) NOT NULL COMMENT '积分类型',
  `description` varchar(200) DEFAULT NULL COMMENT '描述',
  -- 其他字段...
);
```

### 2.2 表关系
- `urb_user_points` ←→ `urb_points_record` (一对多)
- `urb_user_points` ←→ `urb_signin_record` (一对多)

## 3. 核心业务逻辑

### 3.1 签到流程

#### 3.1.1 签到验证
```java
// 检查今日是否已签到
if (isTodaySigned(openId)) {
    throw new RuntimeException("今日已签到，请明天再来");
}
```

#### 3.1.2 连续签到计算
```java
private int calculateContinuousDays(String openId, LocalDate signinDate) {
    SigninRecord lastSignin = baseMapper.selectLastSignin(openId);
    if (lastSignin == null) {
        return 1; // 首次签到
    }

    LocalDate lastSigninDate = lastSignin.getSigninDate();
    LocalDate yesterday = signinDate.minusDays(1);

    if (lastSigninDate.equals(yesterday)) {
        // 连续签到
        return lastSignin.getContinuousDays() + 1;
    } else {
        // 中断签到，重新开始
        return 1;
    }
}
```

#### 3.1.3 积分奖励计算
```java
// 基础签到积分
private static final int BASE_SIGNIN_POINTS = 10;

// 连续签到奖励配置
private static final List<Map<String, Object>> CONTINUOUS_REWARDS = Arrays.asList(
    Map.of("days", 1, "reward", 10),
    Map.of("days", 3, "reward", 15),
    Map.of("days", 7, "reward", 25),
    Map.of("days", 15, "reward", 50),
    Map.of("days", 30, "reward", 100)
);
```

### 3.2 积分管理

#### 3.2.1 积分增加
```java
@Transactional(rollbackFor = Exception.class)
public boolean addPoints(String openId, Integer points, String type, String description) {
    // 1. 获取用户积分信息
    UserPoints userPoints = getUserPointsByOpenId(openId);
    
    // 2. 更新积分
    int beforePoints = userPoints.getPoints();
    int afterPoints = beforePoints + points;
    baseMapper.updatePoints(openId, points);
    
    // 3. 记录积分变动
    PointsRecord record = new PointsRecord();
    record.setOpenId(openId);
    record.setPoints(points);
    record.setBeforePoints(beforePoints);
    record.setAfterPoints(afterPoints);
    record.setType(type);
    record.setDescription(description);
    pointsRecordService.save(record);
    
    return true;
}
```

#### 3.2.2 积分扣除
```java
@Transactional(rollbackFor = Exception.class)
public boolean deductPoints(String openId, Integer points, String type, String description) {
    // 1. 检查积分是否足够
    UserPoints userPoints = getUserPointsByOpenId(openId);
    if (userPoints.getPoints() < points) {
        return false;
    }
    
    // 2. 扣除积分
    baseMapper.updatePoints(openId, -points);
    
    // 3. 记录积分变动
    // ... 类似积分增加的逻辑
    
    return true;
}
```

## 4. API接口设计

### 4.1 小程序接口

#### 4.1.1 获取签到信息
```
GET /blade-chat/signin/info?openId={openId}

响应示例：
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "openId": "user_open_id",
    "todaySigned": false,
    "continuousDays": 5,
    "totalSigninDays": 30,
    "lastSigninTime": "2024-01-01",
    "currentPoints": 150
  }
}
```

#### 4.1.2 执行签到
```
POST /blade-chat/signin/do
Content-Type: application/json

请求体：
{
  "openId": "user_open_id"
}

响应示例：
{
  "code": 200,
  "msg": "签到成功",
  "data": {
    "id": 1,
    "openId": "user_open_id",
    "signinDate": "2024-01-01",
    "points": 25,
    "continuousReward": 15,
    "continuousDays": 6,
    "totalSigninDays": 31,
    "currentPoints": 175
  }
}
```

#### 4.1.3 获取月签到记录
```
GET /blade-chat/signin/record?openId={openId}&year=2024&month=1

响应示例：
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "date": "2024-01-01",
      "day": 1,
      "isCurrentMonth": true,
      "isToday": false,
      "isSigned": true
    }
    // ... 更多日期数据
  ]
}
```

#### 4.1.4 获取用户积分信息
```
GET /miniapp/points/info

响应示例：
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "openId": "用户OpenID",
    "points": 175,
    "totalEarned": 500,
    "totalSpent": 100,
    "level": 2,
    "levelName": "初级用户",
    "experience": 150,
    "nextLevelExp": 200,
    "continuousDays": 5,
    "totalSigninDays": 30
  }
}
```

#### 4.1.5 获取积分记录
```
GET /miniapp/points/records?current=1&size=20

响应示例：
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "records": [
      {
        "id": 123,
        "openId": "用户OpenID",
        "points": 25,
        "beforePoints": 125,
        "afterPoints": 150,
        "type": "SIGNIN",
        "typeName": "签到",
        "description": "每日签到，连续6天",
        "operateTime": "2024-01-15T08:30:00",
        "operateTimeStr": "2024-01-15 08:30:00",
        "operator": "system"
      }
    ],
    "total": 50,
    "current": 1,
    "size": 20
  }
}
```

#### 4.1.6 根据类型获取积分记录
```
GET /miniapp/points/records/type?type=SIGNIN&current=1&size=20

响应示例：同获取积分记录接口
```

#### 4.1.7 获取积分统计信息
```
GET /miniapp/points/statistics?startDate=2024-01-01&endDate=2024-01-31

响应示例：
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "openId": "用户OpenID",
    "totalEarnedRecords": 30,
    "totalSpentRecords": 5,
    "totalEarnedPoints": 500,
    "totalSpentPoints": 350,
    "netPoints": 150
  }
}
```

### 4.2 后台管理接口

#### 4.2.1 用户积分管理
```
GET /admin/points/users/page - 分页查询用户积分
GET /admin/points/users/detail - 获取用户积分详情
POST /admin/points/users/adjust - 管理员手动调整用户积分
```

#### 4.2.2 签到记录管理
```
GET /admin/signin/records/page - 分页查询签到记录
GET /admin/signin/records/detail - 获取签到记录详情
DELETE /admin/signin/records/remove - 删除签到记录
DELETE /admin/signin/records/remove-batch - 批量删除签到记录
```

#### 4.2.3 积分记录管理
```
GET /admin/points/records/page - 分页查询积分记录
GET /admin/points/records/detail - 获取积分记录详情
DELETE /admin/points/records/remove - 删除积分记录
DELETE /admin/points/records/remove-batch - 批量删除积分记录
```

## 5. 前端实现

### 5.1 签到页面结构

#### 5.1.1 页面文件
```
weapp/pages/mine/signin/
├── signin.js      # 页面逻辑
├── signin.wxml    # 页面结构
├── signin.wxss    # 页面样式
└── signin.json    # 页面配置
```

#### 5.1.2 核心功能
- 显示用户积分信息
- 签到按钮和状态
- 连续签到奖励展示
- 本月签到日历
- 签到规则说明

### 5.2 Store管理

#### 5.2.1 签到Store (signinStore.js)
```javascript
class SigninStore {
  // 获取签到信息
  async getSigninInfo() { }
  
  // 执行签到
  async doSignin() { }
  
  // 获取签到记录
  async getSigninRecord(params) { }
  
  // 获取连续签到奖励配置
  async getContinuousRewards() { }
}
```

#### 5.2.2 积分Store (pointsStore.js)
```javascript
class PointsStore {
  // 获取用户积分信息
  async getUserPoints() { }
  
  // 获取积分记录
  async getPointsRecords(current, size) { }
  
  // 根据类型获取积分记录
  async getPointsRecordsByType(type, current, size) { }
  
  // 获取积分统计信息
  async getPointsStatistics(startDate, endDate) { }
  
  // 获取积分明细（兼容旧接口）
  async getPointsDetail(params) { }
  
  // 获取积分商城
  async getPointsMall(params) { }
  
  // 兑换商品
  async exchangeGoods(params) { }
  
  // 获取兑换记录
  async getExchangeRecord(params) { }
  
  // 分享获得积分
  async shareForPoints() { }
  
  // 邀请好友获得积分
  async inviteForPoints(inviteCode) { }
}
```

## 6. 配置说明

### 6.1 签到奖励配置

#### 6.1.1 基础配置
```java
// 基础签到积分
private static final int BASE_SIGNIN_POINTS = 10;

// 连续签到奖励配置
private static final List<Map<String, Object>> CONTINUOUS_REWARDS = Arrays.asList(
    Map.of("days", 1, "reward", 10),
    Map.of("days", 3, "reward", 15),
    Map.of("days", 7, "reward", 25),
    Map.of("days", 15, "reward", 50),
    Map.of("days", 30, "reward", 100)
);
```

#### 6.1.2 数据库配置
```sql
-- 连续签到奖励配置表
INSERT INTO `urb_signin_reward_config` (`days`, `reward_points`, `reward_desc`) VALUES
(1, 10, '连续签到1天奖励'),
(3, 15, '连续签到3天奖励'),
(7, 25, '连续签到7天奖励'),
(15, 50, '连续签到15天奖励'),
(30, 100, '连续签到30天奖励');
```

### 6.2 积分商城配置

#### 6.2.1 示例商品
```sql
INSERT INTO `urb_points_goods` (`goods_name`, `points_price`, `category`) VALUES
('微信红包10元', 1000, '红包'),
('微信红包5元', 500, '红包'),
('优惠券10元', 800, '优惠券'),
('优惠券5元', 400, '优惠券'),
('实物礼品', 2000, '实物');
```

## 7. 安全考虑

### 7.1 防刷机制
- 每日只能签到一次
- 签到时间间隔限制
- IP地址和设备信息记录
- 异常签到行为监控

### 7.2 数据安全
- 积分变动记录完整
- 事务保证数据一致性
- 敏感操作日志记录
- 数据备份和恢复

### 7.3 接口安全
- 参数验证和过滤
- 频率限制
- 用户身份验证
- 异常处理

## 8. 性能优化

### 8.1 数据库优化
- 合理使用索引
- 分页查询
- 读写分离
- 缓存策略

### 8.2 缓存策略
```java
// Redis缓存示例
@Cacheable(value = "userPoints", key = "#openId")
public UserPoints getUserPointsByOpenId(String openId) {
    return baseMapper.selectByOpenId(openId);
}

@CacheEvict(value = "userPoints", key = "#openId")
public boolean updatePoints(String openId, Integer points) {
    return baseMapper.updatePoints(openId, points) > 0;
}
```

### 8.3 异步处理
```java
@Async
public void processSigninAsync(SigninRecord signinRecord) {
    // 异步处理签到后的业务逻辑
    // 如：发送通知、更新统计等
}
```

## 9. 监控运维

### 9.1 业务监控
- 签到成功率
- 用户活跃度
- 积分消耗情况
- 异常行为告警

### 9.2 系统监控
- 接口响应时间
- 数据库性能
- 系统资源使用
- 错误日志监控

### 9.3 数据统计
```sql
-- 签到统计SQL示例
SELECT 
    DATE(signin_date) as date,
    COUNT(*) as signin_count,
    SUM(points) as total_points
FROM urb_signin_record 
WHERE signin_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
GROUP BY DATE(signin_date)
ORDER BY date;
```

## 10. 扩展功能

### 10.1 未来规划
- 签到提醒功能
- 签到排行榜
- 签到任务系统
- 积分等级体系
- 积分过期机制
- 签到分享奖励

### 10.2 技术升级
- 微服务架构
- 消息队列
- 分布式缓存
- 实时计算
- 数据仓库

## 11. 部署说明

### 11.1 环境要求
- JDK 17+
- MySQL 8.0+
- Redis 6.0+ (可选)
- Maven 3.6+

### 11.2 部署步骤
1. 执行数据库脚本
2. 配置应用参数
3. 启动应用服务
4. 验证功能正常

### 11.3 配置参数
```yaml
# application.yml
points:
  signin:
    base-points: 10
    max-continuous-days: 30
    enable-makeup: true
    makeup-cost: 50
  
  mall:
    enable: true
    exchange-limit: 10
```

## 12. 测试建议

### 12.1 功能测试
- 正常签到流程
- 重复签到处理
- 连续签到计算
- 积分变动记录
- 异常情况处理

### 12.2 性能测试
- 并发签到测试
- 大量数据处理
- 缓存效果验证
- 数据库性能测试

### 12.3 安全测试
- 参数验证测试
- 权限控制测试
- 防刷机制测试
- 数据安全测试

---

**文档版本**: v1.0  
**更新时间**: 2024-01-01  
**维护人员**: 开发团队 