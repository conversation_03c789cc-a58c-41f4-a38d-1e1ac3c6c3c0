<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.institution.mapper.UserInstitutionMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="userInstitutionResultMap" type="org.springblade.business.institution.entity.UserInstitution">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="user_id" property="userId"/>
        <result column="institution_id" property="institutionId"/>
        <result column="role" property="role"/>
    </resultMap>


    <select id="selectUserInstitutionPage" resultMap="userInstitutionResultMap">
        select * from urb_user_institution where is_deleted = 0
    </select>

</mapper>
