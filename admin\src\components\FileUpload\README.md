# FileUpload 文件上传组件

## 组件概述

FileUpload 是一个可复用的文件上传组件，支持单文件、多文件上传，与后端接口保持一致，提供完整的文件管理功能。

## 功能特性

- ✅ 支持单文件/多文件上传
- ✅ 支持拖拽上传
- ✅ 文件类型和大小验证
- ✅ 上传进度显示
- ✅ 文件预览和下载
- ✅ 与后端接口完全兼容
- ✅ 支持业务关联
- ✅ 自定义样式和配置

## 基础用法

### 1. 单文件上传

```vue
<template>
  <FileUpload
    v-model="fileUrl"
    :upload-source="'admin'"
    :business-type="'post'"
    :business-id="postId"
    @success="handleUploadSuccess"
    @error="handleUploadError" />
</template>

<script>
import FileUpload from '@/components/FileUpload/index.vue'

export default {
  components: { FileUpload },
  data() {
    return {
      fileUrl: '',
      postId: 1
    }
  },
  methods: {
    handleUploadSuccess(fileData) {
      console.log('上传成功:', fileData)
    },
    handleUploadError(error) {
      console.error('上传失败:', error)
    }
  }
}
</script>
```

### 2. 多文件上传

```vue
<template>
  <FileUpload
    v-model="fileUrls"
    :multiple="true"
    :limit="5"
    :upload-source="'admin'"
    :business-type="'post'"
    :business-id="postId"
    @success="handleUploadSuccess"
    @error="handleUploadError" />
</template>

<script>
export default {
  data() {
    return {
      fileUrls: [],
      postId: 1
    }
  }
}
</script>
```

### 3. 拖拽上传

```vue
<template>
  <FileUpload
    v-model="fileUrls"
    :multiple="true"
    :drag="true"
    :upload-source="'admin'"
    @success="handleUploadSuccess" />
</template>
```

## Props 配置

| 参数 | 说明 | 类型 | 默认值 |
|------|------|------|--------|
| multiple | 是否支持多文件上传 | Boolean | false |
| limit | 最大上传数量 | Number | 10 |
| accept | 接受上传的文件类型 | String | 'image/*,application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,text/plain,text/csv' |
| drag | 是否启用拖拽上传 | Boolean | false |
| listType | 文件列表显示类型 | String | 'text' |
| showFileList | 是否显示已上传文件列表 | Boolean | true |
| disabled | 是否禁用上传 | Boolean | false |
| autoUpload | 是否自动上传 | Boolean | true |
| uploadSource | 上传来源 | String | 'admin' |
| businessType | 业务类型 | String | '' |
| businessId | 业务ID | String/Number | '' |
| buttonText | 上传按钮文字 | String | '选择文件' |
| tipText | 提示文字 | String | '支持jpg/png/gif/pdf/doc/docx/txt/csv格式，单个文件不超过10MB' |
| modelValue | 绑定值 | Array/String | [] |

## Events 事件

| 事件名 | 说明 | 回调参数 |
|--------|------|----------|
| update:modelValue | 绑定值变化时触发 | (value: Array/String) |
| success | 上传成功时触发 | (fileData: Object) |
| error | 上传失败时触发 | (error: Error) |
| remove | 移除文件时触发 | (file: Object) |
| exceed | 超出限制时触发 | (files: Array, fileList: Array) |

## 方法

| 方法名 | 说明 | 参数 |
|--------|------|------|
| submit | 手动上传文件 | - |
| clearFiles | 清空文件列表 | - |

## 使用场景

### 1. 帖子发布 - 图片上传

```vue
<template>
  <div class="post-form">
    <el-form :model="postForm" label-width="100px">
      <el-form-item label="标题">
        <el-input v-model="postForm.title" />
      </el-form-item>
      <el-form-item label="内容">
        <el-input v-model="postForm.content" type="textarea" />
      </el-form-item>
      <el-form-item label="图片">
        <FileUpload
          v-model="postForm.images"
          :multiple="true"
          :limit="6"
          :accept="'image/*'"
          :upload-source="'admin'"
          :business-type="'post'"
          :business-id="postId"
          @success="handleImageUpload" />
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  data() {
    return {
      postForm: {
        title: '',
        content: '',
        images: []
      },
      postId: null
    }
  },
  methods: {
    async handleImageUpload(fileData) {
      // 图片上传成功后的处理
      console.log('图片上传成功:', fileData)
    },
    async submitPost() {
      // 提交帖子
      const postData = {
        ...this.postForm,
        images: this.postForm.images
      }
      // 调用API提交
    }
  }
}
</script>
```

### 2. 用户头像上传

```vue
<template>
  <div class="avatar-upload">
    <FileUpload
      v-model="avatarUrl"
      :multiple="false"
      :accept="'image/*'"
      :upload-source="'admin'"
      :business-type="'user_avatar'"
      :business-id="userId"
      :show-file-list="false"
      @success="handleAvatarUpload" />
  </div>
</template>

<script>
export default {
  data() {
    return {
      avatarUrl: '',
      userId: 1
    }
  },
  methods: {
    async handleAvatarUpload(fileData) {
      // 更新用户头像
      await this.updateUserAvatar(fileData.accessUrl)
    }
  }
}
</script>
```

### 3. 文档上传

```vue
<template>
  <div class="document-upload">
    <FileUpload
      v-model="documents"
      :multiple="true"
      :accept="'application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document'"
      :upload-source="'admin'"
      :business-type="'document'"
      :business-id="documentId"
      :drag="true"
      @success="handleDocumentUpload" />
  </div>
</template>
```

## 样式定制

### 1. 自定义样式

```vue
<template>
  <FileUpload
    v-model="files"
    class="custom-upload"
    :upload-source="'admin'" />
</template>

<style lang="scss" scoped>
.custom-upload {
  :deep(.el-upload) {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: border-color 0.3s;
    
    &:hover {
      border-color: #409eff;
    }
  }
  
  :deep(.file-list) {
    .file-item {
      background: #fafafa;
      border-radius: 8px;
      
      &:hover {
        background: #f0f0f0;
      }
    }
  }
}
</style>
```

### 2. 响应式布局

```vue
<template>
  <FileUpload
    v-model="files"
    :list-type="'picture-card'"
    :upload-source="'admin'" />
</template>

<style lang="scss" scoped>
:deep(.el-upload--picture-card) {
  width: 120px;
  height: 120px;
  line-height: 120px;
}

@media (max-width: 768px) {
  :deep(.el-upload--picture-card) {
    width: 80px;
    height: 80px;
    line-height: 80px;
  }
}
</style>
```

## 注意事项

### 1. 文件大小限制
- 默认最大文件大小为 10MB
- 可在后端配置中调整

### 2. 文件类型限制
- 默认支持图片、PDF、Word、文本等格式
- 可通过 `accept` 属性自定义

### 3. 上传来源
- `admin`: 管理后台
- `miniapp`: 小程序
- 其他自定义来源

### 4. 业务关联
- 通过 `businessType` 和 `businessId` 关联业务数据
- 便于后续文件管理和统计

### 5. 错误处理
- 组件内置文件验证
- 提供详细的错误信息
- 支持自定义错误处理

## 最佳实践

### 1. 性能优化
```vue
<template>
  <!-- 使用 v-if 控制组件渲染 -->
  <FileUpload
    v-if="showUpload"
    v-model="files"
    :upload-source="'admin'" />
</template>
```

### 2. 用户体验
```vue
<template>
  <FileUpload
    v-model="files"
    :upload-source="'admin'"
    @success="handleSuccess"
    @error="handleError">
    <template #tip>
      <div class="upload-tip">
        <p>支持的文件格式：JPG、PNG、PDF、DOC</p>
        <p>单个文件大小不超过 10MB</p>
      </div>
    </template>
  </FileUpload>
</template>
```

### 3. 错误处理
```vue
<script>
export default {
  methods: {
    handleError(error) {
      // 根据错误类型显示不同的提示
      if (error.message.includes('大小')) {
        this.$message.error('文件大小超出限制')
      } else if (error.message.includes('类型')) {
        this.$message.error('不支持的文件类型')
      } else {
        this.$message.error('上传失败，请重试')
      }
    }
  }
}
</script>
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基础文件上传功能
- 支持单文件/多文件上传
- 支持拖拽上传
- 与后端接口完全兼容 