/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (blade<PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.service.impl;

import org.springblade.business.config.entity.UrbMenu;
import org.springblade.business.config.vo.UrbMenuVO;
import org.springblade.business.config.mapper.UrbMenuMapper;
import org.springblade.business.config.service.IUrbMenuService;
import org.springblade.core.mp.base.BaseServiceImpl;
import org.springblade.core.tool.utils.Func;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import static org.springblade.common.cache.CacheNames.WECHAT_MENU_LIST;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@Service
public class UrbMenuServiceImpl extends BaseServiceImpl<UrbMenuMapper, UrbMenu> implements IUrbMenuService {

	@Override
	public IPage<UrbMenuVO> selectUrbMenuPage(IPage<UrbMenuVO> page, UrbMenuVO urbMenu) {
		if (page==null){
			page = new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(1, 100);
		}
		return page.setRecords(baseMapper.selectUrbMenuPage(page, urbMenu));
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_MENU_LIST,allEntries = true),
	})
	public boolean saveMenu(UrbMenu urbMenu) {
		return this.save(urbMenu);
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_MENU_LIST,allEntries = true),
	})
	public boolean updateMenuById(UrbMenu urbMenu) {
		return this.updateById(urbMenu);
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_MENU_LIST,allEntries = true),
	})
	public boolean saveOrUpdateMenu(UrbMenu urbMenu) {
		return this.saveOrUpdate(urbMenu);
	}

	@Override
	@Caching(evict = {
		@CacheEvict(cacheNames = WECHAT_MENU_LIST,allEntries = true),
	})
	public boolean deleteLogicMenu(String ids) {
		return this.deleteLogic(Func.toLongList(ids));
	}

}
