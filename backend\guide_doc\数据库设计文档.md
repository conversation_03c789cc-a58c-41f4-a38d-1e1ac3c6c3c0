# 数据库设计文档

## 数据库概述

### 基本信息
- **数据库名称**: easy_post
- **字符集**: utf8mb4
- **排序规则**: utf8mb4_general_ci
- **引擎**: InnoDB
- **版本**: MySQL 8.0+

### 设计原则
1. **规范化设计**: 遵循第三范式，避免数据冗余
2. **性能优化**: 合理使用索引，优化查询性能
3. **扩展性**: 支持多租户，便于功能扩展
4. **安全性**: 敏感数据加密，权限控制
5. **可维护性**: 清晰的命名规范，完整的注释

## 核心表结构

### 1. 信息贴表 (urb_post)

**表说明**: 存储用户发布的信息贴内容

```sql
CREATE TABLE `urb_post` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `content` text COMMENT '内容',
  `images` text COMMENT '图片JSON数组',
  `address` varchar(255) DEFAULT NULL COMMENT '发布地址',
  `publish_time` datetime(6) DEFAULT NULL COMMENT '发布时间',
  `audit_status` varchar(20) DEFAULT 'PENDING' COMMENT '审核状态（PENDING：待审核，APPROVED：已通过，REJECTED：已拒绝）',
  `geo_location` json DEFAULT NULL COMMENT '地理位置JSON',
  `tags` json DEFAULT NULL COMMENT '标签JSON数组',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `top` varchar(10) DEFAULT '0' COMMENT '是否置顶（0：否，1：是）',
  `completed` int DEFAULT '0' COMMENT '是否已完成（0：否，1：是）',
  `like_count` int DEFAULT '0' COMMENT '点赞数',
  `favorite_count` int DEFAULT '0' COMMENT '收藏数',
  `view_count` int DEFAULT '0' COMMENT '浏览数',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  PRIMARY KEY (`id`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_create_user` (`create_user`),
  KEY `idx_status_deleted` (`status`, `is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='百事通信息贴';
```

**字段说明**:
- `title`: 帖子标题，支持模糊查询
- `content`: 帖子内容，支持富文本
- `images`: 图片URL数组，JSON格式存储
- `address`: 发布地址，用于地理位置显示
- `audit_status`: 审核状态，支持审核流程
- `geo_location`: 地理位置信息，包含经纬度
- `tags`: 标签数组，JSON格式存储
- `contact_name/contact_phone`: 联系人信息
- `top`: 置顶标识，支持帖子置顶功能
- `completed`: 完成标识，用于标记帖子状态
- `like_count/favorite_count/view_count`: 统计数据

### 2. 分类表 (urb_category)

**表说明**: 存储信息贴的分类信息

```sql
CREATE TABLE `urb_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT '0' COMMENT '上级分类ID（0表示顶级分类）',
  `max_images` int DEFAULT '6' COMMENT '最大图片数',
  `allow_tags` json DEFAULT NULL COMMENT '允许的标签JSON数组',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  `icon` varchar(100) DEFAULT NULL COMMENT '分类图标',
  `description` varchar(500) DEFAULT NULL COMMENT '分类描述',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_name` (`name`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_status_deleted` (`status`, `is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告分类';
```

**字段说明**:
- `name`: 分类名称，唯一约束
- `parent_id`: 支持多级分类结构
- `max_images`: 限制每个分类的图片数量
- `allow_tags`: 分类允许使用的标签
- `sort_order`: 分类排序
- `icon`: 分类图标，用于前端显示
- `description`: 分类描述信息

### 3. 标签表 (urb_tag)

**表说明**: 存储信息贴的标签信息

```sql
CREATE TABLE `urb_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `tag_name` varchar(50) NOT NULL COMMENT '标签名称',
  `description` text COMMENT '描述说明',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  `color` varchar(20) DEFAULT '#1890ff' COMMENT '标签颜色',
  `use_count` int DEFAULT '0' COMMENT '使用次数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tag_name` (`tag_name`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_use_count` (`use_count`),
  KEY `idx_status_deleted` (`status`, `is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信息标签';
```

**字段说明**:
- `tag_name`: 标签名称，唯一约束
- `description`: 标签描述信息
- `sort_order`: 标签排序
- `color`: 标签颜色，用于前端显示
- `use_count`: 标签使用次数，用于热门标签统计

### 4. 分类标签关联表 (urb_category_tag)

**表说明**: 存储分类与标签的关联关系

```sql
CREATE TABLE `urb_category_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `category_id` bigint NOT NULL COMMENT '分类ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_tag` (`category_id`, `tag_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_tag_id` (`tag_id`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类标签关联';
```

**字段说明**:
- `category_id`: 分类ID，外键关联
- `tag_id`: 标签ID，外键关联
- `sort_order`: 在分类中的排序
- 唯一约束确保分类和标签的一对多关系

### 5. 帖子标签关联表 (urb_post_tag)

**表说明**: 存储帖子与标签的关联关系

```sql
CREATE TABLE `urb_post_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint NOT NULL COMMENT '帖子ID',
  `tag_id` bigint NOT NULL COMMENT '标签ID',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_tag` (`post_id`, `tag_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子标签关联';
```

**字段说明**:
- `post_id`: 帖子ID，外键关联
- `tag_id`: 标签ID，外键关联
- 唯一约束确保帖子和标签的多对多关系

### 6. 审核日志表 (urb_audit_log)

**表说明**: 存储帖子审核的日志记录

```sql
CREATE TABLE `urb_audit_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint NOT NULL COMMENT '信息贴ID',
  `audit_time` datetime(6) DEFAULT NULL COMMENT '审核时间',
  `audit_status` varchar(20) NOT NULL COMMENT '审核状态（PENDING：待审核，APPROVED：已通过，REJECTED：已拒绝）',
  `audit_user` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_remark` text COMMENT '审核备注',
  `audit_reason` varchar(500) DEFAULT NULL COMMENT '审核原因',
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_audit_status` (`audit_status`),
  KEY `idx_audit_user` (`audit_user`),
  KEY `idx_audit_time` (`audit_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='审核日志';
```

**字段说明**:
- `post_id`: 被审核的帖子ID
- `audit_time`: 审核时间
- `audit_status`: 审核结果状态
- `audit_user`: 审核人员ID
- `audit_remark`: 审核备注信息
- `audit_reason`: 审核原因，特别是拒绝原因

### 7. 用户信息表 (urb_user)

**表说明**: 存储用户的基本信息

```sql
CREATE TABLE `urb_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别（MALE：男，FEMALE：女）',
  `signature` text COMMENT '个性签名',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `open_id` varchar(100) DEFAULT NULL COMMENT '微信OpenID',
  `union_id` varchar(100) DEFAULT NULL COMMENT '微信UnionID',
  `last_login_time` datetime(6) DEFAULT NULL COMMENT '最后登录时间',
  `post_count` int DEFAULT '0' COMMENT '发布帖子数量',
  `like_count` int DEFAULT '0' COMMENT '获得点赞数量',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mobile` (`mobile`),
  UNIQUE KEY `uk_open_id` (`open_id`),
  KEY `idx_union_id` (`union_id`),
  KEY `idx_status_deleted` (`status`, `is_deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户信息';
```

**字段说明**:
- `nickname`: 用户昵称
- `mobile`: 手机号，用于登录和联系
- `gender`: 性别信息
- `signature`: 个性签名
- `avatar`: 用户头像
- `open_id/union_id`: 微信小程序用户标识
- `last_login_time`: 最后登录时间
- `post_count/like_count`: 用户统计数据

### 8. 点赞记录表 (urb_like)

**表说明**: 存储用户点赞记录

```sql
CREATE TABLE `urb_like` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint NOT NULL COMMENT '信息贴ID',
  `user_id` bigint NOT NULL COMMENT '操作用户ID',
  `like_time` datetime(6) DEFAULT NULL COMMENT '点赞时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_user` (`post_id`, `user_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_like_time` (`like_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='点赞记录';
```

**字段说明**:
- `post_id`: 被点赞的帖子ID
- `user_id`: 点赞用户ID
- `like_time`: 点赞时间
- 唯一约束防止重复点赞

### 9. 收藏记录表 (urb_favorite)

**表说明**: 存储用户收藏记录

```sql
CREATE TABLE `urb_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint NOT NULL COMMENT '信息贴ID',
  `user_id` bigint NOT NULL COMMENT '操作用户ID',
  `fav_time` datetime(6) DEFAULT NULL COMMENT '收藏时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_user` (`post_id`, `user_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_fav_time` (`fav_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏记录';
```

**字段说明**:
- `post_id`: 被收藏的帖子ID
- `user_id`: 收藏用户ID
- `fav_time`: 收藏时间
- 唯一约束防止重复收藏

### 10. 浏览记录表 (urb_view_log)

**表说明**: 存储用户浏览记录

```sql
CREATE TABLE `urb_view_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint NOT NULL COMMENT '信息贴ID',
  `user_id` bigint DEFAULT NULL COMMENT '浏览用户ID',
  `view_time` datetime(6) DEFAULT NULL COMMENT '浏览时间',
  `view_count` int DEFAULT '1' COMMENT '当日计数',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_view_time` (`view_time`),
  KEY `idx_post_date` (`post_id`, `view_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='浏览记录';
```

**字段说明**:
- `post_id`: 被浏览的帖子ID
- `user_id`: 浏览用户ID（可为空，支持匿名浏览）
- `view_time`: 浏览时间
- `view_count`: 当日浏览计数
- `ip_address`: 访问IP地址
- `user_agent`: 用户代理信息

### 11. 反馈表 (urb_feedback)

**表说明**: 存储用户反馈信息

```sql
CREATE TABLE `urb_feedback` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `type` varchar(20) NOT NULL COMMENT '反馈类型（BUG：问题，SUGGESTION：建议，COMPLAINT：投诉）',
  `title` varchar(200) NOT NULL COMMENT '反馈标题',
  `content` text NOT NULL COMMENT '反馈内容',
  `contact` varchar(100) DEFAULT NULL COMMENT '联系方式',
  `images` json DEFAULT NULL COMMENT '图片JSON数组',
  `process_status` varchar(20) DEFAULT 'PENDING' COMMENT '处理状态（PENDING：待处理，PROCESSING：处理中，RESOLVED：已解决，CLOSED：已关闭）',
  `process_user` bigint DEFAULT NULL COMMENT '处理人ID',
  `process_time` datetime(6) DEFAULT NULL COMMENT '处理时间',
  `process_remark` text COMMENT '处理备注',
  PRIMARY KEY (`id`),
  KEY `idx_type` (`type`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_create_user` (`create_user`),
  KEY `idx_process_user` (`process_user`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户反馈';
```

**字段说明**:
- `type`: 反馈类型分类
- `title`: 反馈标题
- `content`: 反馈详细内容
- `contact`: 联系方式
- `images`: 反馈相关图片
- `process_status`: 处理状态跟踪
- `process_user`: 处理人员
- `process_remark`: 处理结果备注

### 12. 举报表 (urb_report)

**表说明**: 存储用户举报信息

```sql
CREATE TABLE `urb_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint NOT NULL COMMENT '被举报的帖子ID',
  `report_user` bigint NOT NULL COMMENT '举报用户ID',
  `report_type` varchar(20) NOT NULL COMMENT '举报类型（SPAM：垃圾信息，ILLEGAL：违法信息，HARASSMENT：骚扰信息，OTHER：其他）',
  `report_reason` text NOT NULL COMMENT '举报原因',
  `evidence` json DEFAULT NULL COMMENT '证据图片JSON数组',
  `process_status` varchar(20) DEFAULT 'PENDING' COMMENT '处理状态（PENDING：待处理，PROCESSING：处理中，VALID：有效举报，INVALID：无效举报）',
  `process_user` bigint DEFAULT NULL COMMENT '处理人ID',
  `process_time` datetime(6) DEFAULT NULL COMMENT '处理时间',
  `process_result` text COMMENT '处理结果',
  PRIMARY KEY (`id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_report_user` (`report_user`),
  KEY `idx_report_type` (`report_type`),
  KEY `idx_process_status` (`process_status`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='举报信息';
```

**字段说明**:
- `post_id`: 被举报的帖子ID
- `report_user`: 举报用户ID
- `report_type`: 举报类型分类
- `report_reason`: 举报原因说明
- `evidence`: 举报证据图片
- `process_status`: 处理状态跟踪
- `process_result`: 处理结果说明

## 数据关系图

### 实体关系图
```
urb_post (信息贴)
├── 1:N urb_post_tag (帖子标签关联)
├── N:1 urb_category (分类)
├── 1:1 urb_user (发布用户)
├── 1:N urb_like (点赞记录)
├── 1:N urb_favorite (收藏记录)
├── 1:N urb_view_log (浏览记录)
├── 1:N urb_report (举报记录)
└── 1:N urb_audit_log (审核日志)

urb_category (分类)
├── 1:N urb_category_tag (分类标签关联)
├── 1:N urb_post (信息贴)
└── 1:N urb_category (子分类)

urb_tag (标签)
├── N:1 urb_category_tag (分类标签关联)
└── N:1 urb_post_tag (帖子标签关联)

urb_user (用户)
├── 1:N urb_post (发布帖子)
├── 1:N urb_like (点赞记录)
├── 1:N urb_favorite (收藏记录)
├── 1:N urb_view_log (浏览记录)
├── 1:N urb_feedback (反馈)
└── 1:N urb_report (举报)
```

### 外键关系
```sql
-- 帖子与分类关系
ALTER TABLE `urb_post` ADD CONSTRAINT `fk_post_category` 
FOREIGN KEY (`category_id`) REFERENCES `urb_category` (`id`);

-- 帖子与用户关系
ALTER TABLE `urb_post` ADD CONSTRAINT `fk_post_user` 
FOREIGN KEY (`create_user`) REFERENCES `urb_user` (`id`);

-- 分类标签关联
ALTER TABLE `urb_category_tag` ADD CONSTRAINT `fk_category_tag_category` 
FOREIGN KEY (`category_id`) REFERENCES `urb_category` (`id`);
ALTER TABLE `urb_category_tag` ADD CONSTRAINT `fk_category_tag_tag` 
FOREIGN KEY (`tag_id`) REFERENCES `urb_tag` (`id`);

-- 帖子标签关联
ALTER TABLE `urb_post_tag` ADD CONSTRAINT `fk_post_tag_post` 
FOREIGN KEY (`post_id`) REFERENCES `urb_post` (`id`);
ALTER TABLE `urb_post_tag` ADD CONSTRAINT `fk_post_tag_tag` 
FOREIGN KEY (`tag_id`) REFERENCES `urb_tag` (`id`);

-- 点赞记录关联
ALTER TABLE `urb_like` ADD CONSTRAINT `fk_like_post` 
FOREIGN KEY (`post_id`) REFERENCES `urb_post` (`id`);
ALTER TABLE `urb_like` ADD CONSTRAINT `fk_like_user` 
FOREIGN KEY (`user_id`) REFERENCES `urb_user` (`id`);

-- 收藏记录关联
ALTER TABLE `urb_favorite` ADD CONSTRAINT `fk_favorite_post` 
FOREIGN KEY (`post_id`) REFERENCES `urb_post` (`id`);
ALTER TABLE `urb_favorite` ADD CONSTRAINT `fk_favorite_user` 
FOREIGN KEY (`user_id`) REFERENCES `urb_user` (`id`);

-- 浏览记录关联
ALTER TABLE `urb_view_log` ADD CONSTRAINT `fk_view_log_post` 
FOREIGN KEY (`post_id`) REFERENCES `urb_post` (`id`);
ALTER TABLE `urb_view_log` ADD CONSTRAINT `fk_view_log_user` 
FOREIGN KEY (`user_id`) REFERENCES `urb_user` (`id`);

-- 反馈关联
ALTER TABLE `urb_feedback` ADD CONSTRAINT `fk_feedback_user` 
FOREIGN KEY (`create_user`) REFERENCES `urb_user` (`id`);

-- 举报关联
ALTER TABLE `urb_report` ADD CONSTRAINT `fk_report_post` 
FOREIGN KEY (`post_id`) REFERENCES `urb_post` (`id`);
ALTER TABLE `urb_report` ADD CONSTRAINT `fk_report_user` 
FOREIGN KEY (`report_user`) REFERENCES `urb_user` (`id`);

-- 审核日志关联
ALTER TABLE `urb_audit_log` ADD CONSTRAINT `fk_audit_log_post` 
FOREIGN KEY (`post_id`) REFERENCES `urb_post` (`id`);
```

## 索引设计

### 1. 主键索引
所有表都使用自增主键作为聚集索引

### 2. 唯一索引
```sql
-- 分类名称唯一
CREATE UNIQUE INDEX `uk_name` ON `urb_category` (`name`);

-- 标签名称唯一
CREATE UNIQUE INDEX `uk_tag_name` ON `urb_tag` (`tag_name`);

-- 用户手机号唯一
CREATE UNIQUE INDEX `uk_mobile` ON `urb_user` (`mobile`);

-- 用户OpenID唯一
CREATE UNIQUE INDEX `uk_open_id` ON `urb_user` (`open_id`);

-- 分类标签关联唯一
CREATE UNIQUE INDEX `uk_category_tag` ON `urb_category_tag` (`category_id`, `tag_id`);

-- 帖子标签关联唯一
CREATE UNIQUE INDEX `uk_post_tag` ON `urb_post_tag` (`post_id`, `tag_id`);

-- 点赞记录唯一
CREATE UNIQUE INDEX `uk_post_user` ON `urb_like` (`post_id`, `user_id`);

-- 收藏记录唯一
CREATE UNIQUE INDEX `uk_post_user` ON `urb_favorite` (`post_id`, `user_id`);
```

### 3. 普通索引
```sql
-- 帖子表索引
CREATE INDEX `idx_create_time` ON `urb_post` (`create_time`);
CREATE INDEX `idx_audit_status` ON `urb_post` (`audit_status`);
CREATE INDEX `idx_category_id` ON `urb_post` (`category_id`);
CREATE INDEX `idx_create_user` ON `urb_post` (`create_user`);
CREATE INDEX `idx_status_deleted` ON `urb_post` (`status`, `is_deleted`);

-- 分类表索引
CREATE INDEX `idx_parent_id` ON `urb_category` (`parent_id`);
CREATE INDEX `idx_sort_order` ON `urb_category` (`sort_order`);
CREATE INDEX `idx_status_deleted` ON `urb_category` (`status`, `is_deleted`);

-- 标签表索引
CREATE INDEX `idx_sort_order` ON `urb_tag` (`sort_order`);
CREATE INDEX `idx_use_count` ON `urb_tag` (`use_count`);
CREATE INDEX `idx_status_deleted` ON `urb_tag` (`status`, `is_deleted`);

-- 用户表索引
CREATE INDEX `idx_union_id` ON `urb_user` (`union_id`);
CREATE INDEX `idx_status_deleted` ON `urb_user` (`status`, `is_deleted`);

-- 点赞记录索引
CREATE INDEX `idx_post_id` ON `urb_like` (`post_id`);
CREATE INDEX `idx_user_id` ON `urb_like` (`user_id`);
CREATE INDEX `idx_like_time` ON `urb_like` (`like_time`);

-- 收藏记录索引
CREATE INDEX `idx_post_id` ON `urb_favorite` (`post_id`);
CREATE INDEX `idx_user_id` ON `urb_favorite` (`user_id`);
CREATE INDEX `idx_fav_time` ON `urb_favorite` (`fav_time`);

-- 浏览记录索引
CREATE INDEX `idx_post_id` ON `urb_view_log` (`post_id`);
CREATE INDEX `idx_user_id` ON `urb_view_log` (`user_id`);
CREATE INDEX `idx_view_time` ON `urb_view_log` (`view_time`);
CREATE INDEX `idx_post_date` ON `urb_view_log` (`post_id`, `view_time`);

-- 反馈表索引
CREATE INDEX `idx_type` ON `urb_feedback` (`type`);
CREATE INDEX `idx_process_status` ON `urb_feedback` (`process_status`);
CREATE INDEX `idx_create_user` ON `urb_feedback` (`create_user`);
CREATE INDEX `idx_create_time` ON `urb_feedback` (`create_time`);

-- 举报表索引
CREATE INDEX `idx_post_id` ON `urb_report` (`post_id`);
CREATE INDEX `idx_report_user` ON `urb_report` (`report_user`);
CREATE INDEX `idx_report_type` ON `urb_report` (`report_type`);
CREATE INDEX `idx_process_status` ON `urb_report` (`process_status`);
CREATE INDEX `idx_create_time` ON `urb_report` (`create_time`);
```

## 数据字典

### 1. 状态枚举
```sql
-- 通用状态
0: 禁用
1: 启用

-- 审核状态
PENDING: 待审核
APPROVED: 已通过
REJECTED: 已拒绝

-- 处理状态
PENDING: 待处理
PROCESSING: 处理中
RESOLVED: 已解决
CLOSED: 已关闭
VALID: 有效
INVALID: 无效

-- 性别
MALE: 男
FEMALE: 女

-- 反馈类型
BUG: 问题
SUGGESTION: 建议
COMPLAINT: 投诉

-- 举报类型
SPAM: 垃圾信息
ILLEGAL: 违法信息
HARASSMENT: 骚扰信息
OTHER: 其他

-- 置顶标识
0: 否
1: 是

-- 完成标识
0: 否
1: 是
```

### 2. 字段长度限制
```sql
-- 字符串字段长度
title: 255字符
content: 无限制（TEXT类型）
name: 50字符
tag_name: 50字符
nickname: 50字符
mobile: 20字符
address: 255字符
contact_name: 50字符
contact_phone: 20字符

-- 数值字段范围
sort_order: 0-999999
max_images: 0-20
like_count: 0-999999999
favorite_count: 0-999999999
view_count: 0-999999999
use_count: 0-999999999
```

## 性能优化建议

### 1. 查询优化
- 使用分页查询避免返回大量数据
- 合理使用索引提高查询速度
- 避免使用SELECT *，只查询需要的字段
- 使用EXPLAIN分析查询执行计划

### 2. 索引优化
- 为常用查询条件创建索引
- 避免创建过多索引影响写入性能
- 定期分析索引使用情况
- 删除未使用的索引

### 3. 数据分区
- 对于大表考虑按时间分区
- 历史数据可以归档到历史表
- 定期清理过期数据

### 4. 缓存策略
- 热点数据使用Redis缓存
- 分类和标签数据可以缓存
- 用户信息可以缓存
- 统计数据可以缓存

## 数据安全

### 1. 数据加密
- 敏感信息如手机号需要加密存储
- 使用AES加密算法
- 密钥需要安全管理

### 2. 权限控制
- 实现行级权限控制
- 多租户数据隔离
- 用户只能访问自己的数据

### 3. 审计日志
- 记录重要操作的审计日志
- 保留操作历史记录
- 支持数据追溯

### 4. 备份策略
- 定期备份数据库
- 增量备份和全量备份结合
- 备份数据需要加密存储 