import request from '@/axios';

export const getList = (current, size, params) => {
  return request({
    url: '/blade-ad/post/list',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}
export const getPage = (current, size, params) => {
  return request({
    url: '/blade-ad/post/page',
    method: 'get',
    params: {
      ...params,
      current,
      size,
    }
  })
}

export const getDetail = (id) => {
  return request({
    url: '/blade-ad/post/detail/' + id,
    method: 'get'
  })
}

export const remove = (ids) => {
  return request({
    url: '/blade-ad/post/batch',
    method: 'delete',
    params: {
      postIds: ids,
    }
  })
}

export const add = (row) => {
  return request({
    url: '/blade-ad/post/submit',
    method: 'post',
    data: row
  })
}

export const update = (row) => {
  return request({
    url: '/blade-ad/post/submit',
    method: 'post',
    data: row
  })
}

// 置顶帖子
export const topPost = (id, isTop) => {
  return request({
    url: `/blade-ad/post/${id}/top`,
    method: 'post',
    params: { isTop }
  })
}

// 获取帖子统计
export const getPostStats = () => {
  return request({
    url: '/blade-ad/post/stats',
    method: 'get'
  })
}

