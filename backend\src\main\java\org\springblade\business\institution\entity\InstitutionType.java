/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 机构分类表实体类
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@Data
@TableName("urb_institution_type")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "机构分类表")
public class InstitutionType extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 分类名称
     */
    @Schema(description = "分类名称")
    private String name;
    /**
     * 分类图标
     */
    @Schema(description = "分类图标")
    private String icon;
    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortOrder;


}
