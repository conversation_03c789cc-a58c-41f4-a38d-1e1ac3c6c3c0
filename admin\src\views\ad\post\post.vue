<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               v-model:search="search"
               v-model:page="page"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template #menu-left>
        <el-button type="danger"
                   
                   icon="el-icon-delete"
                   plain
                   v-if="permission.post_delete"
                   @click="handleDelete">删 除
        </el-button>
        <el-button type="success"
                   
                   icon="el-icon-refresh"
                   plain
                   @click="handleRefresh">刷 新
        </el-button>
        <el-button type="primary"
                   icon="el-icon-check"
                   plain
                   @click="handleBatchAudit">批量审核
        </el-button>
      </template>
      <template #status="{ row }">
        <el-tag :type="getStatusType(row.status)">
          {{ getStatusText(row.status) }}
        </el-tag>
      </template>
      <template #auditStatus="{ row }">
        <el-tag :type="getAuditStatusType(row.auditStatus)">
          {{ getAuditStatusText(row.auditStatus) }}
        </el-tag>
      </template>
      <template #content="{ row }">
        <el-button type="text" @click="viewContent(row)">
          {{ row.content ? (row.content.length > 50 ? row.content.substring(0, 50) + '...' : row.content) : '查看内容' }}
        </el-button>
      </template>
      <template #images="{ row }">
        <el-image 
          v-if="row.images && getImageList(row.images).length > 0"
          :src="getImageList(row.images)[0]"
          style="width: 50px; height: 50px;"
          :preview-src-list="getImageList(row.images)">
        </el-image>
        <span v-else style="color: #999;">无图片</span>
      </template>
      <template #tags="{ row }">
        <el-tag v-for="tag in row.tags" :key="tag" type="info" style="margin-right: 5px;">
          {{ tag }}
        </el-tag>
      </template>
      <template #menu="{ row }">
        <el-button v-if="row.auditStatus === '0'" 
                   type="success" 
                   size="small"
                   @click="handleAudit(row, '1')">
          通过
        </el-button>
        <el-button v-if="row.auditStatus === '0'" 
                   type="danger" 
                   size="small"
                   @click="handleAudit(row, '2')">
          拒绝
        </el-button>
      </template>
    </avue-crud>

    <!-- 内容详情对话框 -->
    <el-dialog v-model="contentDialogVisible" title="帖子内容详情" width="600px">
      <div v-if="selectedPost">
        <el-descriptions :column="2" border>
          <!-- <el-descriptions-item label="标题">{{ selectedPost.title }}</el-descriptions-item> -->
          <el-descriptions-item label="分类">{{ selectedPost.categoryName }}</el-descriptions-item>
          <el-descriptions-item label="发布者">{{ selectedPost.nickname }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ selectedPost.publishTime }}</el-descriptions-item>
          <el-descriptions-item label="状态">{{ getStatusText(selectedPost.status) }}</el-descriptions-item>
          <el-descriptions-item label="审核状态">{{ getAuditStatusText(selectedPost.auditStatus) }}</el-descriptions-item>
        </el-descriptions>
        <el-divider />
        <div>
          <h4>内容：</h4>
          <p style="white-space: pre-wrap;">{{ selectedPost.content }}</p>
        </div>
        <div v-if="selectedPost.images && getImageList(selectedPost.images).length > 0">
          <h4>图片：</h4>
          <el-image 
            v-for="(img, index) in getImageList(selectedPost.images)" 
            :key="index"
            :src="img"
            style="width: 100px; height: 100px; margin-right: 10px; margin-bottom: 10px;"
            :preview-src-list="getImageList(selectedPost.images)">
          </el-image>
        </div>
        <div v-if="selectedPost.tags && selectedPost.tags.length > 0">
          <h4>标签：</h4>
          <el-tag v-for="tag in selectedPost.tags" :key="tag" style="margin-right: 5px;">
            {{ tag }}
          </el-tag>
        </div>
      </div>
    </el-dialog>

    <!-- 审核对话框 -->
    <el-dialog v-model="auditDialogVisible" title="帖子审核" width="500px">
      <el-form :model="auditForm" label-width="100px">
        <el-form-item label="审核结果">
          <el-radio-group v-model="auditForm.auditStatus">
            <el-radio label="1">通过</el-radio>
            <el-radio label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="审核备注">
          <el-input v-model="auditForm.auditRemark" 
                    type="textarea" 
                    :rows="4" 
                    placeholder="请输入审核备注（可选）">
          </el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit">确定</el-button>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getPage, getDetail, add, update, remove} from "@/api/ad/post";
  import {batchAudit} from "@/api/ad/auditpost";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        search: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        contentDialogVisible: false,
        selectedPost: null,
        auditDialogVisible: false,
        auditForm: {
          postIds: null,
          auditStatus: '1',
          auditRemark: ''
        },
        option: {
          height: 'auto',
          calcHeight: 210,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          selection: true,
          editBtn: false,
          menu: true,
          column: [
            {
              label: "内容",
              prop: "content",
              type: "textarea",
              // 显示部分
              overHidden: true,
              span: 24,
              slot: true
            },
            {
              label: "分类",
              prop: "categoryName",
              search: true
            },
            {
              label: "发布者",
              prop: "nickname",
              search: true
            },
            // {
            //   label: "状态",
            //   prop: "status",
            //   type: "select",
            //   dicData: [
            //     { label: '正常', value: 1 },
            //     { label: '禁用', value: 0 }
            //   ],
            //   search: true,
            //   slot: true
            // },
            {
              label: "审核状态",
              prop: "auditStatus",
              type: "select",
              dicData: [
                { label: '待审核', value: '0' },
                { label: '已通过', value: '1' },
                { label: '已拒绝', value: '2' }
              ],
              search: true,
              slot: true
            },
            {
              label: "图片",
              prop: "images",
              slot: true,
              search: false
            },
            {
              label: "标签",
              prop: "tags",
              width: 200,
              slot: true,
              search: false
            },
            {
              label: "浏览量",
              prop: "viewCount",
              type: "number"
            },
            {
              label: "点赞数",
              prop: "likeCount",
              type: "number"
            },
            {
              label: "反馈数",
              prop: "feedbackCount",
              type: "number"
            },
            {
              label: "发布时间",
              prop: "publishTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              search: true,
              searchSpan: 12
            },
            {
              label: "更新时间",
              prop: "updateTime",
              type: "datetime",
              format: "YYYY-MM-DD HH:mm:ss",
              valueFormat: "YYYY-MM-DD HH:mm:ss",
              hide: true
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.validData(this.permission.post_add, false),
          viewBtn: this.validData(this.permission.post_view, false),
          delBtn: this.validData(this.permission.post_delete, false),
          editBtn: this.validData(this.permission.post_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      getImageList(images) {
        if (!images) return [];
        if (Array.isArray(images)) return images;
        return images.split(',').filter(img => img.trim() !== '');
      },
      getStatusType(status) {
        return status === 1 ? 'success' : 'danger';
      },
      getStatusText(status) {
        return status === 1 ? '正常' : '禁用';
      },
      getAuditStatusType(status) {
        const statusMap = {
          '0': 'warning',
          '1': 'success',
          '2': 'danger'
        };
        return statusMap[status] || 'info';
      },
      getAuditStatusText(status) {
        const statusMap = {
          '0': '待审核',
          '1': '已通过',
          '2': '已拒绝'
        };
        return statusMap[status] || '未知';
      },
      viewContent(row) {
        this.selectedPost = row;
        this.contentDialogVisible = true;
      },
              handleAudit(row, status) {
          this.auditForm.postIds = row.id;
          this.auditForm.auditStatus = status;
          this.auditForm.auditRemark = '';
          this.auditDialogVisible = true;
        },
        handleBatchAudit() {
          if (this.selectionList.length === 0) {
            this.$message.warning("请选择至少一条数据");
            return;
          }
          this.auditForm.postIds = this.ids;
          this.auditForm.auditStatus = '1';
          this.auditForm.auditRemark = '';
          this.auditDialogVisible = true;
        },
        submitAudit() {
            batchAudit(this.auditForm).then(() => {
              this.auditDialogVisible = false;
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "审核操作成功!"
              });
            }).catch(error => {
              this.$message.error("审核操作失败");
            });
        },
      rowSave(row, done, loading) {
        add(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowUpdate(row, index, done, loading) {
        update(row).then(() => {
          done();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
          loading();
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      handleRefresh() {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "刷新成功!"
        });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getPage(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = Number(data.total);
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
