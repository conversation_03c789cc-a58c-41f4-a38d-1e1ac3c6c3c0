# 标签管理与反馈标签管理 后端接口开发文档

## 一、标签管理接口

### 1. 获取分类下的标签
- **接口**：`GET /blade-ad/category/admin/{categoryId}/tags`
- **参数**：
  - `categoryId`（路径参数）：分类ID
- **返回值**：
  - `200 OK`，`data` 为标签数组
  - 标签结构：
    ```json
    {
      "id": 1,
      "tagName": "标签名",
      "color": "#1890ff",
      "description": "描述",
      "sortOrder": 0,
      "status": 1,
      "useCount": 12
    }
    ```

### 2. 获取所有可用标签
- **接口**：`GET /blade-ad/category/admin/tags/available`
- **返回值**：
  - `200 OK`，`data` 为标签数组，结构同上

### 3. 为分类添加标签
- **接口**：`POST /blade-ad/category/admin/{categoryId}/tags?tagId={tagId}`
- **参数**：
  - `categoryId`（路径参数）：分类ID
  - `tagId`（查询参数）：标签ID
- **返回值**：
  - `200 OK`，`data: true/false`

### 4. 从分类移除标签
- **接口**：`DELETE /blade-ad/category/admin/{categoryId}/tags/{tagId}`
- **参数**：
  - `categoryId`（路径参数）：分类ID
  - `tagId`（路径参数）：标签ID
- **返回值**：
  - `200 OK`，`data: true/false`

### 5. 创建标签并添加到分类
- **接口**：`POST /blade-ad/category/admin/{categoryId}/tags/create`
- **参数**：
  - `categoryId`（路径参数）：分类ID
  - `body`（JSON）：
    ```json
    {
      "tagName": "标签名",
      "color": "#1890ff",
      "description": "描述",
      "sortOrder": 0
    }
    ```
- **返回值**：
  - `200 OK`，`data` 为新建标签对象

### 6. 编辑标签
- **接口**：`PUT /blade-ad/tag/update`
- **参数**：
  - `body`（JSON）：
    ```json
    {
      "id": 1,
      "tagName": "标签名",
      "color": "#1890ff",
      "description": "描述",
      "sortOrder": 0,
      "status": 1
    }
    ```
- **返回值**：
  - `200 OK`，`data: true/false`

---

## 二、反馈标签管理接口

### 1. 获取分类下的反馈标签
- **接口**：`GET /blade-ad/category/admin/{categoryId}/feedback-tags`
- **参数**：
  - `categoryId`（路径参数）：分类ID
- **返回值**：
  - `200 OK`，`data` 为反馈标签数组
  - 反馈标签结构：
    ```json
    {
      "id": 1,
      "label": "反馈标签名",
      "color": "#ff4d4f",
      "description": "描述",
      "sortOrder": 0,
      "status": 1,
      "useCount": 5
    }
    ```

### 2. 获取所有可用反馈标签
- **接口**：`GET /blade-ad/category/admin/feedback-tags/available`
- **返回值**：
  - `200 OK`，`data` 为反馈标签数组，结构同上

### 3. 为分类添加反馈标签
- **接口**：`POST /blade-ad/category/admin/{categoryId}/feedback-tags?tagId={tagId}`
- **参数**：
  - `categoryId`（路径参数）：分类ID
  - `tagId`（查询参数）：反馈标签ID
- **返回值**：
  - `200 OK`，`data: true/false`

### 4. 从分类移除反馈标签
- **接口**：`DELETE /blade-ad/category/admin/{categoryId}/feedback-tags/{tagId}`
- **参数**：
  - `categoryId`（路径参数）：分类ID
  - `tagId`（路径参数）：反馈标签ID
- **返回值**：
  - `200 OK`，`data: true/false`

### 5. 创建反馈标签并添加到分类
- **接口**：`POST /blade-ad/category/admin/{categoryId}/feedback-tags/create`
- **参数**：
  - `categoryId`（路径参数）：分类ID
  - `body`（JSON）：
    ```json
    {
      "tagName": "反馈标签名",
      "color": "#ff4d4f",
      "description": "描述",
      "sortOrder": 0
    }
    ```
- **返回值**：
  - `200 OK`，`data` 为新建反馈标签对象

### 6. 编辑反馈标签
- **接口**：`PUT /blade-ad/tag/update`
- **参数**：
  - `body`（JSON）：
    ```json
    {
      "id": 1,
      "tagName": "反馈标签名",
      "color": "#ff4d4f",
      "description": "描述",
      "sortOrder": 0,
      "status": 1
    }
    ```
- **返回值**：
  - `200 OK`，`data: true/false`

---

## 三、数据结构说明

- 普通标签和反馈标签建议用 `type` 字段区分（如 1=普通标签，2=反馈标签），否则需保证接口返回结构字段一致。
- `useCount` 字段为标签被使用次数，前端用于展示。
- `status` 字段：1=启用，0=禁用。

## 四、注意事项

- 所有接口均需带上管理员权限。
- 前端调用时需保证分类ID、标签ID参数正确。
- 创建/编辑标签时，`color` 字段建议为16进制颜色值。
- 反馈标签和普通标签的接口风格保持一致，便于前端复用。
- 编辑标签和反馈标签均用 `/blade-ad/tag/update`，需保证后端能区分类型。

## 五、接口适配前端说明

- 前端 `category.vue` 已适配上述接口，调用方式见源码。
- 普通标签和反馈标签的管理弹窗、表单、操作按钮均已对接后端接口。
- 若接口路径有调整，需同步前端调用路径。
