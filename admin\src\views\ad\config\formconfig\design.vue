<template>
  <div class="formdesign-page">
    <div class="formdesign-header">
      <h2>{{ isEdit ? '编辑表单' : '新建表单' }}</h2>
      <div class="formdesign-actions">
        <el-button type="primary" @click="handleSave">保存</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>
    <div class="formdesign-info">
      <el-form :model="meta" label-width="100px" class="meta-form">
        <el-form-item label="表单名称" prop="name" required>
          <el-input v-model="meta.name" placeholder="请输入表单名称" />
        </el-form-item>
        <el-form-item label="分类" prop="categoryId">
          <el-select v-model="meta.categoryId" placeholder="请选择分类" filterable>
            <el-option v-for="item in categoryOptions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="formdesign-designer">
      <EDesigner ref="designerRef" height="600px" />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { EDesigner } from 'epic-designer'
import { ElMessage } from 'element-plus'
import { add, update, getDetail } from '@/api/ad/formconfig'
import { getList as getCategoryList } from '@/api/ad/category' // 修正导入

const route = useRoute()
const router = useRouter()
const isEdit = ref(false)
const designerRef = ref()
const meta = ref({
  name: '',
  categoryId: ''
})
const categoryOptions = ref([])

// 拉取分类列表
const fetchCategoryOptions = async () => {
  try {
    // 只查前100条，实际可根据后端分页调整
    const res = await getCategoryList(1, 100, {})
    const list = res.data?.data?.records || res.data?.data || []
    categoryOptions.value = list.map(item => ({ id: item.id, name: item.name }))
  } catch {
    ElMessage.error('获取分类失败')
  }
}

onMounted(async () => {
  fetchCategoryOptions()
  const id = route.query.id
  if (id) {
    isEdit.value = true
    try {
      const res = await getDetail(id)
      const data = res.data.data
      meta.value.name = data.name
      meta.value.categoryId = data.categoryId
      // 回显表单设计数据
      if (data.configJson) {
        designerRef.value?.setData(JSON.parse(data.configJson))
      }
    } catch {
      ElMessage.error('获取表单配置失败')
    }
  }
})

const handleSave = async () => {
  if (!meta.value.name) {
    ElMessage.warning('请填写表单名称')
    return
  }
  if (!meta.value.categoryId) {
    ElMessage.warning('请选择分类')
    return
  }
  // 获取设计器数据
  const schema = designerRef.value?.getData()
  if (!schema || Object.keys(schema).length === 0) {
    ElMessage.error('表单内容不能为空')
    return
  }
  const payload = {
    name: meta.value.name,
    categoryId: meta.value.categoryId,
    configJson: JSON.stringify(schema)
  }
  if (isEdit.value) {
    payload.id = route.query.id
    await update(payload)
    ElMessage.success('保存成功')
  } else {
    await add(payload)
    ElMessage.success('新建成功')
  }
  router.push({ path: '/ad/config/formconfig/list' })
}

const handleBack = () => {
  router.push({ path: '/ad/config/formconfig/list' })
}
</script>

<style lang="scss" scoped>
.formdesign-page {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
  .formdesign-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    h2 {
      font-size: 22px;
      font-weight: bold;
      color: #333;
    }
    .formdesign-actions {
      display: flex;
      gap: 12px;
    }
  }
  .formdesign-info {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 16px 24px 0 24px;
    margin-bottom: 24px;
    .meta-form {
      max-width: 400px;
    }
  }
  .formdesign-designer {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 16px;
    margin-bottom: 24px;
  }
}
</style>

