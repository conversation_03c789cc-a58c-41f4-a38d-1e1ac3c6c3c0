{"openapi": "3.0.1", "info": {"title": "SpringBlade 接口文档系统", "description": "SpringBlade 接口文档系统", "termsOfService": "https://bladex.cn", "contact": {"name": "smallchill", "url": "https://gitee.com/smallc", "email": "<EMAIL>"}, "license": {"name": "Powered By SpringBlade", "url": "https://bladex.cn"}, "version": "4.4.0"}, "servers": [{"url": "http://localhost", "description": "Generated server url"}], "tags": [{"name": "对象存储端点", "description": "对象存储端点"}, {"name": "文件上传", "description": "文件上传接口"}, {"name": "数据权限", "description": "数据权限"}, {"name": "岗位表", "description": "岗位表接口"}, {"name": "行政区划表", "description": "行政区划表接口"}], "paths": {"/blade-system/region/update": {"post": {"tags": ["行政区划表"], "summary": "修改", "description": "传入region", "operationId": "update", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Region"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 6}}, "/blade-system/region/submit": {"post": {"tags": ["行政区划表"], "summary": "新增或修改", "description": "传入region", "operationId": "submit", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Region"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 7}}, "/blade-system/region/save": {"post": {"tags": ["行政区划表"], "summary": "新增", "description": "传入region", "operationId": "save", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Region"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 5}}, "/blade-system/region/remove": {"post": {"tags": ["行政区划表"], "summary": "删除", "description": "传入主键", "operationId": "remove", "parameters": [{"name": "id", "in": "query", "description": "主键", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 8}}, "/blade-system/post/update": {"post": {"tags": ["岗位表"], "summary": "修改", "description": "传入post", "operationId": "update_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 5}}, "/blade-system/post/submit": {"post": {"tags": ["岗位表"], "summary": "新增或修改", "description": "传入post", "operationId": "submit_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 6}}, "/blade-system/post/save": {"post": {"tags": ["岗位表"], "summary": "新增", "description": "传入post", "operationId": "save_1", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Post"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 4}}, "/blade-system/post/remove": {"post": {"tags": ["岗位表"], "summary": "逻辑删除", "description": "传入ids", "operationId": "remove_1", "parameters": [{"name": "ids", "in": "query", "description": "主键集合", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 7}}, "/blade-system/file-upload/upload": {"post": {"tags": ["文件上传"], "summary": "上传单个文件", "description": "上传单个文件到云存储", "operationId": "uploadFile", "parameters": [{"name": "uploadSource", "in": "query", "description": "上传来源", "required": false, "schema": {"type": "string", "default": "admin"}}, {"name": "businessType", "in": "query", "description": "业务类型", "required": false, "schema": {"type": "string"}}, {"name": "businessId", "in": "query", "description": "业务ID", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RFileUpload"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-system/file-upload/upload-batch": {"post": {"tags": ["文件上传"], "summary": "批量上传文件", "description": "批量上传文件到云存储", "operationId": "uploadFiles", "parameters": [{"name": "uploadSource", "in": "query", "description": "上传来源", "required": false, "schema": {"type": "string", "default": "admin"}}, {"name": "businessType", "in": "query", "description": "业务类型", "required": false, "schema": {"type": "string"}}, {"name": "businessId", "in": "query", "description": "业务ID", "required": false, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"required": ["files"], "type": "object", "properties": {"files": {"type": "array", "description": "文件列表", "items": {"type": "string", "format": "binary"}}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListFileUpload"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-system/file-upload/remove": {"post": {"tags": ["文件上传"], "summary": "删除文件", "description": "删除文件", "operationId": "remove_2", "parameters": [{"name": "ids", "in": "query", "description": "主键集合", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBoolean"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-system/file-upload/miniapp/upload": {"post": {"tags": ["文件上传"], "summary": "小程序上传文件", "description": "小程序上传文件接口", "operationId": "miniappUploadFile", "parameters": [{"name": "businessType", "in": "query", "description": "业务类型", "required": false, "schema": {"type": "string"}}, {"name": "businessId", "in": "query", "description": "业务ID", "required": false, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RFileUpload"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-system/file-upload/miniapp/upload-batch": {"post": {"tags": ["文件上传"], "summary": "小程序批量上传文件", "description": "小程序批量上传文件接口", "operationId": "miniappUploadFiles", "parameters": [{"name": "businessType", "in": "query", "description": "业务类型", "required": false, "schema": {"type": "string"}}, {"name": "businessId", "in": "query", "description": "业务ID", "required": false, "schema": {"type": "integer", "format": "int64"}}], "requestBody": {"content": {"application/json": {"schema": {"required": ["files"], "type": "object", "properties": {"files": {"type": "array", "description": "文件列表", "items": {"type": "string", "format": "binary"}}}}}}}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListFileUpload"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-system/data-scope/update": {"post": {"tags": ["数据权限"], "summary": "修改", "description": "传入dataScope", "operationId": "update_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataScope"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 4}}, "/blade-system/data-scope/submit": {"post": {"tags": ["数据权限"], "summary": "新增或修改", "description": "传入dataScope", "operationId": "submit_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataScope"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 5}}, "/blade-system/data-scope/save": {"post": {"tags": ["数据权限"], "summary": "新增", "description": "传入dataScope", "operationId": "save_2", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DataScope"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 3}}, "/blade-system/data-scope/remove": {"post": {"tags": ["数据权限"], "summary": "逻辑删除", "description": "传入ids", "operationId": "remove_3", "parameters": [{"name": "ids", "in": "query", "description": "主键集合", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 6}}, "/blade-resource/oss/endpoint/remove-files": {"post": {"tags": ["对象存储端点"], "operationId": "removeFiles", "parameters": [{"name": "fileNames", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-resource/oss/endpoint/remove-file": {"post": {"tags": ["对象存储端点"], "operationId": "removeFile", "parameters": [{"name": "fileName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-resource/oss/endpoint/remove-bucket": {"post": {"tags": ["对象存储端点"], "operationId": "removeBucket", "parameters": [{"name": "bucketName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-resource/oss/endpoint/put-file": {"post": {"tags": ["对象存储端点"], "operationId": "putFile", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBladeFile"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-resource/oss/endpoint/put-file-by-name": {"post": {"tags": ["对象存储端点"], "operationId": "putFile_1", "parameters": [{"name": "fileName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RBladeFile"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-resource/oss/endpoint/make-bucket": {"post": {"tags": ["对象存储端点"], "operationId": "makeBucket", "parameters": [{"name": "bucketName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-resource/oss/endpoint/copy-file": {"post": {"tags": ["对象存储端点"], "operationId": "copyFile", "parameters": [{"name": "fileName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "destBucketName", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "destFileName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-system/region/select": {"get": {"tags": ["行政区划表"], "summary": "下拉数据源", "description": "传入tenant", "operationId": "select", "parameters": [{"name": "code", "in": "query", "required": false, "schema": {"type": "string", "default": "00"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListRegion"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 9}}, "/blade-system/region/list": {"get": {"tags": ["行政区划表"], "summary": "分页", "description": "传入region", "operationId": "list", "parameters": [{"name": "code", "in": "query", "description": "区划编号", "required": false, "schema": {"type": "string", "description": "区划编号"}}, {"name": "parentCode", "in": "query", "description": "父区划编号", "required": false, "schema": {"type": "string", "description": "父区划编号"}}, {"name": "ancestors", "in": "query", "description": "祖区划编号", "required": false, "schema": {"type": "string", "description": "祖区划编号"}}, {"name": "name", "in": "query", "description": "区划名称", "required": false, "schema": {"type": "string", "description": "区划名称"}}, {"name": "provinceCode", "in": "query", "description": "省级区划编号", "required": false, "schema": {"type": "string", "description": "省级区划编号"}}, {"name": "provinceName", "in": "query", "description": "省级名称", "required": false, "schema": {"type": "string", "description": "省级名称"}}, {"name": "cityCode", "in": "query", "description": "市级区划编号", "required": false, "schema": {"type": "string", "description": "市级区划编号"}}, {"name": "cityName", "in": "query", "description": "市级名称", "required": false, "schema": {"type": "string", "description": "市级名称"}}, {"name": "districtCode", "in": "query", "description": "区级区划编号", "required": false, "schema": {"type": "string", "description": "区级区划编号"}}, {"name": "districtName", "in": "query", "description": "区级名称", "required": false, "schema": {"type": "string", "description": "区级名称"}}, {"name": "townCode", "in": "query", "description": "镇级区划编号", "required": false, "schema": {"type": "string", "description": "镇级区划编号"}}, {"name": "townName", "in": "query", "description": "镇级名称", "required": false, "schema": {"type": "string", "description": "镇级名称"}}, {"name": "villageCode", "in": "query", "description": "村级区划编号", "required": false, "schema": {"type": "string", "description": "村级区划编号"}}, {"name": "villageName", "in": "query", "description": "村级名称", "required": false, "schema": {"type": "string", "description": "村级名称"}}, {"name": "level", "in": "query", "description": "层级", "required": false, "schema": {"type": "string", "description": "层级"}}, {"name": "sort", "in": "query", "description": "排序", "required": false, "schema": {"type": "string", "description": "排序"}}, {"name": "remark", "in": "query", "description": "备注", "required": false, "schema": {"type": "string", "description": "备注"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageRegion"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 2}}, "/blade-system/region/lazy-tree": {"get": {"tags": ["行政区划表"], "summary": "懒加载列表", "description": "传入menu", "operationId": "lazyTree", "parameters": [{"name": "parentCode", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "code", "in": "query", "description": "区划编号", "schema": {"type": "string"}}, {"name": "name", "in": "query", "description": "区划名称", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListRoleVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 4}}, "/blade-system/region/lazy-list": {"get": {"tags": ["行政区划表"], "summary": "懒加载列表", "description": "传入menu", "operationId": "lazyList", "parameters": [{"name": "parentCode", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "code", "in": "query", "description": "区划编号", "schema": {"type": "string"}}, {"name": "name", "in": "query", "description": "区划名称", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListRoleVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 3}}, "/blade-system/region/detail": {"get": {"tags": ["行政区划表"], "summary": "详情", "description": "传入region", "operationId": "detail", "parameters": [{"name": "code", "in": "query", "description": "区划编号", "required": false, "schema": {"type": "string", "description": "区划编号"}}, {"name": "parentCode", "in": "query", "description": "父区划编号", "required": false, "schema": {"type": "string", "description": "父区划编号"}}, {"name": "ancestors", "in": "query", "description": "祖区划编号", "required": false, "schema": {"type": "string", "description": "祖区划编号"}}, {"name": "name", "in": "query", "description": "区划名称", "required": false, "schema": {"type": "string", "description": "区划名称"}}, {"name": "provinceCode", "in": "query", "description": "省级区划编号", "required": false, "schema": {"type": "string", "description": "省级区划编号"}}, {"name": "provinceName", "in": "query", "description": "省级名称", "required": false, "schema": {"type": "string", "description": "省级名称"}}, {"name": "cityCode", "in": "query", "description": "市级区划编号", "required": false, "schema": {"type": "string", "description": "市级区划编号"}}, {"name": "cityName", "in": "query", "description": "市级名称", "required": false, "schema": {"type": "string", "description": "市级名称"}}, {"name": "districtCode", "in": "query", "description": "区级区划编号", "required": false, "schema": {"type": "string", "description": "区级区划编号"}}, {"name": "districtName", "in": "query", "description": "区级名称", "required": false, "schema": {"type": "string", "description": "区级名称"}}, {"name": "townCode", "in": "query", "description": "镇级区划编号", "required": false, "schema": {"type": "string", "description": "镇级区划编号"}}, {"name": "townName", "in": "query", "description": "镇级名称", "required": false, "schema": {"type": "string", "description": "镇级名称"}}, {"name": "villageCode", "in": "query", "description": "村级区划编号", "required": false, "schema": {"type": "string", "description": "村级区划编号"}}, {"name": "villageName", "in": "query", "description": "村级名称", "required": false, "schema": {"type": "string", "description": "村级名称"}}, {"name": "level", "in": "query", "description": "层级", "required": false, "schema": {"type": "string", "description": "层级"}}, {"name": "sort", "in": "query", "description": "排序", "required": false, "schema": {"type": "string", "description": "排序"}}, {"name": "remark", "in": "query", "description": "备注", "required": false, "schema": {"type": "string", "description": "备注"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RRegionVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 1}}, "/blade-system/post/select": {"get": {"tags": ["岗位表"], "summary": "下拉数据源", "description": "传入post", "operationId": "select_1", "parameters": [{"name": "tenantId", "in": "query", "required": true, "schema": {"type": "string"}}, {"name": "clientId", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "userId", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "deptId", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "userName", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "account", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "roleId", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "<PERSON><PERSON><PERSON>", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListPost"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 8}}, "/blade-system/post/page": {"get": {"tags": ["岗位表"], "summary": "分页", "description": "传入post", "operationId": "page", "parameters": [{"name": "categoryName", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "category", "in": "query", "description": "类型", "required": false, "schema": {"type": "string", "description": "类型"}}, {"name": "postCode", "in": "query", "description": "岗位编号", "required": false, "schema": {"type": "string", "description": "岗位编号"}}, {"name": "postName", "in": "query", "description": "岗位名称", "required": false, "schema": {"type": "string", "description": "岗位名称"}}, {"name": "sort", "in": "query", "description": "岗位排序", "required": false, "schema": {"type": "string", "description": "岗位排序"}}, {"name": "remark", "in": "query", "description": "岗位描述", "required": false, "schema": {"type": "string", "description": "岗位描述"}}, {"name": "tenantId", "in": "query", "description": "租户ID", "required": false, "schema": {"type": "string", "description": "租户ID"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPagePostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 3}}, "/blade-system/post/list": {"get": {"tags": ["岗位表"], "summary": "分页", "description": "传入post", "operationId": "list_1", "parameters": [{"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "category", "in": "query", "description": "类型", "required": false, "schema": {"type": "string", "description": "类型"}}, {"name": "postCode", "in": "query", "description": "岗位编号", "required": false, "schema": {"type": "string", "description": "岗位编号"}}, {"name": "postName", "in": "query", "description": "岗位名称", "required": false, "schema": {"type": "string", "description": "岗位名称"}}, {"name": "sort", "in": "query", "description": "岗位排序", "required": false, "schema": {"type": "string", "description": "岗位排序"}}, {"name": "remark", "in": "query", "description": "岗位描述", "required": false, "schema": {"type": "string", "description": "岗位描述"}}, {"name": "tenantId", "in": "query", "description": "租户ID", "required": false, "schema": {"type": "string", "description": "租户ID"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPagePostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 2}}, "/blade-system/post/detail": {"get": {"tags": ["岗位表"], "summary": "详情", "description": "传入post", "operationId": "detail_1", "parameters": [{"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "category", "in": "query", "description": "类型", "required": false, "schema": {"type": "string", "description": "类型"}}, {"name": "postCode", "in": "query", "description": "岗位编号", "required": false, "schema": {"type": "string", "description": "岗位编号"}}, {"name": "postName", "in": "query", "description": "岗位名称", "required": false, "schema": {"type": "string", "description": "岗位名称"}}, {"name": "sort", "in": "query", "description": "岗位排序", "required": false, "schema": {"type": "string", "description": "岗位排序"}}, {"name": "remark", "in": "query", "description": "岗位描述", "required": false, "schema": {"type": "string", "description": "岗位描述"}}, {"name": "tenantId", "in": "query", "description": "租户ID", "required": false, "schema": {"type": "string", "description": "租户ID"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RPostVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 1}}, "/blade-system/file-upload/stats": {"get": {"tags": ["文件上传"], "summary": "获取统计信息", "description": "获取文件上传统计信息", "operationId": "getStats", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RObject"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-system/file-upload/list": {"get": {"tags": ["文件上传"], "summary": "分页查询", "description": "分页查询文件上传", "operationId": "list_2", "parameters": [{"name": "fileSizeFormatted", "in": "query", "description": "文件大小格式化显示", "required": false, "schema": {"type": "string", "description": "文件大小格式化显示"}}, {"name": "createTimeFormatted", "in": "query", "description": "上传时间格式化显示", "required": false, "schema": {"type": "string", "description": "上传时间格式化显示"}}, {"name": "createUserName", "in": "query", "description": "上传用户名称", "required": false, "schema": {"type": "string", "description": "上传用户名称"}}, {"name": "originalName", "in": "query", "description": "原始文件名", "required": false, "schema": {"type": "string", "description": "原始文件名"}}, {"name": "fileName", "in": "query", "description": "存储文件名", "required": false, "schema": {"type": "string", "description": "存储文件名"}}, {"name": "fileExtension", "in": "query", "description": "文件扩展名", "required": false, "schema": {"type": "string", "description": "文件扩展名"}}, {"name": "fileSize", "in": "query", "description": "文件大小（字节）", "required": false, "schema": {"type": "string", "description": "文件大小（字节）"}}, {"name": "contentType", "in": "query", "description": "文件类型（MIME类型）", "required": false, "schema": {"type": "string", "description": "文件类型（MIME类型）"}}, {"name": "fileCategory", "in": "query", "description": "文件分类", "required": false, "schema": {"type": "string", "description": "文件分类"}}, {"name": "uploadSource", "in": "query", "description": "上传来源", "required": false, "schema": {"type": "string", "description": "上传来源"}}, {"name": "storageProvider", "in": "query", "description": "云存储提供商", "required": false, "schema": {"type": "string", "description": "云存储提供商"}}, {"name": "bucketName", "in": "query", "description": "存储桶名称", "required": false, "schema": {"type": "string", "description": "存储桶名称"}}, {"name": "storagePath", "in": "query", "description": "存储路径", "required": false, "schema": {"type": "string", "description": "存储路径"}}, {"name": "accessUrl", "in": "query", "description": "访问URL", "required": false, "schema": {"type": "string", "description": "访问URL"}}, {"name": "thumbnailUrl", "in": "query", "description": "缩略图URL", "required": false, "schema": {"type": "string", "description": "缩略图URL"}}, {"name": "fileMd5", "in": "query", "description": "文件MD5值", "required": false, "schema": {"type": "string", "description": "文件MD5值"}}, {"name": "status", "in": "query", "description": "文件状态", "required": false, "schema": {"type": "string", "description": "文件状态"}}, {"name": "businessId", "in": "query", "description": "关联业务ID", "required": false, "schema": {"type": "string", "description": "关联业务ID"}}, {"name": "businessType", "in": "query", "description": "关联业务类型", "required": false, "schema": {"type": "string", "description": "关联业务类型"}}, {"name": "remark", "in": "query", "description": "备注信息", "required": false, "schema": {"type": "string", "description": "备注信息"}}, {"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageFileUploadVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-system/file-upload/business": {"get": {"tags": ["文件上传"], "summary": "根据业务查询文件", "description": "根据业务类型和业务ID查询文件", "operationId": "getFilesByBusiness", "parameters": [{"name": "businessType", "in": "query", "description": "业务类型", "required": true, "schema": {"type": "string"}}, {"name": "businessId", "in": "query", "description": "业务ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RListFileUpload"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-system/data-scope/list": {"get": {"tags": ["数据权限"], "summary": "分页", "description": "传入dataScope", "operationId": "list_3", "parameters": [{"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "menuId", "in": "query", "description": "菜单主键", "required": false, "schema": {"type": "string", "description": "菜单主键"}}, {"name": "resourceCode", "in": "query", "description": "资源编号", "required": false, "schema": {"type": "string", "description": "资源编号"}}, {"name": "scopeName", "in": "query", "description": "数据权限名称", "required": false, "schema": {"type": "string", "description": "数据权限名称"}}, {"name": "scopeField", "in": "query", "description": "数据权限可见字段", "required": false, "schema": {"type": "string", "description": "数据权限可见字段"}}, {"name": "scopeClass", "in": "query", "description": "数据权限类名", "required": false, "schema": {"type": "string", "description": "数据权限类名"}}, {"name": "scopeColumn", "in": "query", "description": "数据权限字段", "required": false, "schema": {"type": "string", "description": "数据权限字段"}}, {"name": "scopeType", "in": "query", "description": "数据权限类型", "required": false, "schema": {"type": "string", "description": "数据权限类型"}}, {"name": "scopeValue", "in": "query", "description": "数据权限值域", "required": false, "schema": {"type": "string", "description": "数据权限值域"}}, {"name": "remark", "in": "query", "description": "数据权限备注", "required": false, "schema": {"type": "string", "description": "数据权限备注"}}, {"name": "current", "in": "query", "description": "当前页", "required": false, "schema": {"type": "string", "description": "当前页"}}, {"name": "size", "in": "query", "description": "每页的数量", "required": false, "schema": {"type": "string", "description": "每页的数量"}}, {"name": "ascs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}, {"name": "descs", "in": "query", "required": false, "schema": {"type": "string", "readOnly": true}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RIPageDataScopeVO"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 2}}, "/blade-system/data-scope/detail": {"get": {"tags": ["数据权限"], "summary": "详情", "description": "传入dataScope", "operationId": "detail_2", "parameters": [{"name": "id", "in": "query", "description": "主键", "required": false, "schema": {"type": "string", "description": "主键"}}, {"name": "menuId", "in": "query", "description": "菜单主键", "required": false, "schema": {"type": "string", "description": "菜单主键"}}, {"name": "resourceCode", "in": "query", "description": "资源编号", "required": false, "schema": {"type": "string", "description": "资源编号"}}, {"name": "scopeName", "in": "query", "description": "数据权限名称", "required": false, "schema": {"type": "string", "description": "数据权限名称"}}, {"name": "scopeField", "in": "query", "description": "数据权限可见字段", "required": false, "schema": {"type": "string", "description": "数据权限可见字段"}}, {"name": "scopeClass", "in": "query", "description": "数据权限类名", "required": false, "schema": {"type": "string", "description": "数据权限类名"}}, {"name": "scopeColumn", "in": "query", "description": "数据权限字段", "required": false, "schema": {"type": "string", "description": "数据权限字段"}}, {"name": "scopeType", "in": "query", "description": "数据权限类型", "required": false, "schema": {"type": "string", "description": "数据权限类型"}}, {"name": "scopeValue", "in": "query", "description": "数据权限值域", "required": false, "schema": {"type": "string", "description": "数据权限值域"}}, {"name": "remark", "in": "query", "description": "数据权限备注", "required": false, "schema": {"type": "string", "description": "数据权限备注"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RDataScope"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}, "x-order": 1}}, "/blade-resource/oss/endpoint/stat-file": {"get": {"tags": ["对象存储端点"], "operationId": "statFile", "parameters": [{"name": "fileName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ROssFile"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-resource/oss/endpoint/file-path": {"get": {"tags": ["对象存储端点"], "operationId": "filePath", "parameters": [{"name": "fileName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RString"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}, "/blade-resource/oss/endpoint/file-link": {"get": {"tags": ["对象存储端点"], "operationId": "fileLink", "parameters": [{"name": "fileName", "in": "query", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/RString"}}}}, "400": {"description": "Bad Request", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "401": {"description": "Unauthorized", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "405": {"description": "Method Not Allowed", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "415": {"description": "Unsupported Media Type", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}, "500": {"description": "Internal Server Error", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/R"}}}}}}}}, "components": {"schemas": {"R": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "object", "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "Region": {"type": "object", "properties": {"code": {"type": "string", "description": "区划编号"}, "parentCode": {"type": "string", "description": "父区划编号"}, "ancestors": {"type": "string", "description": "祖区划编号"}, "name": {"type": "string", "description": "区划名称"}, "provinceCode": {"type": "string", "description": "省级区划编号"}, "provinceName": {"type": "string", "description": "省级名称"}, "cityCode": {"type": "string", "description": "市级区划编号"}, "cityName": {"type": "string", "description": "市级名称"}, "districtCode": {"type": "string", "description": "区级区划编号"}, "districtName": {"type": "string", "description": "区级名称"}, "townCode": {"type": "string", "description": "镇级区划编号"}, "townName": {"type": "string", "description": "镇级名称"}, "villageCode": {"type": "string", "description": "村级区划编号"}, "villageName": {"type": "string", "description": "村级名称"}, "level": {"type": "integer", "description": "层级", "format": "int32"}, "sort": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"type": "string", "description": "备注"}}, "description": "Region对象"}, "Post": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "tenantId": {"type": "string", "description": "租户ID"}, "category": {"type": "integer", "description": "类型", "format": "int32"}, "postCode": {"type": "string", "description": "岗位编号"}, "postName": {"type": "string", "description": "岗位名称"}, "sort": {"type": "integer", "description": "岗位排序", "format": "int32"}, "remark": {"type": "string", "description": "岗位描述"}}, "description": "Post对象"}, "FileUpload": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64", "refType": null}, "status": {"type": "integer", "description": "文件状态", "format": "int32", "refType": null}, "originalName": {"type": "string", "description": "原始文件名", "refType": null}, "fileName": {"type": "string", "description": "存储文件名", "refType": null}, "fileExtension": {"type": "string", "description": "文件扩展名", "refType": null}, "fileSize": {"type": "integer", "description": "文件大小（字节）", "format": "int64", "refType": null}, "contentType": {"type": "string", "description": "文件类型（MIME类型）", "refType": null}, "fileCategory": {"type": "string", "description": "文件分类", "refType": null}, "uploadSource": {"type": "string", "description": "上传来源", "refType": null}, "storageProvider": {"type": "string", "description": "云存储提供商", "refType": null}, "bucketName": {"type": "string", "description": "存储桶名称", "refType": null}, "storagePath": {"type": "string", "description": "存储路径", "refType": null}, "accessUrl": {"type": "string", "description": "访问URL", "refType": null}, "thumbnailUrl": {"type": "string", "description": "缩略图URL", "refType": null}, "fileMd5": {"type": "string", "description": "文件MD5值", "refType": null}, "businessId": {"type": "integer", "description": "关联业务ID", "format": "int64", "refType": null}, "businessType": {"type": "string", "description": "关联业务类型", "refType": null}, "remark": {"type": "string", "description": "备注信息", "refType": null}}, "description": "文件上传"}, "RFileUpload": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/FileUpload"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RListFileUpload": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "array", "description": "承载数据", "items": {"$ref": "#/components/schemas/FileUpload"}}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RBoolean": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "boolean", "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "DataScope": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "menuId": {"type": "integer", "description": "菜单主键", "format": "int64"}, "resourceCode": {"type": "string", "description": "资源编号"}, "scopeName": {"type": "string", "description": "数据权限名称"}, "scopeField": {"type": "string", "description": "数据权限可见字段"}, "scopeClass": {"type": "string", "description": "数据权限类名"}, "scopeColumn": {"type": "string", "description": "数据权限字段"}, "scopeType": {"type": "integer", "description": "数据权限类型", "format": "int32"}, "scopeValue": {"type": "string", "description": "数据权限值域"}, "remark": {"type": "string", "description": "数据权限备注"}}, "description": "DataScope对象"}, "BladeFile": {"type": "object", "properties": {"link": {"type": "string"}, "domain": {"type": "string"}, "name": {"type": "string"}, "originalName": {"type": "string"}}, "description": "承载数据"}, "RBladeFile": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/BladeFile"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RListRegion": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "array", "description": "承载数据", "items": {"$ref": "#/components/schemas/Region"}}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "IPageRegion": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "current": {"type": "integer", "format": "int64"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/Region"}}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int64", "deprecated": true}}, "description": "承载数据"}, "RIPageRegion": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/IPageRegion"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RListRoleVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "array", "description": "承载数据", "items": {"$ref": "#/components/schemas/RoleVO"}}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RoleVO": {"type": "object", "properties": {"id": {"type": "integer", "format": "int64"}, "tenantId": {"type": "string", "description": "租户ID"}, "parentId": {"type": "integer", "format": "int64"}, "roleName": {"type": "string", "description": "角色名"}, "sort": {"type": "integer", "description": "排序", "format": "int32"}, "roleAlias": {"type": "string", "description": "角色别名"}, "isDeleted": {"type": "integer", "description": "是否已删除", "format": "int32"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/RoleVO"}}, "parentName": {"type": "string"}, "hasChildren": {"type": "boolean"}}, "description": "RoleVO对象"}, "RRegionVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/RegionVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RegionVO": {"type": "object", "properties": {"code": {"type": "string", "description": "区划编号"}, "parentCode": {"type": "string", "description": "父区划编号"}, "ancestors": {"type": "string", "description": "祖区划编号"}, "name": {"type": "string", "description": "区划名称"}, "provinceCode": {"type": "string", "description": "省级区划编号"}, "provinceName": {"type": "string", "description": "省级名称"}, "cityCode": {"type": "string", "description": "市级区划编号"}, "cityName": {"type": "string", "description": "市级名称"}, "districtCode": {"type": "string", "description": "区级区划编号"}, "districtName": {"type": "string", "description": "区级名称"}, "townCode": {"type": "string", "description": "镇级区划编号"}, "townName": {"type": "string", "description": "镇级名称"}, "villageCode": {"type": "string", "description": "村级区划编号"}, "villageName": {"type": "string", "description": "村级名称"}, "level": {"type": "integer", "description": "层级", "format": "int32"}, "sort": {"type": "integer", "description": "排序", "format": "int32"}, "remark": {"type": "string", "description": "备注"}, "id": {"type": "integer", "format": "int64"}, "parentId": {"type": "integer", "format": "int64"}, "parentName": {"type": "string"}, "hasChildren": {"type": "boolean"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/RegionVO"}}}, "description": "RegionVO对象"}, "RListPost": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "array", "description": "承载数据", "items": {"$ref": "#/components/schemas/Post"}}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "IPagePostVO": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "current": {"type": "integer", "format": "int64"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/PostVO"}}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int64", "deprecated": true}}, "description": "承载数据"}, "PostVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "tenantId": {"type": "string", "description": "租户ID"}, "category": {"type": "integer", "description": "类型", "format": "int32"}, "postCode": {"type": "string", "description": "岗位编号"}, "postName": {"type": "string", "description": "岗位名称"}, "sort": {"type": "integer", "description": "岗位排序", "format": "int32"}, "remark": {"type": "string", "description": "岗位描述"}, "categoryName": {"type": "string"}}, "description": "PostVO对象"}, "RIPagePostVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/IPagePostVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RPostVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/PostVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RObject": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "object", "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "FileUploadVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "status": {"type": "integer", "description": "文件状态", "format": "int32"}, "originalName": {"type": "string", "description": "原始文件名"}, "fileName": {"type": "string", "description": "存储文件名"}, "fileExtension": {"type": "string", "description": "文件扩展名"}, "fileSize": {"type": "integer", "description": "文件大小（字节）", "format": "int64"}, "contentType": {"type": "string", "description": "文件类型（MIME类型）"}, "fileCategory": {"type": "string", "description": "文件分类"}, "uploadSource": {"type": "string", "description": "上传来源"}, "storageProvider": {"type": "string", "description": "云存储提供商"}, "bucketName": {"type": "string", "description": "存储桶名称"}, "storagePath": {"type": "string", "description": "存储路径"}, "accessUrl": {"type": "string", "description": "访问URL"}, "thumbnailUrl": {"type": "string", "description": "缩略图URL"}, "fileMd5": {"type": "string", "description": "文件MD5值"}, "businessId": {"type": "integer", "description": "关联业务ID", "format": "int64"}, "businessType": {"type": "string", "description": "关联业务类型"}, "remark": {"type": "string", "description": "备注信息"}, "fileSizeFormatted": {"type": "string", "description": "文件大小格式化显示"}, "createTimeFormatted": {"type": "string", "description": "上传时间格式化显示"}, "createUserName": {"type": "string", "description": "上传用户名称"}}, "description": "文件上传"}, "IPageFileUploadVO": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "current": {"type": "integer", "format": "int64"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/FileUploadVO"}}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int64", "deprecated": true}}, "description": "承载数据"}, "RIPageFileUploadVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/IPageFileUploadVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "DataScopeVO": {"type": "object", "properties": {"id": {"type": "integer", "description": "主键", "format": "int64"}, "menuId": {"type": "integer", "description": "菜单主键", "format": "int64"}, "resourceCode": {"type": "string", "description": "资源编号"}, "scopeName": {"type": "string", "description": "数据权限名称"}, "scopeField": {"type": "string", "description": "数据权限可见字段"}, "scopeClass": {"type": "string", "description": "数据权限类名"}, "scopeColumn": {"type": "string", "description": "数据权限字段"}, "scopeType": {"type": "integer", "description": "数据权限类型", "format": "int32"}, "scopeValue": {"type": "string", "description": "数据权限值域"}, "remark": {"type": "string", "description": "数据权限备注"}, "scopeTypeName": {"type": "string"}}, "description": "DataScopeVO对象"}, "IPageDataScopeVO": {"type": "object", "properties": {"size": {"type": "integer", "format": "int64"}, "current": {"type": "integer", "format": "int64"}, "records": {"type": "array", "items": {"$ref": "#/components/schemas/DataScopeVO"}}, "total": {"type": "integer", "format": "int64"}, "pages": {"type": "integer", "format": "int64", "deprecated": true}}, "description": "承载数据"}, "RIPageDataScopeVO": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/IPageDataScopeVO"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RDataScope": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/DataScope"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "OssFile": {"type": "object", "properties": {"link": {"type": "string"}, "name": {"type": "string"}, "hash": {"type": "string"}, "length": {"type": "integer", "format": "int64"}, "putTime": {"type": "string", "format": "date-time"}, "contentType": {"type": "string"}}, "description": "承载数据"}, "ROssFile": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"$ref": "#/components/schemas/OssFile"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}, "RString": {"type": "object", "properties": {"code": {"type": "integer", "description": "状态码", "format": "int32"}, "success": {"type": "boolean", "description": "是否成功"}, "data": {"type": "string", "description": "承载数据"}, "msg": {"type": "string", "description": "返回消息"}}, "description": "返回信息"}}, "securitySchemes": {"Blade-Auth": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "<PERSON>-<PERSON><PERSON>", "in": "header"}, "Authorization": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}, "Tenant-Id": {"type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Tenant-Id", "in": "header"}}}, "x-openapi": {"x-setting": {"customCode": 200, "language": "zh-CN", "enableSwaggerModels": true, "swaggerModelName": "Swagger Models", "enableReloadCacheParameter": false, "enableAfterScript": true, "enableDocumentManage": true, "enableVersion": false, "enableRequestCache": true, "enableFilterMultipartApis": false, "enableFilterMultipartApiMethodType": "POST", "enableHost": false, "enableHostText": "http://localhost", "enableDynamicParameter": false, "enableDebug": true, "enableFooter": false, "enableFooterCustom": true, "footerCustomContent": "Copyright © 2024 SpringBlade All Rights Reserved", "enableSearch": true, "enableOpenApi": true, "enableHomeCustom": false, "enableGroup": true, "enableResponseCode": true}, "x-markdownFiles": []}}