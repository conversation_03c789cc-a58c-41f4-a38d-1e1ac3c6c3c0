<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="org.springblade.business.group.mapper.GroupCategoryMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="groupCategoryResultMap" type="org.springblade.business.group.entity.GroupCategory">
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_user" property="createUser"/>
        <result column="update_user" property="updateUser"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="category_name" property="categoryName"/>
        <result column="category_image" property="categoryImage"/>
        <result column="sort_order" property="sortOrder"/>
    </resultMap>


    <select id="selectGroupCategoryPage" resultMap="groupCategoryResultMap">
        select * from urb_group_category where is_deleted = 0
        order by sort_order asc,create_time desc
    </select>

</mapper>
