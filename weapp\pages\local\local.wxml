<!-- 使用layout组件 -->
<custom-nav
  id="custom-nav"
  background="linear-gradient(to bottom, #ff6b6b, #ff8585)"
  text-color="#ffffff"
  show-location="{{false}}"
  show-back="{{false}}"
  show-search="{{false}}"
  bind:navReady="onNavReady"
  bind:showDrawer="onShowDrawer"
  bind:regionpickershow="onRegionPickerShow">
</custom-nav>

<!-- 分类标签吸顶 -->
<view wx:if="{{showStickyCategory}}" class="sticky-category-bar" style="top: {{navBarHeight}}px;">
  <scroll-view scroll-x class="category-tabs-scroll" show-scrollbar="false">
    <view class="category-tabs {{categories.length <= 1 ? 'single-tab' : ''}}">
      <view
        class="tab-item {{selectedCategory === item.id ? 'active' : ''}}"
        wx:for="{{categories}}"
        wx:key="id"
        data-index="{{index}}"
        bindtap="switchTab">
        {{item.name}}
      </view>
    </view>
  </scroll-view>
</view>

<scroll-view
  scroll-y
  bindscroll="onScroll"
  class="scroll-container main-scroll-view"
  style="height: calc(100vh - {{navBarHeight}}px);"
  scroll-into-view="{{scrollIntoView}}"
  enable-back-to-top="{{!reachedBottom}}"
  scroll-with-animation="{{true}}"
>
  <view class="main-content">
    <!-- 你的内容区域 -->
    <view class="card-section" id="category-section">
      <card>
        <!-- 主Tab区域：最新/附近 + 申请入驻 -->
        <view class="main-tab-section">
          <view class="main-tabs">
            <view
              wx:for="{{['最新', '附近']}}"
              wx:key="index"
              class="main-tab-item {{currentTab === index ? 'active' : ''}}"
              data-index="{{index}}"
              bindtap="switchMainTab"
            >
              {{item}}
            </view>
          </view>
          <view class="apply-settle-btn" bindtap="onApplySettle">
            申请入驻
          </view>
        </view>
        <!-- 分类筛选 -->
        <scroll-view scroll-x class="category-tabs-scroll" show-scrollbar="false" id="category-tabs-scroll">
          <view class="category-tabs {{categories.length <= 1 ? 'single-tab' : ''}}">
            <view
              class="tab-item {{selectedCategory === item.id ? 'active' : ''}}"
              wx:for="{{categories}}"
              wx:key="id"
              data-index="{{index}}"
              bindtap="switchTab">
              {{item.name}}
            </view>
          </view>
        </scroll-view>
        <view class="institutions-container">
          <!-- 机构列表 -->
          <block wx:for="{{institutionList}}" wx:key="id">
            <institution-item institution="{{item}}" bind:tap="onInstitutionTap" />
          </block>
          <!-- 底部状态区域 -->
          <view class="bottom-status">
            <!-- 空数据状态 -->
            <view wx:if="{{!loading && !isPreloading && institutionList.length === 0}}" class="empty-state">
              <text class="empty-icon">🏢</text>
              <text class="empty-text">暂无机构信息</text>
              <text class="empty-tip">事事有着落，件件有回音</text>
            </view>

            <!-- 预加载状态 -->
            <view wx:elif="{{isPreloading && institutionList.length > 0}}" class="loading-more preloading">
              <text>智能预加载中...</text>
            </view>

            <!-- 初始加载中状态 -->
            <view wx:elif="{{loading && institutionList.length === 0}}" class="loading-more">
              <text>加载中...</text>
            </view>

            <!-- 加载更多状态 -->
            <view wx:elif="{{loading && institutionList.length > 0}}" class="loading-more">
              <text>加载更多...</text>
            </view>

            <!-- 已到达底部状态 -->
            <view wx:elif="{{reachedBottom && institutionList.length > 0}}" class="reached-bottom">
              <text>🏢 已显示全部机构</text>
            </view>

            <!-- 没有更多数据状态 -->
            <view wx:elif="{{!hasMore && institutionList.length > 0 && !loading && !isPreloading}}" class="no-more">
              <text>🏢 已显示全部机构</text>
            </view>
          </view>
        </view>
      </card>
    </view>
  </view>
</scroll-view>
<custom-tabbar id="customTabbar" show="{{showTabbar}}" />
