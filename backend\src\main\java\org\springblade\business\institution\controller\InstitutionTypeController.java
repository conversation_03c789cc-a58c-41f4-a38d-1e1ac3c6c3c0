/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.institution.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.business.institution.entity.InstitutionType;
import org.springblade.business.institution.service.IInstitutionTypeService;
import org.springblade.business.institution.vo.InstitutionTypeVO;
import org.springblade.business.institution.wrapper.InstitutionTypeWrapper;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.core.boot.ctrl.BladeController;

/**
 * 机构分类表 控制器
 *
 * <AUTHOR>
 * @since 2025-07-30
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/institutiontype")
@io.swagger.v3.oas.annotations.tags.Tag(name = "机构分类表", description = "机构分类表接口")
public class InstitutionTypeController extends BladeController {

	private IInstitutionTypeService institutionTypeService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入institutionType")
	public R<InstitutionTypeVO> detail(InstitutionType institutionType) {
		InstitutionType detail = institutionTypeService.getOne(Condition.getQueryWrapper(institutionType));
		return R.data(InstitutionTypeWrapper.build().entityVO(detail));
	}

	/**
	 * 分页 机构分类表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入institutionType")
	public R<IPage<InstitutionTypeVO>> list(InstitutionType institutionType, Query query) {
		IPage<InstitutionType> pages = institutionTypeService.page(Condition.getPage(query), Condition.getQueryWrapper(institutionType));
		return R.data(InstitutionTypeWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页 机构分类表
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入institutionType")
	public R<IPage<InstitutionTypeVO>> page(InstitutionTypeVO institutionType, Query query) {
		IPage<InstitutionTypeVO> pages = institutionTypeService.selectInstitutionTypePage(Condition.getPage(query), institutionType);
		return R.data(pages);
	}

	/**
	 * 新增 机构分类表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入institutionType")
	public R save(@Valid @RequestBody InstitutionType institutionType) {
		return R.status(institutionTypeService.save(institutionType));
	}

	/**
	 * 修改 机构分类表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入institutionType")
	public R update(@Valid @RequestBody InstitutionType institutionType) {
		return R.status(institutionTypeService.updateById(institutionType));
	}

	/**
	 * 新增或修改 机构分类表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入institutionType")
	public R submit(@Valid @RequestBody InstitutionType institutionType) {
		return R.status(institutionTypeService.saveOrUpdate(institutionType));
	}


	/**
	 * 删除 机构分类表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		return R.status(institutionTypeService.deleteLogic(Func.toLongList(ids)));
	}


}
