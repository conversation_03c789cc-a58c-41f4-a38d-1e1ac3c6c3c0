import request from '@/axios'

// 获取表单配置分页列表
export function getFormConfigList(params) {
  return request({
    url: '/blade-ad/formconfig/list',
    method: 'get',
    params
  })
}

// 获取单个表单配置详情
export function getFormConfig(params) {
  return request({
    url: '/blade-ad/formconfig/detail',
    method: 'get',
    params
  })
}

// 新增表单配置
export function addFormConfig(data) {
  return request({
    url: '/blade-ad/formconfig/save',
    method: 'post',
    data
  })
}

// 更新表单配置
export function updateFormConfig(data) {
  return request({
    url: '/blade-ad/formconfig/update',
    method: 'post',
    data
  })
}

// 新增或修改表单配置
export function submitFormConfig(data) {
  return request({
    url: '/blade-ad/formconfig/submit',
    method: 'post',
    data
  })
}

// 删除表单配置（逻辑删除，传ids字符串）
export function deleteFormConfig(ids) {
  return request({
    url: '/blade-ad/formconfig/remove',
    method: 'post',
    params: { ids }
  })
} 