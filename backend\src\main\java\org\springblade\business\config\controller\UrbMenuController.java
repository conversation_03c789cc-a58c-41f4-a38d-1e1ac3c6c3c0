/**
 * Copyright (c) 2018-2099, <PERSON><PERSON> 庄骞 (<PERSON><PERSON><PERSON>@qq.com).
 * <p>
 * Licensed under the GNU LESSER GENERAL PUBLIC LICENSE 3.0;
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.gnu.org/licenses/lgpl.html
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.controller;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import jakarta.validation.Valid;

import org.springblade.common.anno.AccessLimit;
import org.springblade.core.mp.support.Condition;
import org.springblade.core.mp.support.Query;
import org.springblade.core.tool.api.R;
import org.springblade.core.tool.utils.Func;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.bind.annotation.RequestParam;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springblade.business.config.entity.UrbMenu;
import org.springblade.business.config.vo.UrbMenuVO;
import org.springblade.business.config.wrapper.UrbMenuWrapper;
import org.springblade.business.config.service.IUrbMenuService;
import org.springblade.core.boot.ctrl.BladeController;

/**
 *  控制器
 *
 * <AUTHOR>
 * @since 2025-07-03
 */
@RestController
@AllArgsConstructor
@RequestMapping("/blade-ad/urbmenu")
@io.swagger.v3.oas.annotations.tags.Tag(name = "菜单管理", description = "接口")
public class UrbMenuController extends BladeController {

	private IUrbMenuService urbMenuService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")

	@ApiOperationSupport(order = 1)
	@Operation(summary = "详情", description = "传入urbMenu")
	public R<UrbMenuVO> detail(UrbMenu urbMenu) {
		UrbMenu detail = urbMenuService.getOne(Condition.getQueryWrapper(urbMenu));
		return R.data(UrbMenuWrapper.build().entityVO(detail));
	}

	/**
	 * 分页
	 */
	@GetMapping("/list")

	@ApiOperationSupport(order = 2)
	@Operation(summary = "分页", description = "传入urbMenu")
	public R<IPage<UrbMenuVO>> list(UrbMenu urbMenu, Query query) {
		IPage<UrbMenu> pages = urbMenuService.page(Condition.getPage(query), Condition.getQueryWrapper(urbMenu));
		return R.data(UrbMenuWrapper.build().pageVO(pages));
	}


	/**
	 * 自定义分页
	 */
	@GetMapping("/page")

	@ApiOperationSupport(order = 3)
	@Operation(summary = "分页", description = "传入urbMenu")
	public R<IPage<UrbMenuVO>> page(UrbMenuVO urbMenu, Query query) {
		IPage<UrbMenuVO> pages = urbMenuService.selectUrbMenuPage(Condition.getPage(query), urbMenu);
		return R.data(pages);
	}

	/**
	 * 新增
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@Operation(summary = "新增", description = "传入urbMenu")
	public R save(@Valid @RequestBody UrbMenu urbMenu) {
		boolean result = urbMenuService.saveMenu(urbMenu);
		return R.status(result);
	}

	/**
	 * 修改
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@Operation(summary = "修改", description = "传入urbMenu")
	public R update(@Valid @RequestBody UrbMenu urbMenu) {
		boolean result = urbMenuService.updateMenuById(urbMenu);
		return R.status(result);
	}

	/**
	 * 新增或修改
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@Operation(summary = "新增或修改", description = "传入urbMenu")
	public R submit(@Valid @RequestBody UrbMenu urbMenu) {
		boolean result = urbMenuService.saveOrUpdateMenu(urbMenu);
		return R.status(result);
	}


	/**
	 * 删除
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@Operation(summary = "逻辑删除", description = "传入ids")
	public R remove(@Parameter(description = "主键集合", required = true) @RequestParam String ids) {
		boolean result = urbMenuService.deleteLogicMenu(ids);
		return R.status(result);
	}


}
