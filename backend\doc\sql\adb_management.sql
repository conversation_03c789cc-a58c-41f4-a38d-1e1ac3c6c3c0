/*
SQLyog Community v13.3.0 (64 bit)
MySQL - 8.4.3 : Database - adb_management

*********************************************************************
*/

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`adb_management` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `adb_management`;

/*Table structure for table `urb_audit_post` */

DROP TABLE IF EXISTS `urb_audit_post`;

CREATE TABLE `urb_audit_post` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `audit_time` datetime(6) DEFAULT NULL COMMENT '审核时间',
  `audit_status` varchar(20) DEFAULT NULL COMMENT '审核状态',
  `audit_user` bigint DEFAULT NULL COMMENT '审核人ID',
  `audit_remark` text COMMENT '审核备注',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审核日志';

/*Table structure for table `urb_audit_rule` */

DROP TABLE IF EXISTS `urb_audit_rule`;

CREATE TABLE `urb_audit_rule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `rule_name` varchar(50) DEFAULT NULL COMMENT '规则名称',
  `keywords` json DEFAULT NULL COMMENT '违禁词列表',
  `max_content_length` int DEFAULT '0' COMMENT '最大内容长度',
  `enable_image_check` tinyint(1) DEFAULT '0' COMMENT '启用图片审核',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='审核规则';

/*Table structure for table `urb_call_log` */

DROP TABLE IF EXISTS `urb_call_log`;

CREATE TABLE `urb_call_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `call_time` datetime(6) DEFAULT NULL COMMENT '拨打时间',
  `duration` int DEFAULT '0' COMMENT '通话时长',
  `call_status` varchar(20) DEFAULT NULL COMMENT '接通状态',
  `name` varchar(32) DEFAULT NULL COMMENT '联系人姓名',
  `phone` varchar(32) DEFAULT NULL COMMENT '联系电话',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='电话记录';

/*Table structure for table `urb_category` */

DROP TABLE IF EXISTS `urb_category`;

CREATE TABLE `urb_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `name` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT NULL COMMENT '上级分类ID',
  `max_images` int DEFAULT '0' COMMENT '最大图片数',
  `allow_tags` json DEFAULT NULL COMMENT '允许的标签',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='广告分类';

/*Table structure for table `urb_contact` */

DROP TABLE IF EXISTS `urb_contact`;

CREATE TABLE `urb_contact` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `wechat` varchar(50) DEFAULT NULL COMMENT '微信账号',
  `contact_type` varchar(20) DEFAULT NULL COMMENT '联系方式类型',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1899472367138168835 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='联系人';

/*Table structure for table `urb_favorite` */

DROP TABLE IF EXISTS `urb_favorite`;

CREATE TABLE `urb_favorite` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `fav_time` datetime(6) DEFAULT NULL COMMENT '收藏时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='收藏记录';

/*Table structure for table `urb_feedback` */

DROP TABLE IF EXISTS `urb_feedback`;

CREATE TABLE `urb_feedback` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `user_id` bigint DEFAULT NULL COMMENT '反馈用户ID',
  `content` text COMMENT '反馈内容',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户反馈';

/*Table structure for table `urb_feedback_tag` */

DROP TABLE IF EXISTS `urb_feedback_tag`;

CREATE TABLE `urb_feedback_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `label` varchar(50) DEFAULT NULL COMMENT '标签名称',
  `description` text COMMENT '标签说明',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='反馈标签';

/*Table structure for table `urb_feedback_tag_relation` */

DROP TABLE IF EXISTS `urb_feedback_tag_relation`;

CREATE TABLE `urb_feedback_tag_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `feedback_id` bigint DEFAULT NULL COMMENT '反馈ID',
  `tag_id` bigint DEFAULT NULL COMMENT '标签ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='反馈-标签关联表';

/*Table structure for table `urb_like` */

DROP TABLE IF EXISTS `urb_like`;

CREATE TABLE `urb_like` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `user_id` bigint DEFAULT NULL COMMENT '操作用户ID',
  `like_time` datetime(6) DEFAULT NULL COMMENT '点赞时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='点赞记录';

/*Table structure for table `urb_post` */

DROP TABLE IF EXISTS `urb_post`;

CREATE TABLE `urb_post` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `content` text COMMENT '内容',
  `images` text COMMENT '图片',
  `address` varchar(255) DEFAULT NULL COMMENT '发布地址',
  `publish_time` datetime(6) DEFAULT NULL COMMENT '发布时间',
  `audit_status` varchar(20) DEFAULT NULL COMMENT '审核状态',
  `geo_location` json DEFAULT NULL COMMENT '地理位置',
  `like_count` int DEFAULT '0' COMMENT '点赞数',
  `favorite_count` int DEFAULT '0' COMMENT '收藏数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='百事通信息贴';

/*Table structure for table `urb_post_category` */

DROP TABLE IF EXISTS `urb_post_category`;

CREATE TABLE `urb_post_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `category_id` bigint DEFAULT NULL COMMENT '分类ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='信息贴-分类关联表';

/*Table structure for table `urb_post_tag` */

DROP TABLE IF EXISTS `urb_post_tag`;

CREATE TABLE `urb_post_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `tag_id` bigint DEFAULT NULL COMMENT '标签ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='信息贴-标签关联表';

/*Table structure for table `urb_report` */

DROP TABLE IF EXISTS `urb_report`;

CREATE TABLE `urb_report` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `user_id` bigint DEFAULT NULL COMMENT '举报用户ID',
  `content` text COMMENT '举报内容',
  `images` text COMMENT '举报图片',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='举报记录';

/*Table structure for table `urb_report_tag` */

DROP TABLE IF EXISTS `urb_report_tag`;

CREATE TABLE `urb_report_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `label` varchar(50) DEFAULT NULL COMMENT '标签名称',
  `description` text COMMENT '标签说明',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='举报标签';

/*Table structure for table `urb_report_tag_relation` */

DROP TABLE IF EXISTS `urb_report_tag_relation`;

CREATE TABLE `urb_report_tag_relation` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `report_id` bigint DEFAULT NULL COMMENT '举报ID',
  `tag_id` bigint DEFAULT NULL COMMENT '标签ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='举报-标签关联表';

/*Table structure for table `urb_tag` */

DROP TABLE IF EXISTS `urb_tag`;

CREATE TABLE `urb_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `tag_name` varchar(50) DEFAULT NULL COMMENT '标签名称',
  `description` text COMMENT '描述说明',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='信息标签';

/*Table structure for table `urb_user` */

DROP TABLE IF EXISTS `urb_user`;

CREATE TABLE `urb_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `mobile` varchar(20) DEFAULT NULL COMMENT '手机号',
  `gender` varchar(10) DEFAULT NULL COMMENT '性别',
  `signature` text COMMENT '个性签名',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1899127984496861187 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户信息';

/*Table structure for table `urb_user_contact` */

DROP TABLE IF EXISTS `urb_user_contact`;

CREATE TABLE `urb_user_contact` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `contact_id` bigint DEFAULT NULL COMMENT '联系人ID',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='用户-联系人关联表';

/*Table structure for table `urb_view_log` */

DROP TABLE IF EXISTS `urb_view_log`;

CREATE TABLE `urb_view_log` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `create_dept` bigint DEFAULT NULL COMMENT '创建部门ID',
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `create_user` bigint DEFAULT NULL COMMENT '创建用户ID',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除（0：未删除，1：已删除）',
  `status` int DEFAULT '1' COMMENT '状态（0：禁用，1：启用）',
  `tenant_id` varchar(6) DEFAULT NULL COMMENT '租户ID',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `update_user` bigint DEFAULT NULL COMMENT '更新用户ID',
  `post_id` bigint DEFAULT NULL COMMENT '信息贴ID',
  `user_id` bigint DEFAULT NULL COMMENT '浏览用户ID',
  `view_time` datetime(6) DEFAULT NULL COMMENT '浏览时间',
  `view_count` int DEFAULT '0' COMMENT '当日计数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci COMMENT='浏览记录';

/*Table structure for table `url_post_contact` */

DROP TABLE IF EXISTS `url_post_contact`;

CREATE TABLE `url_post_contact` (
  `id` bigint DEFAULT NULL,
  `contact_id` bigint DEFAULT NULL,
  `post_id` bigint DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_0900_ai_ci;

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
