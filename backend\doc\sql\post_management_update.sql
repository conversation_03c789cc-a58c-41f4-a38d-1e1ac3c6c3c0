-- 小程序发布功能数据库更新脚本
-- 更新时间: 2025-03-10
-- 说明: 为帖子、分类、标签表添加小程序发布功能所需字段

-- 1. 更新帖子表 urb_post
ALTER TABLE `urb_post`
ADD COLUMN `category_id` bigint(20) NULL COMMENT '分类ID' AFTER `completed`,
ADD COLUMN `miniapp_user_id` bigint(20) NULL COMMENT '小程序用户ID' AFTER `category_id`,
ADD COLUMN `open_id` varchar(100) NULL COMMENT '小程序用户OpenID' AFTER `miniapp_user_id`,
ADD COLUMN `like_count` int(11) DEFAULT 0 COMMENT '点赞数' AFTER `open_id`,
ADD COLUMN `favorite_count` int(11) DEFAULT 0 COMMENT '收藏数' AFTER `like_count`,
ADD COLUMN `view_count` int(11) DEFAULT 0 COMMENT '浏览数' AFTER `favorite_count`,
ADD COLUMN `audit_remark` varchar(500) NULL COMMENT '审核备注' AFTER `view_count`,
ADD COLUMN `audit_time` datetime NULL COMMENT '审核时间' AFTER `audit_remark`,
ADD COLUMN `audit_user_id` bigint(20) NULL COMMENT '审核人ID' AFTER `audit_time`,
ADD COLUMN `reject_reason` varchar(500) NULL COMMENT '拒绝原因' AFTER `audit_user_id`,
ADD COLUMN `enable_audit` int(1) DEFAULT 1 COMMENT '是否启用审核：0-否，1-是' AFTER `reject_reason`;

-- 添加索引
ALTER TABLE `urb_post`
ADD INDEX `idx_category_id` (`category_id`),
ADD INDEX `idx_open_id` (`open_id`),
ADD INDEX `idx_audit_status` (`audit_status`),
ADD INDEX `idx_publish_time` (`publish_time`);

-- 2. 更新分类表 urb_category
ALTER TABLE `urb_category`
ADD COLUMN `icon` varchar(200) NULL COMMENT '分类图标' AFTER `parent_id`,
ADD COLUMN `description` varchar(500) NULL COMMENT '分类描述' AFTER `icon`,
ADD COLUMN `sort` int(11) DEFAULT 0 COMMENT '排序' AFTER `description`,
ADD COLUMN `enabled` int(1) DEFAULT 1 COMMENT '是否启用：0-否，1-是' AFTER `sort`,
ADD COLUMN `enable_audit` int(1) DEFAULT 1 COMMENT '是否启用审核：0-否，1-是' AFTER `enabled`;

-- 添加索引
ALTER TABLE `urb_category`
ADD INDEX `idx_enabled` (`enabled`),
ADD INDEX `idx_sort` (`sort`);

-- 3. 更新标签表 urb_tag
ALTER TABLE `urb_tag`
ADD COLUMN `category_id` bigint(20) NULL COMMENT '分类ID' AFTER `name`,
ADD COLUMN `color` varchar(20) DEFAULT '#1890ff' COMMENT '标签颜色' AFTER `category_id`,
ADD COLUMN `icon` varchar(200) NULL COMMENT '标签图标' AFTER `color`,
ADD COLUMN `sort` int(11) DEFAULT 0 COMMENT '排序' AFTER `icon`,
ADD COLUMN `enabled` int(1) DEFAULT 1 COMMENT '是否启用：0-否，1-是' AFTER `sort`,
ADD COLUMN `use_count` int(11) DEFAULT 0 COMMENT '使用次数' AFTER `enabled`,
ADD COLUMN `is_system` int(1) DEFAULT 0 COMMENT '是否系统标签：0-否，1-是' AFTER `use_count`;

-- 添加索引
ALTER TABLE `urb_tag`
ADD INDEX `idx_category_id` (`category_id`),
ADD INDEX `idx_enabled` (`enabled`),
ADD INDEX `idx_use_count` (`use_count`);

-- 4. 创建点赞表 urb_post_like
CREATE TABLE `urb_post_like` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `post_id` bigint(20) NOT NULL COMMENT '帖子ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户OpenID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_user` (`post_id`, `open_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_open_id` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子点赞表';

-- 5. 创建收藏表 urb_post_favorite
CREATE TABLE `urb_post_favorite` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `post_id` bigint(20) NOT NULL COMMENT '帖子ID',
  `open_id` varchar(100) NOT NULL COMMENT '用户OpenID',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_post_user` (`post_id`, `open_id`),
  KEY `idx_post_id` (`post_id`),
  KEY `idx_open_id` (`open_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='帖子收藏表';


-- 7. 插入默认分类数据
INSERT INTO `urb_category` (`name`, `parent_id`, `icon`, `description`, `sort`, `enabled`, `enable_audit`, `tip`, `create_time`, `update_time`) VALUES
('二手交易', 0, 'icon-second-hand', '二手物品买卖', 1, 1, 1, '请确保物品信息真实有效', NOW(), NOW()),
('房屋租赁', 0, 'icon-house', '房屋出租求租', 2, 1, 1, '请提供详细的房屋信息', NOW(), NOW()),
('求职招聘', 0, 'icon-job', '工作机会发布', 3, 1, 1, '请提供真实的工作信息', NOW(), NOW()),
('生活服务', 0, 'icon-service', '各类生活服务', 4, 1, 1, '请确保服务信息准确', NOW(), NOW()),
('教育培训', 0, 'icon-education', '教育培训信息', 5, 1, 1, '请提供真实的教育信息', NOW(), NOW());

-- 8. 插入默认标签数据
INSERT INTO `urb_tag` (`name`, `category_id`, `color`, `sort`, `enabled`, `use_count`, `is_system`, `create_time`, `update_time`) VALUES
-- 二手交易标签
('数码产品', 1, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('服装鞋帽', 1, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('家居用品', 1, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('图书音像', 1, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('运动户外', 1, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),

-- 房屋租赁标签
('整租', 2, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('合租', 2, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('短租', 2, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('长租', 2, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('精装修', 2, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),

-- 求职招聘标签
('全职', 3, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('兼职', 3, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('实习', 3, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('技术', 3, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('销售', 3, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),

-- 生活服务标签
('家政服务', 4, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('维修服务', 4, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('美容美发', 4, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('餐饮服务', 4, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('物流快递', 4, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),

-- 教育培训标签
('语言培训', 5, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('技能培训', 5, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('学历教育', 5, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('在线课程', 5, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('考证培训', 5, '#722ed1', 5, 1, 0, 1, NOW(), NOW());

-- 9. 更新现有数据的默认值
UPDATE `urb_post` SET `audit_status` = 'APPROVED' WHERE `audit_status` IS NULL;
UPDATE `urb_post` SET `like_count` = 0 WHERE `like_count` IS NULL;
UPDATE `urb_post` SET `favorite_count` = 0 WHERE `favorite_count` IS NULL;
UPDATE `urb_post` SET `view_count` = 0 WHERE `view_count` IS NULL;
UPDATE `urb_post` SET `enable_audit` = 1 WHERE `enable_audit` IS NULL;

UPDATE `urb_category` SET `enabled` = 1 WHERE `enabled` IS NULL;
UPDATE `urb_category` SET `enable_audit` = 1 WHERE `enable_audit` IS NULL;
UPDATE `urb_category` SET `sort` = 0 WHERE `sort` IS NULL;

UPDATE `urb_tag` SET `enabled` = 1 WHERE `enabled` IS NULL;
UPDATE `urb_tag` SET `use_count` = 0 WHERE `use_count` IS NULL;
UPDATE `urb_tag` SET `is_system` = 0 WHERE `is_system` IS NULL;
UPDATE `urb_tag` SET `sort` = 0 WHERE `sort` IS NULL;
UPDATE `urb_tag` SET `color` = '#1890ff' WHERE `color` IS NULL;
