# 小程序发布功能API接口文档

## 1. 接口概述

本文档描述了小程序发布功能的所有API接口，包括小程序端接口和后台管理接口。

### 1.1 基础信息
- **接口版本**: v1.0
- **接口前缀**: `/api`
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 响应格式
```json
{
    "code": 200,
    "success": true,
    "data": {},
    "msg": "操作成功"
}
```

### 1.3 状态码说明
| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 2. 小程序端接口

### 2.1 帖子发布相关

#### 2.1.1 发布帖子
**接口地址**: `POST /miniapp/post/publish`

**请求参数**:
```json
{
    "title": "帖子标题",
    "content": "帖子内容",
    "images": ["图片1", "图片2"],
    "address": "发布地址",
    "geoLocation": "地理位置",
    "tags": ["标签1", "标签2"],
    "contactName": "联系人姓名",
    "contactPhone": "联系电话",
    "categoryId": 1,
    "openId": "用户OpenID",
    "nickName": "用户昵称",
    "avatarUrl": "用户头像"
}
```

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "id": 1,
        "title": "帖子标题",
        "content": "帖子内容",
        "auditStatus": "PENDING",
        "createTime": "2025-03-10 10:00:00"
    },
    "msg": "发布成功"
}
```

#### 2.1.2 获取我的帖子
**接口地址**: `GET /miniapp/post/my-posts`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| openId | string | 是 | 用户OpenID |
| current | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认10 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "records": [
            {
                "id": 1,
                "title": "帖子标题",
                "content": "帖子内容",
                "auditStatus": "APPROVED",
                "createTime": "2025-03-10 10:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1
    },
    "msg": "获取成功"
}
```

#### 2.1.3 获取帖子详情
**接口地址**: `GET /miniapp/post/detail/{id}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 帖子ID |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "id": 1,
        "title": "帖子标题",
        "content": "帖子内容",
        "images": ["图片1", "图片2"],
        "tags": ["标签1", "标签2"],
        "contactName": "联系人",
        "contactPhone": "联系电话",
        "likeCount": 10,
        "favoriteCount": 5,
        "viewCount": 100,
        "category": {
            "id": 1,
            "name": "二手交易"
        }
    },
    "msg": "获取成功"
}
```

#### 2.1.4 更新帖子
**接口地址**: `PUT /miniapp/post/{id}`

**请求参数**: 同发布帖子

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "id": 1,
        "title": "更新后的标题",
        "auditStatus": "PENDING"
    },
    "msg": "更新成功"
}
```

#### 2.1.5 删除帖子
**接口地址**: `DELETE /miniapp/post/{id}`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 帖子ID |
| openId | string | 是 | 用户OpenID |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "删除成功"
}
```

#### 2.1.6 获取帖子列表
**接口地址**: `GET /miniapp/post/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryId | long | 否 | 分类ID |
| keyword | string | 否 | 搜索关键词 |
| current | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认10 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "records": [
            {
                "id": 1,
                "title": "帖子标题",
                "content": "帖子内容",
                "likeCount": 10,
                "favoriteCount": 5,
                "viewCount": 100,
                "createTime": "2025-03-10 10:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1
    },
    "msg": "获取成功"
}
```

### 2.2 分类相关

#### 2.2.1 获取分类列表
**接口地址**: `GET /miniapp/post/categories`

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "二手交易",
            "icon": "icon-second-hand",
            "description": "二手物品买卖",
            "tip": "请确保物品信息真实有效",
            "tags": [
                {
                    "id": 1,
                    "name": "数码产品",
                    "color": "#1890ff"
                }
            ]
        }
    ],
    "msg": "获取成功"
}
```

### 2.3 标签相关

#### 2.3.1 根据分类获取标签
**接口地址**: `GET /miniapp/post/tags/{categoryId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryId | long | 是 | 分类ID |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "数码产品",
            "color": "#1890ff",
            "useCount": 10
        }
    ],
    "msg": "获取成功"
}
```

### 2.4 互动相关

#### 2.4.1 点赞帖子
**接口地址**: `POST /miniapp/post/{id}/like`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 帖子ID |
| openId | string | 是 | 用户OpenID |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "点赞成功"
}
```

#### 2.4.2 收藏帖子
**接口地址**: `POST /miniapp/post/{id}/favorite`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 帖子ID |
| openId | string | 是 | 用户OpenID |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "收藏成功"
}
```

#### 2.4.3 获取收藏的帖子
**接口地址**: `GET /miniapp/post/favorites`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| openId | string | 是 | 用户OpenID |
| current | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认10 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "records": [
            {
                "id": 1,
                "title": "帖子标题",
                "createTime": "2025-03-10 10:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1
    },
    "msg": "获取成功"
}
```

## 3. 后台管理接口

### 3.1 帖子管理

#### 3.1.1 获取帖子列表（后台管理）
**接口地址**: `GET /admin/post/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryId | long | 否 | 分类ID |
| auditStatus | string | 否 | 审核状态 |
| keyword | string | 否 | 搜索关键词 |
| current | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认10 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "records": [
            {
                "id": 1,
                "title": "帖子标题",
                "content": "帖子内容",
                "auditStatus": "PENDING",
                "openId": "用户OpenID",
                "createTime": "2025-03-10 10:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1
    },
    "msg": "获取成功"
}
```

#### 3.1.2 审核帖子
**接口地址**: `POST /admin/post/audit`

**请求参数**:
```json
{
    "postId": 1,
    "auditStatus": "APPROVED",
    "auditRemark": "审核备注",
    "rejectReason": "拒绝原因"
}
```

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "审核成功"
}
```

#### 3.1.3 批量审核帖子
**接口地址**: `POST /admin/post/batch-audit`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| postIds | string | 是 | 帖子ID列表，逗号分隔 |
| auditStatus | string | 是 | 审核状态 |
| auditRemark | string | 否 | 审核备注 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "批量审核成功"
}
```

#### 3.1.4 置顶帖子
**接口地址**: `POST /admin/post/{id}/top`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 帖子ID |
| isTop | boolean | 是 | 是否置顶 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "操作成功"
}
```

#### 3.1.5 获取审核统计
**接口地址**: `GET /admin/post/audit-stats`

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "pendingCount": 10,
        "approvedCount": 100,
        "rejectedCount": 5
    },
    "msg": "获取成功"
}
```

#### 3.1.6 获取帖子统计
**接口地址**: `GET /admin/post/stats`

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "totalCount": 1000,
        "todayCount": 50,
        "weekCount": 300
    },
    "msg": "获取成功"
}
```

### 3.2 分类管理

#### 3.2.1 获取分类列表（后台管理）
**接口地址**: `GET /admin/category/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 分类名称 |
| enabled | int | 否 | 是否启用 |
| current | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认10 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "records": [
            {
                "id": 1,
                "name": "二手交易",
                "enabled": 1,
                "enableAudit": 1,
                "createTime": "2025-03-10 10:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1
    },
    "msg": "获取成功"
}
```

#### 3.2.2 获取分类树
**接口地址**: `GET /admin/category/tree`

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "二手交易",
            "children": [
                {
                    "id": 2,
                    "name": "数码产品"
                }
            ],
            "tags": [
                {
                    "id": 1,
                    "name": "数码产品"
                }
            ]
        }
    ],
    "msg": "获取成功"
}
```

#### 3.2.3 新增分类
**接口地址**: `POST /admin/category/save`

**请求参数**:
```json
{
    "name": "分类名称",
    "parentId": 0,
    "icon": "分类图标",
    "description": "分类描述",
    "sort": 1,
    "enabled": 1,
    "enableAudit": 1,
    "tip": "提示信息"
}
```

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "新增成功"
}
```

#### 3.2.4 修改分类
**接口地址**: `POST /admin/category/update`

**请求参数**: 同新增分类

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "修改成功"
}
```

#### 3.2.5 启用/禁用分类
**接口地址**: `POST /admin/category/{id}/enable`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 分类ID |
| enabled | boolean | 是 | 是否启用 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "操作成功"
}
```

#### 3.2.6 启用/禁用分类审核
**接口地址**: `POST /admin/category/{id}/enable-audit`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 分类ID |
| enableAudit | boolean | 是 | 是否启用审核 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "操作成功"
}
```

### 3.3 标签管理

#### 3.3.1 获取标签列表（后台管理）
**接口地址**: `GET /admin/tag/list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 否 | 标签名称 |
| categoryId | long | 否 | 分类ID |
| enabled | int | 否 | 是否启用 |
| current | int | 否 | 页码，默认1 |
| size | int | 否 | 每页大小，默认10 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "records": [
            {
                "id": 1,
                "name": "数码产品",
                "categoryId": 1,
                "color": "#1890ff",
                "useCount": 10,
                "enabled": 1,
                "createTime": "2025-03-10 10:00:00"
            }
        ],
        "total": 1,
        "size": 10,
        "current": 1
    },
    "msg": "获取成功"
}
```

#### 3.3.2 根据分类获取标签
**接口地址**: `GET /admin/tag/category/{categoryId}`

**路径参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryId | long | 是 | 分类ID |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "数码产品",
            "color": "#1890ff",
            "useCount": 10
        }
    ],
    "msg": "获取成功"
}
```

#### 3.3.3 获取热门标签
**接口地址**: `GET /admin/tag/hot`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| limit | int | 否 | 限制数量，默认10 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": [
        {
            "id": 1,
            "name": "数码产品",
            "useCount": 100
        }
    ],
    "msg": "获取成功"
}
```

#### 3.3.4 新增标签
**接口地址**: `POST /admin/tag/save`

**请求参数**:
```json
{
    "name": "标签名称",
    "categoryId": 1,
    "color": "#1890ff",
    "icon": "标签图标",
    "sort": 1,
    "enabled": 1
}
```

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "新增成功"
}
```

#### 3.3.5 修改标签
**接口地址**: `POST /admin/tag/update`

**请求参数**: 同新增标签

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "修改成功"
}
```

#### 3.3.6 启用/禁用标签
**接口地址**: `POST /admin/tag/{id}/enable`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | long | 是 | 标签ID |
| enabled | boolean | 是 | 是否启用 |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": true,
    "msg": "操作成功"
}
```

#### 3.3.7 创建标签
**接口地址**: `POST /admin/tag/create`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | string | 是 | 标签名称 |
| categoryId | long | 是 | 分类ID |

**响应示例**:
```json
{
    "code": 200,
    "success": true,
    "data": {
        "id": 1,
        "name": "新标签",
        "categoryId": 1,
        "useCount": 1
    },
    "msg": "创建成功"
}
```

## 4. 错误码说明

### 4.1 通用错误码
| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

### 4.2 业务错误码
| 错误码 | 说明 |
|--------|------|
| 1001 | 分类不存在或已禁用 |
| 1002 | 帖子不存在 |
| 1003 | 无权限操作 |
| 1004 | 审核状态错误 |
| 1005 | 标签已存在 |

## 5. 接口调用示例

### 5.1 JavaScript示例
```javascript
// 发布帖子
const publishPost = async (postData) => {
    try {
        const response = await fetch('/miniapp/post/publish', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(postData)
        });
        const result = await response.json();
        return result;
    } catch (error) {
        console.error('发布失败:', error);
    }
};

// 获取分类列表
const getCategories = async () => {
    try {
        const response = await fetch('/miniapp/post/categories');
        const result = await response.json();
        return result.data;
    } catch (error) {
        console.error('获取分类失败:', error);
    }
};
```

### 5.2 Java示例
```java
// 发布帖子
@PostMapping("/publish")
public R<SupPost> publish(@Valid @RequestBody PostPublishDTO postPublishDTO) {
    try {
        SupPost post = supPostService.publishPost(postPublishDTO);
        return R.data(post);
    } catch (Exception e) {
        return R.fail("发布失败: " + e.getMessage());
    }
}

// 审核帖子
@PostMapping("/audit")
@PreAuth("hasPermission('post:audit')")
public R<Boolean> auditPost(@Valid @RequestBody PostAuditDTO postAuditDTO) {
    try {
        boolean result = supPostService.auditPost(postAuditDTO);
        return R.data(result);
    } catch (Exception e) {
        return R.fail("审核失败: " + e.getMessage());
    }
}
```

## 6. 注意事项

### 6.1 接口调用限制
- 小程序接口无需认证，但建议进行频率限制
- 后台管理接口需要管理员权限
- 文件上传大小限制为10MB

### 6.2 数据格式要求
- 所有时间格式为：`yyyy-MM-dd HH:mm:ss`
- 图片URL需要完整的访问地址
- 标签数据为字符串数组格式

### 6.3 性能优化建议
- 使用分页查询避免大量数据返回
- 合理使用缓存减少数据库查询
- 异步处理耗时操作

---

**文档版本**: v1.0  
**更新时间**: 2025-03-10  
**维护人员**: 开发团队 