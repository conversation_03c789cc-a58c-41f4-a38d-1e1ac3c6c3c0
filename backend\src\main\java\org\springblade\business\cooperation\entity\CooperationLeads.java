/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.cooperation.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 合作线索表实体类
 *
 * <AUTHOR>
 * @since 2025-07-28
 */
@Data
@TableName("urb_cooperation_leads")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "合作线索表")
public class CooperationLeads extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String name;
    /**
     * 联系电话
     */
    @Schema(description = "联系电话")
    private String phone;
    /**
     * 微信ID
     */
    @Schema(description = "微信ID")
    private String wechat;
    /**
     * 合作类型(广告/赞助/联合推广等)
     */
    @Schema(description = "合作类型(广告/赞助/联合推广等)")
    private String cooperationType;
    /**
     * 合作详情描述
     */
    @Schema(description = "合作详情描述")
    private String cooperationDetails;
    /**
     * 备注信息
     */
    @Schema(description = "备注信息")
    private String remarks;


}
