-- 分类功能增强SQL脚本
-- 更新时间: 2025-03-10
-- 说明: 完善分类表结构，支持图标、描述、排序等功能，并创建分类标签关联表

-- 1. 更新分类表 urb_category，添加缺失字段
ALTER TABLE `urb_category`
ADD COLUMN `icon` varchar(200) NULL COMMENT '分类图标' AFTER `parent_id`,
ADD COLUMN `description` varchar(500) NULL COMMENT '分类描述' AFTER `icon`,
ADD COLUMN `sort` int(11) DEFAULT 0 COMMENT '排序' AFTER `description`,
ADD COLUMN `enabled` int(1) DEFAULT 1 COMMENT '是否启用：0-否，1-是' AFTER `sort`,
ADD COLUMN `enable_audit` int(1) DEFAULT 1 COMMENT '是否启用审核：0-否，1-是' AFTER `enabled`,
ADD COLUMN `tip` varchar(500) NULL COMMENT '提示信息' AFTER `enable_audit`;

-- 添加索引
ALTER TABLE `urb_category`
ADD INDEX `idx_enabled` (`enabled`),
ADD INDEX `idx_sort` (`sort`),
ADD INDEX `idx_parent_id` (`parent_id`);

-- 2. 更新标签表 urb_tag，添加缺失字段
ALTER TABLE `urb_tag`
ADD COLUMN `category_id` bigint(20) NULL COMMENT '分类ID' AFTER `tag_name`,
ADD COLUMN `color` varchar(20) DEFAULT '#1890ff' COMMENT '标签颜色' AFTER `category_id`,
ADD COLUMN `icon` varchar(200) NULL COMMENT '标签图标' AFTER `color`,
ADD COLUMN `sort` int(11) DEFAULT 0 COMMENT '排序' AFTER `icon`,
ADD COLUMN `enabled` int(1) DEFAULT 1 COMMENT '是否启用：0-否，1-是' AFTER `sort`,
ADD COLUMN `use_count` int(11) DEFAULT 0 COMMENT '使用次数' AFTER `enabled`,
ADD COLUMN `is_system` int(1) DEFAULT 0 COMMENT '是否系统标签：0-否，1-是' AFTER `use_count`;

-- 添加索引
ALTER TABLE `urb_tag`
ADD INDEX `idx_category_id` (`category_id`),
ADD INDEX `idx_enabled` (`enabled`),
ADD INDEX `idx_use_count` (`use_count`),
ADD INDEX `idx_sort` (`sort`);

-- 3. 创建分类标签关联表 urb_category_tag
CREATE TABLE `urb_category_tag` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键',
  `category_id` bigint(20) NOT NULL COMMENT '分类ID',
  `tag_id` bigint(20) NOT NULL COMMENT '标签ID',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_category_tag` (`category_id`, `tag_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_tag_id` (`tag_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分类标签关联表';

-- 4. 插入默认分类数据（参考小程序分类）
INSERT INTO `urb_category` (`name`, `parent_id`, `icon`, `description`, `sort`, `enabled`, `enable_audit`, `tip`, `create_time`, `update_time`) VALUES
-- 二手交易分类组
('二手交易', 0, '/assets/images/categories/second-hand.png', '闲置物品、二手商品交易', 1, 1, 1, '请确保物品信息真实有效，支持面交和快递', NOW(), NOW()),
('数码产品', 1, '/assets/images/categories/electronics.png', '手机、电脑、数码产品等', 1, 1, 1, '请提供详细的商品信息和真实图片', NOW(), NOW()),
('服装鞋帽', 1, '/assets/images/categories/clothing.png', '服装、鞋子、帽子等', 2, 1, 1, '请注明尺码、品牌、新旧程度', NOW(), NOW()),
('家居用品', 1, '/assets/images/categories/furniture.png', '家具、家电、生活用品等', 3, 1, 1, '大件物品请注明是否包邮', NOW(), NOW()),
('图书音像', 1, '/assets/images/categories/books.png', '书籍、音像制品等', 4, 1, 1, '请注明书籍版本和品相', NOW(), NOW()),
('运动户外', 1, '/assets/images/categories/sports.png', '运动器材、户外装备等', 5, 1, 1, '请注明使用情况和品牌', NOW(), NOW()),

-- 生活服务分类组
('生活服务', 0, '/assets/images/categories/service.png', '各类生活服务信息', 2, 1, 1, '请确保服务信息准确，价格透明', NOW(), NOW()),
('房屋租赁', 4, '/assets/images/categories/house.png', '房屋出租、求租信息', 1, 1, 1, '请提供详细的房屋信息和真实图片', NOW(), NOW()),
('本地服务', 4, '/assets/images/categories/local-service.png', '家政、维修、搬家等服务', 2, 1, 1, '请注明服务范围和收费标准', NOW(), NOW()),
('求助互助', 4, '/assets/images/categories/help.png', '寻人寻物、互助信息', 3, 1, 1, '请提供详细的求助信息', NOW(), NOW()),
('教育培训', 4, '/assets/images/categories/education.png', '教育培训信息', 4, 1, 1, '请提供真实的教育资质信息', NOW(), NOW()),
('美容美发', 4, '/assets/images/categories/beauty.png', '美容、美发、美甲等服务', 5, 1, 1, '请注明服务项目和价格', NOW(), NOW()),

-- 求职招聘分类组
('求职招聘', 0, '/assets/images/categories/job.png', '工作机会发布和求职信息', 3, 1, 1, '请提供真实的工作信息，薪资透明', NOW(), NOW()),
('全职工作', 10, '/assets/images/categories/fulltime.png', '全职工作机会', 1, 1, 1, '请注明工作地点、薪资待遇、要求', NOW(), NOW()),
('兼职工作', 10, '/assets/images/categories/parttime.png', '兼职工作机会', 2, 1, 1, '请注明工作时间、薪资标准', NOW(), NOW()),
('实习机会', 10, '/assets/images/categories/internship.png', '实习工作机会', 3, 1, 1, '请注明实习期限、补贴标准', NOW(), NOW()),
('技术岗位', 10, '/assets/images/categories/tech.png', '技术类工作岗位', 4, 1, 1, '请注明技术要求和薪资范围', NOW(), NOW()),
('销售岗位', 10, '/assets/images/categories/sales.png', '销售类工作岗位', 5, 1, 1, '请注明销售目标和提成比例', NOW(), NOW()),

-- 其他分类
('同城跑腿', 0, '/assets/images/categories/runner.png', '跑腿、代购、代取等服务', 4, 1, 1, '请注明服务范围和收费标准', NOW(), NOW()),
('便民信息', 0, '/assets/images/categories/info.png', '各类便民信息发布', 5, 1, 1, '请确保信息真实有效', NOW(), NOW());

-- 5. 插入默认标签数据
INSERT INTO `urb_tag` (`tag_name`, `category_id`, `color`, `sort`, `enabled`, `use_count`, `is_system`, `create_time`, `update_time`) VALUES
-- 二手交易标签
('数码产品', 2, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('服装鞋帽', 3, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('家居用品', 4, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('图书音像', 5, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('运动户外', 6, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),
('面交', 1, '#13c2c2', 6, 1, 0, 1, NOW(), NOW()),
('包邮', 1, '#eb2f96', 7, 1, 0, 1, NOW(), NOW()),
('急售', 1, '#fa541c', 8, 1, 0, 1, NOW(), NOW()),
('全新', 1, '#52c41a', 9, 1, 0, 1, NOW(), NOW()),
('九成新', 1, '#1890ff', 10, 1, 0, 1, NOW(), NOW()),

-- 房屋租赁标签
('整租', 8, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('合租', 8, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('短租', 8, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('长租', 8, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('精装修', 8, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),
('近地铁', 8, '#13c2c2', 6, 1, 0, 1, NOW(), NOW()),
('带阳台', 8, '#eb2f96', 7, 1, 0, 1, NOW(), NOW()),
('独立卫浴', 8, '#fa541c', 8, 1, 0, 1, NOW(), NOW()),

-- 求职招聘标签
('全职', 12, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('兼职', 13, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('实习', 14, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('技术', 15, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('销售', 16, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),
('五险一金', 10, '#13c2c2', 6, 1, 0, 1, NOW(), NOW()),
('双休', 10, '#eb2f96', 7, 1, 0, 1, NOW(), NOW()),
('年终奖', 10, '#fa541c', 8, 1, 0, 1, NOW(), NOW()),
('带薪年假', 10, '#52c41a', 9, 1, 0, 1, NOW(), NOW()),
('免费培训', 10, '#1890ff', 10, 1, 0, 1, NOW(), NOW()),

-- 生活服务标签
('家政服务', 9, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('维修服务', 9, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('美容美发', 11, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('教育培训', 10, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('物流快递', 4, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),
('上门服务', 4, '#13c2c2', 6, 1, 0, 1, NOW(), NOW()),
('24小时', 4, '#eb2f96', 7, 1, 0, 1, NOW(), NOW()),
('专业认证', 4, '#fa541c', 8, 1, 0, 1, NOW(), NOW()),

-- 同城跑腿标签
('代购', 17, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
('代取', 17, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
('代送', 17, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
('跑腿', 17, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
('急件', 17, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),
('同城', 17, '#13c2c2', 6, 1, 0, 1, NOW(), NOW());

-- 6. 建立分类标签关联关系
INSERT INTO `urb_category_tag` (`category_id`, `tag_id`, `sort_order`) VALUES
-- 二手交易分类的标签
(1, 6, 1), (1, 7, 2), (1, 8, 3), (1, 9, 4), (1, 10, 5),
(2, 1, 1), (2, 6, 2), (2, 7, 3), (2, 8, 4),
(3, 2, 1), (3, 6, 2), (3, 7, 3), (3, 9, 4), (3, 10, 5),
(4, 3, 1), (4, 6, 2), (4, 7, 3), (4, 8, 4),
(5, 4, 1), (5, 6, 2), (5, 7, 3), (5, 9, 4),
(6, 5, 1), (6, 6, 2), (6, 7, 3), (6, 8, 4),

-- 房屋租赁分类的标签
(8, 11, 1), (8, 12, 2), (8, 13, 3), (8, 14, 4), (8, 15, 5), (8, 16, 6), (8, 17, 7), (8, 18, 8),

-- 求职招聘分类的标签
(10, 19, 1), (10, 20, 2), (10, 21, 3), (10, 22, 4), (10, 23, 5), (10, 24, 6), (10, 25, 7), (10, 26, 8), (10, 27, 9), (10, 28, 10),
(12, 19, 1), (12, 24, 2), (12, 25, 3), (12, 26, 4), (12, 27, 5), (12, 28, 6),
(13, 20, 1), (13, 24, 2), (13, 25, 3),
(14, 21, 1), (14, 24, 2), (14, 25, 3),
(15, 22, 1), (15, 24, 2), (15, 25, 3), (15, 26, 4), (15, 27, 5), (15, 28, 6),
(16, 23, 1), (16, 24, 2), (16, 25, 3), (16, 26, 4), (16, 27, 5), (16, 28, 6),

-- 生活服务分类的标签
(4, 29, 1), (4, 30, 2), (4, 31, 3), (4, 32, 4), (4, 33, 5), (4, 34, 6), (4, 35, 7), (4, 36, 8),
(9, 29, 1), (9, 30, 2), (9, 34, 3), (9, 35, 4), (9, 36, 5),
(11, 31, 1), (11, 34, 2), (11, 35, 3), (11, 36, 4),
(10, 32, 1), (10, 34, 2), (10, 35, 3), (10, 36, 4),

-- 同城跑腿分类的标签
(17, 37, 1), (17, 38, 2), (17, 39, 3), (17, 40, 4), (17, 41, 5), (17, 42, 6);

-- 7. 验证数据
SELECT '分类数据统计' as info, COUNT(*) as count FROM urb_category;
SELECT '标签数据统计' as info, COUNT(*) as count FROM urb_tag;
SELECT '分类标签关联统计' as info, COUNT(*) as count FROM urb_category_tag;

-- 8. 查看分类树形结构
SELECT 
    c1.id,
    c1.name as category_name,
    c1.icon,
    c1.description,
    c1.sort,
    c1.enabled,
    c2.name as parent_name
FROM urb_category c1
LEFT JOIN urb_category c2 ON c1.parent_id = c2.id
WHERE c1.is_deleted = 0
ORDER BY c1.sort, c1.id; 