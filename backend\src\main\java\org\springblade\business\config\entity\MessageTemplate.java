/**
 * Copyright (c) 2018-2099, Chill <PERSON><PERSON> 庄骞 (<EMAIL>).
 * <p>
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 * <p>
 * http://www.apache.org/licenses/LICENSE-2.0
 * <p>
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.springblade.business.config.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import org.springblade.core.mp.base.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;

/**
 * 消息模板表实体类
 *
 * <AUTHOR>
 * @since 2025-07-09
 */
@Data
@TableName("urb_message_template")
@EqualsAndHashCode(callSuper = true)
@Schema(description = "消息模板表")
public class MessageTemplate extends BaseEntity {

    @Serial
	private static final long serialVersionUID = 1L;

    /**
     * 消息类型代码
     */
    @Schema(description = "消息类型代码")
    private String typeCode;
    /**
     * 消息类型名称
     */
    @Schema(description = "消息类型名称")
    private String typeName;
    /**
     * 标题模板
     */
    @Schema(description = "标题模板")
    private String titleTemplate;
    /**
     * 内容模板
     */
    @Schema(description = "内容模板")
    private String contentTemplate;



}
