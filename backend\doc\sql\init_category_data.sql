-- 初始化分类数据SQL脚本
-- 更新时间: 2025-03-10
-- 说明: 根据小程序分类内容，初始化分类和标签数据

-- 1. 清空现有数据（谨慎使用）
-- DELETE FROM urb_category_tag;
-- DELETE FROM urb_tag;
-- DELETE FROM urb_category;

-- 2. 插入分类数据
INSERT INTO `urb_category` (`id`, `name`, `parent_id`, `icon`, `description`, `sort`, `enabled`, `enable_audit`, `tip`, `max_images`, `status`, `create_time`, `update_time`) VALUES
(1, '求职招聘', 0, '/assets/images/common/job.png', '找工作、招聘信息发布', 1, 1, 1, '请确保信息真实有效，避免虚假招聘', 9, 1, NOW(), NOW()),
(2, '二手交易', 0, '/assets/images/common/secondhand.png', '闲置物品、二手商品交易', 2, 1, 1, '交易时请当面验货，注意安全', 9, 1, NOW(), NOW()),
(3, '本地推荐', 0, '/assets/images/common/recommend.png', '本地美食、娱乐、生活推荐', 3, 1, 1, '推荐内容需要真实体验', 9, 1, NOW(), NOW()),
(4, '房屋租售', 0, '/assets/images/common/house.png', '房屋出租、出售、求租信息', 4, 1, 1, '请提供真实房源信息，避免虚假广告', 9, 1, NOW(), NOW()),
(5, '教育服务', 0, '/assets/images/common/education.png', '教育培训、课程推荐、学习交流', 5, 1, 1, '请确保教育资质真实有效', 9, 1, NOW(), NOW()),
(6, '便民服务', 0, '/assets/images/common/service.png', '家政服务、维修、搬家等便民信息', 6, 1, 1, '服务前请确认商家资质', 9, 1, NOW(), NOW()),
(7, '同城跑腿', 0, '/assets/images/common/runner.png', '代购、代取、代送等跑腿服务', 7, 1, 1, '跑腿服务请选择正规平台', 9, 1, NOW(), NOW());

-- 3. 插入标签数据
INSERT INTO `urb_tag` (`id`, `tag_name`, `category_id`, `color`, `sort`, `enabled`, `use_count`, `is_system`, `create_time`, `update_time`) VALUES
-- 求职招聘标签
(1, '全职', 1, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
(2, '兼职', 1, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
(3, '实习', 1, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
(4, '急招', 1, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
(5, '高薪', 1, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),
(6, '五险一金', 1, '#13c2c2', 6, 1, 0, 1, NOW(), NOW()),

-- 二手交易标签
(7, '热门', 2, '#f5222d', 1, 1, 0, 1, NOW(), NOW()),
(8, '急售', 2, '#fa8c16', 2, 1, 0, 1, NOW(), NOW()),
(9, '面交', 2, '#52c41a', 3, 1, 0, 1, NOW(), NOW()),
(10, '包邮', 2, '#1890ff', 4, 1, 0, 1, NOW(), NOW()),
(11, '全新', 2, '#722ed1', 5, 1, 0, 1, NOW(), NOW()),
(12, '可议价', 2, '#faad14', 6, 1, 0, 1, NOW(), NOW()),

-- 本地推荐标签
(13, '美食', 3, '#f5222d', 1, 1, 0, 1, NOW(), NOW()),
(14, '娱乐', 3, '#722ed1', 2, 1, 0, 1, NOW(), NOW()),
(15, '景点', 3, '#52c41a', 3, 1, 0, 1, NOW(), NOW()),
(16, '购物', 3, '#fa8c16', 4, 1, 0, 1, NOW(), NOW()),
(17, '推荐', 3, '#1890ff', 5, 1, 0, 1, NOW(), NOW()),
(18, '限时', 3, '#faad14', 6, 1, 0, 1, NOW(), NOW()),

-- 房屋租售标签
(19, '整租', 4, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
(20, '合租', 4, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
(21, '精装', 4, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
(22, '近地铁', 4, '#722ed1', 4, 1, 0, 1, NOW(), NOW()),
(23, '急租', 4, '#f5222d', 5, 1, 0, 1, NOW(), NOW()),
(24, '可养宠物', 4, '#13c2c2', 6, 1, 0, 1, NOW(), NOW()),

-- 教育服务标签
(25, '一对一', 5, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
(26, '小班', 5, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
(27, '在线', 5, '#722ed1', 3, 1, 0, 1, NOW(), NOW()),
(28, '名师', 5, '#faad14', 4, 1, 0, 1, NOW(), NOW()),
(29, '免费试听', 5, '#13c2c2', 5, 1, 0, 1, NOW(), NOW()),
(30, '包过', 5, '#f5222d', 6, 1, 0, 1, NOW(), NOW()),

-- 便民服务标签
(31, '家政', 6, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
(32, '维修', 6, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
(33, '搬家', 6, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
(34, '保洁', 6, '#722ed1', 4, 1, 0, 1, NOW(), NOW()),
(35, '上门', 6, '#13c2c2', 5, 1, 0, 1, NOW(), NOW()),
(36, '24小时', 6, '#f5222d', 6, 1, 0, 1, NOW(), NOW()),

-- 同城跑腿标签
(37, '代购', 7, '#1890ff', 1, 1, 0, 1, NOW(), NOW()),
(38, '代取', 7, '#52c41a', 2, 1, 0, 1, NOW(), NOW()),
(39, '代送', 7, '#faad14', 3, 1, 0, 1, NOW(), NOW()),
(40, '急件', 7, '#f5222d', 4, 1, 0, 1, NOW(), NOW()),
(41, '安全', 7, '#13c2c2', 5, 1, 0, 1, NOW(), NOW()),
(42, '准时', 7, '#722ed1', 6, 1, 0, 1, NOW(), NOW());

-- 4. 建立分类标签关联关系
INSERT INTO `urb_category_tag` (`category_id`, `tag_id`, `sort_order`, `create_time`, `update_time`) VALUES
-- 求职招聘分类的标签
(1, 1, 1, NOW(), NOW()),
(1, 2, 2, NOW(), NOW()),
(1, 3, 3, NOW(), NOW()),
(1, 4, 4, NOW(), NOW()),
(1, 5, 5, NOW(), NOW()),
(1, 6, 6, NOW(), NOW()),

-- 二手交易分类的标签
(2, 7, 1, NOW(), NOW()),
(2, 8, 2, NOW(), NOW()),
(2, 9, 3, NOW(), NOW()),
(2, 10, 4, NOW(), NOW()),
(2, 11, 5, NOW(), NOW()),
(2, 12, 6, NOW(), NOW()),

-- 本地推荐分类的标签
(3, 13, 1, NOW(), NOW()),
(3, 14, 2, NOW(), NOW()),
(3, 15, 3, NOW(), NOW()),
(3, 16, 4, NOW(), NOW()),
(3, 17, 5, NOW(), NOW()),
(3, 18, 6, NOW(), NOW()),

-- 房屋租售分类的标签
(4, 19, 1, NOW(), NOW()),
(4, 20, 2, NOW(), NOW()),
(4, 21, 3, NOW(), NOW()),
(4, 22, 4, NOW(), NOW()),
(4, 23, 5, NOW(), NOW()),
(4, 24, 6, NOW(), NOW()),

-- 教育服务分类的标签
(5, 25, 1, NOW(), NOW()),
(5, 26, 2, NOW(), NOW()),
(5, 27, 3, NOW(), NOW()),
(5, 28, 4, NOW(), NOW()),
(5, 29, 5, NOW(), NOW()),
(5, 30, 6, NOW(), NOW()),

-- 便民服务分类的标签
(6, 31, 1, NOW(), NOW()),
(6, 32, 2, NOW(), NOW()),
(6, 33, 3, NOW(), NOW()),
(6, 34, 4, NOW(), NOW()),
(6, 35, 5, NOW(), NOW()),
(6, 36, 6, NOW(), NOW()),

-- 同城跑腿分类的标签
(7, 37, 1, NOW(), NOW()),
(7, 38, 2, NOW(), NOW()),
(7, 39, 3, NOW(), NOW()),
(7, 40, 4, NOW(), NOW()),
(7, 41, 5, NOW(), NOW()),
(7, 42, 6, NOW(), NOW());

-- 5. 更新自增ID
ALTER TABLE `urb_category` AUTO_INCREMENT = 8;
ALTER TABLE `urb_tag` AUTO_INCREMENT = 43;
ALTER TABLE `urb_category_tag` AUTO_INCREMENT = 43; 