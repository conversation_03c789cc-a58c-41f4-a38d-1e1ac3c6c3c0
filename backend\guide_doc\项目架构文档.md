# 易贴易找后端项目架构文档

## 项目概述

**项目名称**: EasyPostEasyFindBackend (易贴易找后台)  
**版本**: 4.4.0  
**技术栈**: Spring Boot 3.x + MyBatis Plus + MySQL + SpringBlade框架  
**Java版本**: 17  
**构建工具**: Maven  

## 技术架构

### 核心技术栈

| 技术组件 | 版本 | 说明 |
|---------|------|------|
| Spring Boot | 3.2.10 | 主框架 |
| SpringBlade | 4.4.2 | 企业级开发框架 |
| MyBatis Plus | 最新版 | ORM框架 |
| MySQL | 8.0+ | 数据库 |
| Knife4j | 最新版 | API文档 |
| Hutool | 5.8.26 | 工具类库 |
| 微信Java SDK | 4.7.0 | 微信小程序集成 |

### 框架特性

- **微服务架构**: 基于SpringBlade框架的微服务架构
- **多租户支持**: 内置多租户隔离机制
- **权限管理**: 基于RBAC的权限控制系统
- **API文档**: 集成Knife4j自动生成API文档
- **文件存储**: 支持七牛云等OSS存储
- **微信集成**: 支持微信小程序和支付功能

## 项目结构

```
backend/
├── src/main/java/org/springblade/
│   ├── Application.java                 # 启动类
│   ├── business/                        # 业务模块
│   │   ├── post/                        # 帖子业务模块
│   │   │   ├── controller/              # 控制器层
│   │   │   ├── service/                 # 服务层
│   │   │   ├── mapper/                  # 数据访问层
│   │   │   ├── entity/                  # 实体类
│   │   │   ├── vo/                      # 视图对象
│   │   │   ├── dto/                     # 数据传输对象
│   │   │   └── wrapper/                 # 包装器
│   │   ├── user/                        # 用户业务模块
│   │   ├── feedback/                    # 反馈业务模块
│   │   └── report/                      # 举报业务模块
│   ├── modules/                         # 系统模块
│   │   ├── system/                      # 系统管理
│   │   ├── auth/                        # 认证授权
│   │   ├── desk/                        # 工作台
│   │   ├── develop/                     # 开发工具
│   │   └── resource/                    # 资源管理
│   ├── common/                          # 公共模块
│   ├── core/                            # 核心模块
│   └── miniapp/                         # 小程序模块
├── src/main/resources/
│   ├── application.yml                  # 主配置文件
│   ├── application-dev.yml              # 开发环境配置
│   ├── application-prod.yml             # 生产环境配置
│   ├── application-test.yml             # 测试环境配置
│   ├── config/                          # 配置目录
│   ├── db/                              # 数据库相关
│   ├── log/                             # 日志配置
│   ├── static/                          # 静态资源
│   └── templates/                       # 模板文件
├── doc/                                 # 文档目录
│   ├── sql/                             # SQL脚本
│   └── script/                          # 部署脚本
└── pom.xml                              # Maven配置
```

## 核心模块说明

### 1. 业务模块 (business)

#### 1.1 帖子模块 (post)
- **功能**: 信息贴的发布、管理、审核
- **核心实体**: SupPost (信息贴)、Category (分类)、Tag (标签)
- **主要接口**:
  - `/blade-ad/post/*` - 帖子管理接口
  - `/blade-ad/category/*` - 分类管理接口
  - `/blade-ad/tag/*` - 标签管理接口

#### 1.2 用户模块 (user)
- **功能**: 用户信息管理、用户行为统计
- **核心实体**: User (用户信息)

#### 1.3 反馈模块 (feedback)
- **功能**: 用户反馈收集、处理
- **核心实体**: Feedback (反馈信息)

#### 1.4 举报模块 (report)
- **功能**: 内容举报处理、违规内容管理
- **核心实体**: Report (举报信息)

### 2. 系统模块 (modules)

#### 2.1 系统管理 (system)
- **功能**: 用户管理、角色管理、部门管理、字典管理
- **核心功能**: 系统基础数据管理

#### 2.2 认证授权 (auth)
- **功能**: 用户认证、权限验证、Token管理
- **核心功能**: 安全认证体系

#### 2.3 工作台 (desk)
- **功能**: 通知公告、工作流程
- **核心功能**: 系统工作台功能

### 3. 公共模块 (common)

#### 3.1 常量定义
- **LauncherConstant**: 应用启动常量
- **业务常量**: 各业务模块的常量定义

#### 3.2 工具类
- **数据转换**: 实体与VO之间的转换
- **验证工具**: 数据验证相关工具

### 4. 核心模块 (core)

#### 4.1 启动器
- **BladeApplication**: 框架启动器
- **配置加载**: 自动配置加载

#### 4.2 基础组件
- **BaseEntity**: 基础实体类
- **BaseService**: 基础服务接口
- **BaseController**: 基础控制器

## 数据库设计

### 核心表结构

#### 1. 信息贴表 (urb_post)
```sql
CREATE TABLE `urb_post` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `title` varchar(255) DEFAULT NULL COMMENT '标题',
  `content` text COMMENT '内容',
  `images` text COMMENT '图片',
  `address` varchar(255) DEFAULT NULL COMMENT '发布地址',
  `publish_time` datetime(6) DEFAULT NULL COMMENT '发布时间',
  `audit_status` varchar(20) DEFAULT NULL COMMENT '审核状态',
  `geo_location` json DEFAULT NULL COMMENT '地理位置',
  `tags` json DEFAULT NULL COMMENT '标签',
  `contact_name` varchar(50) DEFAULT NULL COMMENT '联系人姓名',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `top` varchar(10) DEFAULT NULL COMMENT '是否置顶',
  `completed` int DEFAULT '0' COMMENT '是否已完成',
  -- 基础字段
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='百事通信息贴';
```

#### 2. 分类表 (urb_category)
```sql
CREATE TABLE `urb_category` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `name` varchar(50) DEFAULT NULL COMMENT '分类名称',
  `parent_id` bigint DEFAULT NULL COMMENT '上级分类ID',
  `max_images` int DEFAULT '0' COMMENT '最大图片数',
  `allow_tags` json DEFAULT NULL COMMENT '允许的标签',
  -- 基础字段
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='广告分类';
```

#### 3. 标签表 (urb_tag)
```sql
CREATE TABLE `urb_tag` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `tag_name` varchar(50) DEFAULT NULL COMMENT '标签名称',
  `description` text COMMENT '描述说明',
  `sort_order` int DEFAULT '0' COMMENT '排序序号',
  -- 基础字段
  `create_time` datetime(6) DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime(6) DEFAULT NULL COMMENT '更新时间',
  `is_deleted` int DEFAULT '0' COMMENT '是否删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='信息标签';
```

#### 4. 关联表
- **urb_post_tag**: 帖子与标签的关联表
- **urb_post_category**: 帖子与分类的关联表
- **urb_category_tag**: 分类与标签的关联表

### 数据关系

```
urb_post (信息贴)
├── 1:N urb_post_tag (帖子标签关联)
├── N:1 urb_category (分类)
└── 1:1 urb_contact (联系人)

urb_category (分类)
├── 1:N urb_category_tag (分类标签关联)
└── 1:N urb_post (信息贴)

urb_tag (标签)
├── N:1 urb_category_tag (分类标签关联)
└── N:1 urb_post_tag (帖子标签关联)
```

## API接口设计

### 1. 帖子管理接口

#### 1.1 获取帖子列表
```
GET /blade-ad/post/list
参数: 
- page: 页码
- size: 每页大小
- title: 标题模糊查询
- audit_status: 审核状态
```

#### 1.2 创建帖子
```
POST /blade-ad/post/save
请求体:
{
  "title": "帖子标题",
  "content": "帖子内容",
  "images": ["图片URL1", "图片URL2"],
  "address": "发布地址",
  "tags": ["标签1", "标签2"],
  "contact_name": "联系人",
  "contact_phone": "联系电话"
}
```

#### 1.3 更新帖子
```
POST /blade-ad/post/update
请求体: 同创建帖子
```

#### 1.4 删除帖子
```
POST /blade-ad/post/remove
参数: ids (逗号分隔的ID列表)
```

### 2. 分类管理接口

#### 2.1 获取分类列表
```
GET /blade-ad/category/list
参数:
- page: 页码
- size: 每页大小
- name: 分类名称
```

#### 2.2 创建分类
```
POST /blade-ad/category/save
请求体:
{
  "name": "分类名称",
  "parent_id": 0,
  "max_images": 6,
  "allow_tags": ["标签1", "标签2"]
}
```

### 3. 标签管理接口

#### 3.1 获取标签列表
```
GET /blade-ad/tag/list
参数:
- page: 页码
- size: 每页大小
- tag_name: 标签名称
```

#### 3.2 创建标签
```
POST /blade-ad/tag/save
请求体:
{
  "tag_name": "标签名称",
  "description": "标签描述",
  "sort_order": 1
}
```

## 配置说明

### 1. 数据库配置
```yaml
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************
    username: root
    password: password
```

### 2. MyBatis Plus配置
```yaml
mybatis-plus:
  mapper-locations: classpath:org/springblade/**/mapper/*Mapper.xml
  typeAliasesPackage: org.springblade.**.entity
  global-config:
    db-config:
      id-type: assign_id
      logic-delete-value: 1
      logic-not-delete-value: 0
```

### 3. 文件上传配置
```yaml
spring:
  servlet:
    multipart:
      max-file-size: 256MB
      max-request-size: 1024MB
```

### 4. 微信小程序配置
```yaml
wx:
  miniapp:
    configs:
      - appid: wx5f0591468a438c48
        secret: bbdbb042eb8a9d55f032f53a41b99c82
    main-app-id: wx5f0591468a438c48
    main-secret: bbdbb042eb8a9d55f032f53a41b99c82
```

## 部署说明

### 1. 环境要求
- JDK 17+
- MySQL 8.0+
- Maven 3.6+
- Redis (可选，用于缓存)

### 2. 构建部署
```bash
# 编译打包
mvn clean package -Dmaven.test.skip=true

# 运行
java -jar target/SpringBlade.jar --spring.profiles.active=prod
```

### 3. Docker部署
```bash
# 构建镜像
docker build -t easy-post-backend:latest .

# 运行容器
docker run -d -p 80:80 --name easy-post-backend easy-post-backend:latest
```

## 开发规范

### 1. 代码规范
- 遵循阿里巴巴Java开发手册
- 使用Lombok简化代码
- 统一使用Swagger注解
- 遵循RESTful API设计规范

### 2. 命名规范
- 类名: 大驼峰命名法 (PascalCase)
- 方法名: 小驼峰命名法 (camelCase)
- 常量: 全大写+下划线 (UPPER_SNAKE_CASE)
- 数据库表: 小写+下划线 (snake_case)

### 3. 注释规范
- 类和方法必须有JavaDoc注释
- 复杂业务逻辑需要行内注释
- API接口必须有Swagger注解

### 4. 异常处理
- 统一使用R对象返回结果
- 业务异常使用自定义异常类
- 系统异常统一处理

## 性能优化

### 1. 数据库优化
- 合理使用索引
- 避免N+1查询问题
- 使用分页查询
- 配置连接池

### 2. 缓存策略
- 使用Redis缓存热点数据
- 合理设置缓存过期时间
- 避免缓存穿透和雪崩

### 3. 接口优化
- 使用异步处理耗时操作
- 合理使用分页
- 避免返回过大的数据量

## 安全考虑

### 1. 认证授权
- 使用JWT Token认证
- 实现RBAC权限控制
- 接口权限验证

### 2. 数据安全
- 敏感数据加密存储
- SQL注入防护
- XSS攻击防护

### 3. 接口安全
- 参数验证
- 频率限制
- 日志记录

## 监控运维

### 1. 日志管理
- 使用Logback记录日志
- 分级日志配置
- 日志文件轮转

### 2. 健康检查
- 集成Spring Boot Actuator
- 数据库连接检查
- 服务状态监控

### 3. 性能监控
- 接口响应时间监控
- 数据库性能监控
- 系统资源监控 